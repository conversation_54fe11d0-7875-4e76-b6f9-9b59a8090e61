#!/bin/bash

export DATABASE_NAME=myaccount_dev_db
export DATABASE_USER=[username]
export DATABASE_PASSWORD=[password]
export DATABASE_HOST=rds-postgres-stage.cpd3dmaosiyq.us-east-1.rds.amazonaws.com

# Declare empty Discord Webhook to avoid reporting errors in dev
export DISCORD_WEBHOOK=

export DEBUG=True
export ENVIRONMENT=development
export SUSPEND_SIGNALS=true
export PYTHONPATH="${PYTHONPATH}:${PWD}/myaccount/"

# TODO geração do pode demorar um bocado
python myaccount/manage.py generate_swagger -o myaccount/swagger.json

python myaccount/manage.py runserver 0.0.0.0:8000
