#!/bin/bash

export AWS_BUCKET_NAME=keeps.myaccount.media.prd
export AWS_MAIL_SENDER="Plataforma Aprendizagem <<EMAIL>>"
export BROKER_URL="amqp://admin:admin@localhost:5672"
export CELERY_BROKER_URL="amqp://admin:admin@localhost:5672"
export CELERY_QUEUE=myaccount
export CELERY_RESULT_BACKEND="amqp://admin:admin@localhost:5672"
export CELERY_RESULT_SERIALIZER=json
export CELERY_TIMEZONE=America/Sao_Paulo
export DATABASE_GAMEUP_NAME=gameup_db
export DATABASE_HOST="keeps-learn-platform-us-east.cpd3dmaosiyq.us-east-1.rds.amazonaws.com"
export DATABASE_KEYCLOAK_NAME=keycloak_db
export DATABASE_KONQUEST_NAME=konquest_db
export DATABASE_NAME=myaccount_db
export DATABASE_PASSWORD=5WsHV7cw2JrvQmqVerB3YwfxXxCXTCX2T3kjmmrsw29JBjWLHP
export DATABASE_PORT=5432
export DATABASE_SMARTZAP_NAME=smartzap_db
export DATABASE_USER=postgres
export DEBOUNCE_API_KEY=60d49f73e0093
export DEBOUNCE_API_URL="https://api.debounce.io/v1/"
export DISCORD_WEBHOOK="https://discord.com/api/webhooks/1021831362015137912/lVVFj3rRLnXxHWn5U1hGwrU1TLNVePFGstbSozvpCitpih34dawsFlZzGx1lfsnUPBye"
export ELASTIC_APM_ENVIRONMENT=production
export ELASTIC_APM_SECRET_TOKEN=UGvFOr8Rghs1uKtXx8
export ENVIRONMENT=production
export IAM_ADMIN_PASS_ADMIN=learnpl4tf0rm
export IAM_ADMIN_REALM=keeps
export IAM_ADMIN_SERVER_URL="https://iam.keepsdev.com/auth/"
export IAM_ADMIN_USER_ADMIN=<EMAIL>
export IAM_OPENID_CLIENT_ID=myaccount
export IAM_OPENID_PUBLIC_KEY="MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4snhFhwQBLWHK4q1lj66Tp6WbS2QJitWz8CCiN3X2Z3lIeCkLNCZSqpbrYTq9p/HJyuSB1GPKYLeMVa+cAXi2MOvjr80+2ngCPKXX9LDNz56PG7OI5OXU5gqOuE3GdqDIe+Mf6GyOhsUfF1R9brfLrRnUcYu29BVpik4mRT0QS0R4ml3V6PHA4fDk7zuMpkgfN4A5yy3CjUfQ+RLT7ynnO9EDaxbYpnIKvdK09JYacxsGZdeStKoj+adm0WAaL+etfy/MMsuuY6S1ey27wUe81/xY9GtR7h9KcPOJA5cKZLwA6Fhqtfz+buTlcmJg/pAI+o2Y0a8G3BxeLkrWWmU8QIDAQAB"
export IAM_OPENID_REALM=keeps
export IAM_OPENID_SECRET_KEY=efe89a2a-07cc-436c-9d41-dc1fb72c8f6b
export IAM_OPENID_SERVER_URL="https://iam.keepsdev.com/auth/"
export IAM_OPENID_TOKEN_URL="/protocol/openid-connect/token"
export KEYCLOAK_CLIENT_ID=myaccount
export KEYCLOAK_CLIENT_PUBLIC_KEY="MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtmbT6XgN9buXZMF6EAjHRNmfGaarVUbik+KtON1YBp0KgTdJn0lCtxhCq5DtvFLOOBrDccw6/RDjeSWlGEcgqggwjPnDIgdtAtgP83mofiILj0mQZfap3o/WknpR6LlveEeM09jnszCTy+WKGZ+HkOJRnhOo/JR7cKSmQxKjEI6NCCNzC4CkwUrPnE5nBhLDk5MvU9KLsBy1TyXoi7c5sh66SLJwD5yn2EzsIxxxje4fYOsXEf6PD6vsAHvjNQNlFELapQTomCECkAwg0fsDZlGAbyK+FoXt/jquKFZ/rcutoXu5tVVNNSfvs5Q4W931KAzqLozd/grR9HlSZAvVWwIDAQAB"
export KEYCLOAK_CLIENT_SECRET_KEY=efe89a2a-07cc-436c-9d41-dc1fb72c8f6b
export KEYCLOAK_REALM=keeps
export KIZUP_API_URL="https://kizup-api.keepsdev.com/api/1/"
export KIZUP_ID=85d8e4b9-9582-4c98-926d-9322e40896db
export KONQUEST_API_URL="http://konquest-svc:8000/"
export KONQUEST_ID=0abf08ea-d252-4d7c-ab45-ab3f9135c288
export KONQUEST_WEB_URL="https://konquest.keepsdev.com"
export MESSAGEBIRD_ACCESS_KEY=*************************
export MESSAGEBIRD_CHANNEL_ID=442e50bac00848928b4a7f595ea6990c
export MESSAGEBIRD_NAMESPACE=6dadf5f1_35e6_4865_a6ff_be3535f8e460
export MYACCOUNT_WEB_URL="https://account.keepsdev.com"
export NOTIFICATION_API_URL="https://learning-platform-api.keepsdev.com/notification"
export QUEUE_GAMEUP_COMPANY=gameup-companies
export QUEUE_GAMEUP_USER=gameup-users
export QUEUE_KONQUEST_COMPANY=konquest-companies
export QUEUE_KONQUEST_USER=konquest-users
export QUEUE_SMARTZAP_COMPANY=smartzap-companies
export QUEUE_SMARTZAP_USER=smartzap-users
export SLACK_LOG_CHANNEL_WEBHOOK="*****************************************************************************"
export SMARTZAP_WEB_URL="http://smartzap.keepsdev.com"
export TWILIO_ACCOUNT_SID=ACb530962b6a3fdea6990e63792eae0a8000
export TWILIO_AUTH_TOKEN=3ce181845e6faa49685b7cdb81fe18a1
export TWILIO_SUPPORT_PHONE_NUMBER=+***********
export WHATSAPP_BROKER=messagebird
export PYTHONPATH="${PYTHONPATH}:${PWD}/myaccount/"

# TODO geração do pode demorar um bocado
python script/import_users_by_xlsx.py