import json
from dataclasses import dataclass
from typing import List, Optional, Tuple

import confluent_kafka as kafka


@dataclass
class KeepsMessage:
    topic: str
    m_id: str
    value: str
    payload: dict
    size: int
    source: kafka.Message
    headers: Optional[List[Tuple[str, bytes]]]

    def __init__(self, message: kafka.Message) -> None:
        self.source = message
        self.size = len(message)
        self.topic = message.topic()

        key = message.key()
        value = message.value()

        self.m_id = key.decode("utf-8") if key else None
        self.value = value.decode("utf-8") if value else None
        try:
            self.payload = json.loads(self.value)
        except Exception as error:
            print(f"ERROR: {error}")
            print(json.loads(self.value))
        self.headers = message.headers() or []

    def get_header(self, key: str) -> Optional[bytes]:
        """Retrieve the value of a specific header key."""
        if self.headers:
            for header_key, header_value in self.headers:
                if header_key == key:
                    return header_value
        return None

    def __str__(self) -> str:
        return '[%s] "%s"="%s"' % (self.topic, self.m_id, self.value[:30] if self.value else None)
