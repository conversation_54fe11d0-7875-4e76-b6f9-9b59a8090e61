import uuid
from datetime import datetime, timedelta

import pandas
from common.models import KafkaUserIntegrationLog
from config.settings import AWS_KAFKA_INTEGRATION_BUCKET, TEMP_UPLOAD_FOLDER
from custom.discord_webhook_logger import DiscordWebhookLogger
from django.db.models import QuerySet
from pytz import timezone
from utils.aws import S3Client


class ErrorReportGeneratorService:
    def __init__(self, s3_client: S3Client, interval_hours=24):
        self._s3_client = s3_client
        self._interval_hours = interval_hours
        self.discord_webhook = DiscordWebhookLogger()
        pass

    def generate_reports(self):
        now = datetime.now(tz=timezone("UTC"))
        some_time_before = now - timedelta(hours=self._interval_hours)

        logs = KafkaUserIntegrationLog.objects.filter(created_date__gte=some_time_before, created_date__lt=now)
        workspace_ids = logs.values_list("workspace_id", flat=True).distinct()
        for workspace_id in workspace_ids:
            try:
                self._generate_workspace_report(logs, workspace_id)
            except Exception as error:
                self.discord_webhook.emit_short_message(
                    "Kafka-Worker Error Report Generator | Unable to generate Kafka Workspace Report", error
                )

    def _generate_workspace_report(self, logs: QuerySet, workspace_id: str) -> None:
        logs = logs.filter(workspace_id=workspace_id)
        logs_representation = []
        for log in logs:
            logs_representation.append(
                {
                    "log_id": log.id,
                    "title": log.title,
                    "row_read": log.message_read,
                    "file_read": log.batch_name,
                    "error_date": log.created_date,
                }
            )
        df = pandas.DataFrame(logs_representation)
        file_name = f"INTEGRATION_ERROR_LOG-{uuid.uuid4()}.csv"
        file_path = f"{TEMP_UPLOAD_FOLDER}/{file_name}"
        df.to_csv(file_path, index=False)
        destiny_path = f"{workspace_id}/user/errors/{file_name}"
        s3_client = self._s3_client
        s3_client.send_file_path(file_path, destiny_path, "text/csv", AWS_KAFKA_INTEGRATION_BUCKET)
