from datetime import datetime
from typing import List

import confluent_kafka as kafka
import elasticapm
from config import settings

from kafka_worker.keeps_message import KeepsMessage
from kafka_worker.user_integration_processor import UserIntegrationProcessor
from kafka_worker.workspace_user_topics import WorkspaceUserTopic


# pylint: disable=too-many-instance-attributes
class UserIntegrationWorker:
    running = True

    def __init__(
        self,
        config: dict,
        consumer: kafka.Consumer,
        integration_processor: UserIntegrationProcessor,
        apm_client: elasticapm.Client = None,
        workspace_user_topics: List[WorkspaceUserTopic] = None,
        poll_timeout: int = None,
    ):
        self.config = config
        self.consumer = consumer
        self._workspace_user_topics = workspace_user_topics
        self._topics = [topic.topic_key for topic in workspace_user_topics]
        self.poll_timeout = poll_timeout if poll_timeout else settings.KAFKA_WORKER_POLL_TIMEOUT
        self.apm_client = apm_client
        self._integration_processor = integration_processor
        self._log_especial_values()

    def _on_assign_callback(self, consumer, partitions):
        consumer.assign(partitions)

        self._log("*** Topics assignments:")
        for partition in partitions:
            low_mark, high_mark = consumer.get_watermark_offsets(partition)
            self._log(f" - {partition.topic} : {partition.offset} / low: {low_mark} / high: {high_mark}")

    def start(self):
        if not self._topics:
            self._log("*******************************************************")
            self._log("*** Any topics assigned to Kafka Worker, skipping worker... ***")
            return
        self.consumer.subscribe(self._topics, on_assign=self._on_assign_callback)
        self._log("*******************************************************")
        self._log(f'*** Connecting to broker: {self.config["bootstrap.servers"]}')
        self._log("*** Listening to topics:")
        for topic in self._topics:
            self._log(f" - {topic}")
        self._log("")
        while self.running:
            try:
                message = self.consumer.poll(timeout=self.poll_timeout)
                if not message:
                    continue
                if message.error():
                    e = f"[ERROR] Message: {message.error()}"
                    self._log(e)
                    self.apm_client.capture_message(e)
                    continue

                keeps_message = KeepsMessage(message)
                self._process_message(keeps_message)
            except Exception as error:
                self._log(f"error: {error}")
                self._log("*** Terminating...")
                self.consumer.close()
                self.running = False

    def _process_message(self, message: KeepsMessage):
        self.apm_client.begin_transaction(transaction_type="batch_processing")
        workspace_user_topics = [_topic for _topic in self._workspace_user_topics if _topic.topic_key == message.topic]
        for workspace_user_topic in workspace_user_topics:
            self._integration_processor.process(
                message, workspace_user_topic.workspace_id, workspace_user_topic.role_id
            )
        self.apm_client.end_transaction(name=__name__, result="success")

    def _log_especial_values(self):
        self._log("*** Special Values:")
        self._log(f"- OFFSET_BEGINNING: {kafka.OFFSET_BEGINNING}")
        self._log(f"- OFFSET_END: {kafka.OFFSET_END}")
        self._log(f"- OFFSET_INVALID: {kafka.OFFSET_INVALID}")
        self._log(f"- OFFSET_STORED: {kafka.OFFSET_STORED}")
        self._log(f"- TIMESTAMP_NOT_AVAILABLE: {kafka.TIMESTAMP_NOT_AVAILABLE}")
        self._log(f"- TIMESTAMP_CREATE_TIME: {kafka.TIMESTAMP_CREATE_TIME}")
        self._log(f"- TIMESTAMP_LOG_APPEND_TIME: {kafka.TIMESTAMP_LOG_APPEND_TIME}\n")

    @staticmethod
    def _log(msg):
        print(f"[{datetime.now()}] {msg}")
