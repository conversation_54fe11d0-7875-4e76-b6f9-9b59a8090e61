import uuid
from unittest.mock import MagicMock, patch
from django.test import TestCase
from model_mommy import mommy

from common.models import KafkaUserIntegrationLog
from kafka_worker.error_report_generator.error_report_generator_service import ErrorReportGeneratorService
from utils.aws import S3Client


class TestErrorReportGeneratorService(TestCase):
    def setUp(self):
        self.s3_client = MagicMock(spec=S3Client)
        self.service = ErrorReportGeneratorService(self.s3_client)
        self.logs = []
        for _ in range(5):
            self.logs.append(mommy.make(KafkaUserIntegrationLog))

    @patch('kafka_worker.error_report_generator.error_report_generator_service.DiscordWebhookLogger.emit_short_message')
    def test_generate_reports_failure(self, mock_emit_short_message):
        exception = Exception("Test Exception")
        self.service._generate_workspace_report = MagicMock(side_effect=exception)

        self.service.generate_reports()

        mock_emit_short_message.assert_called_with(
            "Kafka-Worker Error Report Generator | Unable to generate Kafka Workspace Report",
            exception
        )

    @patch('kafka_worker.error_report_generator.error_report_generator_service.pandas.DataFrame.to_csv')
    @patch('kafka_worker.error_report_generator.error_report_generator_service.KafkaUserIntegrationLog.objects.filter')
    def test_generate_workspace_report(self, mock_filter, mock_to_csv):
        # Given
        workspace_id = uuid.uuid4()
        logs = KafkaUserIntegrationLog.objects.filter(workspace_id=workspace_id)

        # When
        self.service._generate_workspace_report(logs, workspace_id)

        # Then
        self.assertTrue(mock_filter.called)
        mock_to_csv.assert_called()
        self.s3_client.send_file_path.assert_called()
