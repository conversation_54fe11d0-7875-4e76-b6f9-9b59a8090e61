from unittest.mock import MagicMock, patch

from django.test import TestCase
from model_mommy import mommy

from common.models import Workspace
from kafka_worker.keeps_message import KeepsMessage
from kafka_worker.user_grpc_client import BatchResultDto, UserServiceGrpcClient
from kafka_worker.user_integration_processor import UserIntegrationProcessor

from config import settings


class TestUserIntegrationProcessor(TestCase):
    def setUp(self):
        self.mock_grpc_client = MagicMock(spec=UserServiceGrpcClient)
        self.processor = UserIntegrationProcessor(MagicMock(), self.mock_grpc_client, MagicMock())

    @patch("kafka_worker.user_integration_processor.UserIntegrationMessageHash.objects.filter")
    @patch("common.models.UserIntegrationMessageHash.objects.update_or_create")
    def test_should_import_user(self, mock_integration_message_save, mock_filter):
        # Given
        user_data = {"email": "<EMAIL>", "name": "Tester"}
        mock_filter.return_value.exists.return_value = False
        self.processor._account_service.parser_kafka_message.return_value = user_data
        mock_message = MagicMock(spec=KeepsMessage)
        mock_message.payload = user_data
        workspace = mommy.make(Workspace)

        mock_grpc_result = MagicMock(spec=BatchResultDto)
        mock_grpc_result.error = None
        self.mock_grpc_client.create_users.return_value = [mock_grpc_result ]

        # When
        self.processor.process(mock_message, workspace.id, settings.KONQUEST_DEFAULT_ROLE)

        # Then
        self.mock_grpc_client.create_users.assert_called_with(
            users_data=[user_data], permissions=[settings.KONQUEST_DEFAULT_ROLE], workspace_id=workspace.id
        )
        mock_integration_message_save.assert_called()

    @patch("kafka_worker.user_integration_processor.UserIntegrationMessageHash.objects.filter")
    def test_should_ignore_message_already_processed(self, mock_filter):
        # Given
        user_data = {"email": "<EMAIL>", "name": "Tester"}
        mock_filter.return_value.exists.return_value = True
        mock_message = MagicMock(spec=KeepsMessage)
        mock_message.payload = user_data
        workspace = mommy.make(Workspace)

        # When
        self.processor.process(mock_message, workspace.id, settings.KONQUEST_DEFAULT_ROLE)

        # Then
        self.processor._account_service.import_user.assert_not_called()
