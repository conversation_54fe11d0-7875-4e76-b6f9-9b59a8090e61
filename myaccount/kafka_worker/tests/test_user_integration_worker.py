from unittest.mock import MagicMock

from django.test import TestCase
from kafka_worker.user_integration_worker import UserIntegrationWorker
from kafka_worker.workspace_user_topics import WorkspaceUserTopic
from mock.mock import PropertyMock


class TestUserIntegrationWorker(TestCase):
    def setUp(self):
        consumer = MagicMock()
        self.workspace_id = "2334"
        self.topic = "topic_test"
        self.workspace_topics = [WorkspaceUserTopic(self.workspace_id, self.topic)]
        self.worker = UserIntegrationWorker(
            config={"bootstrap.servers": []},
            consumer=consumer,
            integration_processor=MagicMock(),
            apm_client=MagicMock(),
            workspace_user_topics=self.workspace_topics,
            poll_timeout=1,
        )

    def test_should_process_message(self):
        # Given
        mock_message = MagicMock()
        mock_message.error.return_value = False
        mock_message.value.return_value = '{"payload":{"name":"<PERSON>", "age":30, "car":null}}'.encode()
        mock_message.topic.return_value = self.topic
        self.worker.consumer.poll.return_value = mock_message
        self.worker.apm_client.end_transaction = PropertyMock(side_effect=ValueError("Stop"))

        # When
        self.worker.start()

        # Then
        self.worker._integration_processor.process.assert_called_once()

    def test_should_ignore_messages_with_error(self):
        # Given
        mock_message = MagicMock()
        mock_message.error.return_value = True
        self.worker.apm_client.capture_message = PropertyMock(side_effect=ValueError("Stop"))

        # When
        self.worker.start()

        # Then
        self.worker._integration_processor.process.assert_not_called()
