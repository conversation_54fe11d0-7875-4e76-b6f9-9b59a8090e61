import hashlib
import json
import traceback
from datetime import datetime
from typing import Optional

from common.models import UserIntegrationMessageHash
from common.models.kafka_user_integration_log import KafkaUserIntegrationLog
from common.services.import_user_service import ImportUserService
from config import settings
from custom.discord_webhook_logger import DiscordWebhookLogger
from django.db import OperationalError, connection
from django.db import utils as db_utils

from kafka_worker.keeps_message import KeepsMessage
from kafka_worker.user_grpc_client import UserServiceGrpcClient

ERROR_TO_SAVE_USER_LOG_TITLE = "Kakfa-Worker User Integration Processor | Error to save user"


class UserIntegrationProcessor:
    def __init__(
        self,
        account_service: ImportUserService,
        user_service_grpc_client: UserServiceGrpcClient,
        logger: DiscordWebhookLogger
    ):
        self._account_service = account_service
        self._logger = logger
        self._user_service_grpc_client = user_service_grpc_client
        self._max_db_connection_retries = 2
        self._current_db_connection_retry_attempt = 0
        self._log_error_description_length = 1024

    @staticmethod
    def _hash(message: dict):
        dhash = hashlib.md5()
        encoded = json.dumps(message, sort_keys=True, default=str).encode()
        dhash.update(encoded)
        return dhash.hexdigest()

    @staticmethod
    def _is_known_hash(message_hash: str, user_email: str):
        return UserIntegrationMessageHash.objects.filter(hash=message_hash, user_email=user_email).exists()

    def _save_log_error(self, message: KeepsMessage, workspace_id: str, error: Exception):
        description = f"{traceback.format_exc()}: {error}"
        if len(description) > self._log_error_description_length:
            description = (
                description[: self._log_error_description_length]
                + " ... "
                + description[-self._log_error_description_length:]
            )

        batch_name = message.get_header("connect.file.name")
        if batch_name:
            batch_name = batch_name.decode("utf-8")

        try:
            message_read = json.dumps(message.payload, default=str)
        except Exception:
            message_read = None

        title = ERROR_TO_SAVE_USER_LOG_TITLE.upper()

        KafkaUserIntegrationLog(
            title=title,
            description=description,
            batch_name=batch_name,
            workspace_id=workspace_id,
            message_read=message_read,
            is_error=True,
        ).save()

    def process(self, message: KeepsMessage, workspace_id: str, role_id: str):
        try:
            self._save_user(message, workspace_id, role_id)
        except (db_utils.InterfaceError, OperationalError) as error:
            print("retring message", error)
            connection.connect()
            self._current_db_connection_retry_attempt += 1
            if self._current_db_connection_retry_attempt <= self._max_db_connection_retries:
                self.process(message, workspace_id, role_id)
                return
            self._current_db_connection_retry_attempt = 0
            self._log(ERROR_TO_SAVE_USER_LOG_TITLE, error)
            self._save_log_error(message, workspace_id, error)
            return
        except Exception as error:
            self._log(ERROR_TO_SAVE_USER_LOG_TITLE, error)
            self._save_log_error(message, workspace_id, error)

    def _save_user(self, message: KeepsMessage, workspace_id: str, default_role_id: str):
        user_payload = message.payload.get("payload")
        if not user_payload:
            user_payload = message.payload
        message_hash = self._hash({"payload": user_payload, "workspace_id": workspace_id})
        user = self._account_service.parser_kafka_message(user_payload)
        user_email = user["email"]

        if self._is_known_hash(message_hash, user_email):
            return

        default_role_id = default_role_id or settings.KONQUEST_DEFAULT_ROLE

        results = self._user_service_grpc_client.create_users(
            users_data=[user],
            permissions=[default_role_id],
            workspace_id=workspace_id
        )
        result = results[0] if results else {}

        if result.error:
            raise Exception(result.error)

        self._log(f"User created/updated {result}")
        try:
            self._save_new_hash(message_hash, workspace_id, user_email)
        except Exception as error:
            self._log("Kakfa-Worker User Integration Processor | Error to save message hash", error)

    @staticmethod
    def _save_new_hash(message_hash: str, workspace_id: str, user_email: str):
        UserIntegrationMessageHash.objects.update_or_create(
            workspace_id=workspace_id, user_email=user_email, defaults={"hash": message_hash}
        )

    def _log(self, message: str, error: Optional[Exception] = None):
        if error:
            self._logger.emit_short_message(message, error)
        print(f"[{datetime.now()}] {message}")
