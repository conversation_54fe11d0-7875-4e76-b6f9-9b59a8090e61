from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from config import settings
from grpc_helpers.client_base import GrpcClientBase
from grpc_services.generated import users_pb2, users_pb2_grpc


@dataclass
class BatchResultDto:
    email: str
    error: Optional[str] = None
    id: Optional[str] = None


class UserServiceGrpcClient(GrpcClientBase):
    def __init__(self, host: str = settings.GRPC_SERVER):
        super().__init__(host, users_pb2_grpc.UserServiceStub)

    def _set_required_fields(self, user, data: Dict[str, Any]):
        """Set required user fields from data."""
        user.email = data["email"]
        user.name = data["name"]
        user.time_zone = data.get("time_zone", "America/Sao_paulo")

    def _set_optional_fields(self, user, data: Dict[str, Any]):
        """Set optional string/numeric fields if present in data."""
        optional_fields = [
            "ein",
            "password",
            "additional_data",
            "nickname",
            "secondary_email",
            "phone",
            "gender",
            "birthday",
            "address",
            "avatar",
            "language_id",
            "country",
            "related_user_leader_id",
            "related_user_leader",
            "admission_date",
            "contract_type",
            "cpf",
            "education",
            "ethnicity",
            "job",
            "hierarchical_level",
            "marital_status",
        ]

        for field in optional_fields:
            if field in data:
                setattr(user, field, data[field])

    def _set_boolean_fields(self, user, data: Dict[str, Any]):
        """Set boolean fields if present in data."""
        boolean_fields = ["status", "is_user_integration", "email_verified"]

        for field in boolean_fields:
            if field in data:
                setattr(user, field, data[field])

    def _set_profile_fields(self, user, data: Dict[str, Any]):
        """Set profile fields if profile data is present."""
        if "profile" not in data:
            return

        user.profile.CopyFrom(users_pb2.EmployeeInfoCreate())
        profile = data["profile"]
        profile_fields = [
            "id",
            "job_function",
            "job_function",
            "director",
            "manager",
            "area_of_activity",
            "user_id",
            "workspace_id",
        ]

        for field in profile_fields:
            if field in profile:
                setattr(user.profile, field, profile[field])

    def create_users(
        self, users_data: List[Dict[str, Any]], permissions: List[str], workspace_id: str
    ) -> List[BatchResultDto]:
        """Create multiple users with the given data and permissions.

        Args:
            users_data: List of dictionaries containing user information
            permissions: List of permission strings to apply to all users
            workspace_id: Workspace ID

        Returns:
            List of created user objects
        """
        request = users_pb2.CreateUsersRequest()
        request.permissions.extend(permissions)

        for data in users_data:
            user = request.users.add()

            # Set different types of fields using helper methods
            self._set_required_fields(user, data)
            self._set_optional_fields(user, data)
            self._set_boolean_fields(user, data)
            self._set_profile_fields(user, data)

        metadata = [("x-client", workspace_id)] if workspace_id else []

        response = self._call_with_retry(self.stub.CreateUser, request, metadata=metadata)

        return response.users

    def close(self):
        self.channel.close()
