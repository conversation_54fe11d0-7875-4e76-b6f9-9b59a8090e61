from rest_framework import views
from rest_framework.permissions import AllowAny
from rest_framework.response import Response

from authentication.services.authentication_service import AuthenticationService


class LoginView(views.APIView):
    permission_classes = (AllowAny,)

    def __init__(self):
        super().__init__()
        self._service = AuthenticationService()

    def post(self, request):
        response, status = self._service.login(request.data["username"], request.data["password"])
        return Response(response, status=status)
