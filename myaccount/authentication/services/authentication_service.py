# -*- coding: utf-8 -*-
import requests
from django.conf import settings
from rest_framework.exceptions import APIException
from utils.keycloak.user_manager import KeyCloakUserManager


class AuthenticationService:
    @staticmethod
    def login(username, password):
        data_login = {
            "username": username,
            "password": password,
            "client_secret": settings.IAM_OPENID_SECRET_KEY,
            "client_id": settings.IAM_OPENID_CLIENT_ID,
            "grant_type": "password",
        }

        url = settings.IAM_OPENID_SERVER_URL + "realms/" + settings.IAM_OPENID_REALM + settings.IAM_OPENID_TOKEN_URL
        response = requests.post(url, data=data_login)
        return response.json(), response.status_code

    @staticmethod
    def reset_password(email):
        user_task = KeyCloakUserManager()
        user_id = user_task.get_user_id_by_email(email)

        if not user_id:
            raise APIException("User not found.", code=404)

        user = user_task.get_user(user_id)
        user_task.send_reset_password(user_id=user_id)

        return user

    @staticmethod
    def change_password(email, password):
        user_task = KeyCloakUserManager()
        user_id = user_task.get_user_id_by_email(email)

        if not user_id:
            raise APIException("User not found.", code=404)

        return user_task.set_user_password(user_id=user_id, password=password, temporary=False)
