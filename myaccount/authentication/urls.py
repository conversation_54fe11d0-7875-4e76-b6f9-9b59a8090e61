from django.urls import path

from authentication.views.authentication_viewset import <PERSON>ginView
from authentication.views.reset_password_viewset import ResetPasswordView

_READ_ONLY = {"get": "list"}

_LIST = {"get": "list", "post": "create"}

_DETAIL = {"get": "retrieve", "put": "update", "patch": "partial_update", "delete": "destroy"}


urlpatterns = [
    path("", LoginView.as_view(), name="login"),
    path("/reset-password", ResetPasswordView.as_view(), name="reset-password"),
]
