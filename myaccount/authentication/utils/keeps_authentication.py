from django.conf import settings
from jose import ExpiredSignatureError
from keycloak import KeycloakOpenID
from rest_framework import authentication, permissions
from rest_framework.exceptions import APIException


class KeepsAuthentication(authentication.TokenAuthentication):
    def __init__(self):
        self._config = getattr(settings, "KEYCLOAK_CONFIG")

        # Read configurations
        try:
            self._server_url = self._config["KEYCLOAK_SERVER_URL"]
            self._client_id = self._config["KEYCLOAK_CLIENT_ID"]
            self._realm = self._config["KEYCLOAK_REALM"]
        except KeyError as exc:
            raise Exception("KEYCLOAK_SERVER_URL, <PERSON><PERSON><PERSON><PERSON>OA<PERSON>_CLIENT_ID or <PERSON><PERSON><PERSON><PERSON><PERSON>K_REALM not found.") from exc

        self._client_secret_key = self._config.get("KEYCLOAK_CLIENT_SECRET_KEY", None)
        self._client_public_key = self._config.get("<PERSON><PERSON><PERSON><PERSON>OAK_CLIENT_PUBLIC_KEY", None)

        # Create Keycloak instance
        self._keycloak = KeycloakOpenID(
            server_url=self._server_url,
            client_id=self._client_id,
            realm_name=self._realm,
            client_secret_key=self._client_secret_key,
            verify=False,
        )

    def authenticate(self, request):
        # Decode token and set request.user

        if self._skip_urls(request):
            return None, None

        if self._allow_public_access(request):
            return None, None

        user = self._get_token_info(request)
        user["client_id"] = self._get_profile(request)

        return user, None

    def _allow_public_access(self, request):
        # Verifica se a view associada à solicitação tem a permissão AllowAny
        view = getattr(request, "resolver_match", None)
        if view and hasattr(view.func, "cls"):
            view_class = view.func.cls
            if hasattr(view_class, "permission_classes"):
                return any(
                    permission_class == permissions.AllowAny for permission_class in view_class.permission_classes
                )
        return False

    def _get_token_info(self, request):
        """Decode token"""

        # Options check token
        options = {"verify_signature": True, "verify_aud": False, "exp": True}

        # JWT not found
        if "HTTP_AUTHORIZATION" not in request.META:
            raise APIException("HTTP_AUTHORIZATION not found in the request")

        jwt = request.META.get("HTTP_AUTHORIZATION")

        if jwt == settings.KEEPS_SECRET_TOKEN_INTEGRATION:
            return {"token_integration": jwt}

        try:
            token_info = self._keycloak.decode_token(jwt, key=self._client_public_key, options=options)

        except ExpiredSignatureError as exc:
            options["verify_exp"] = False
            token_info = self._keycloak.decode_token(jwt, key=self._client_public_key, options=options)

            if "offline_access" in token_info["realm_access"]["roles"]:
                return token_info

            raise ExpiredSignatureError("Login expired") from exc

        return token_info

    @staticmethod
    def _get_profile(request):
        return request.META.get("HTTP_X_CLIENT") or None

    # pylint: disable=R0916
    @staticmethod
    def _skip_urls(request):
        if (
            "/docs" in request.path
            or "/swagger" in request.path
            or "/redoc" in request.path
            or "/auth" in request.path
            or "/health-check" in request.path
            or "/sign-up" in request.path
        ):
            return True
        return False


class KeepsIsAuthenticatedPermission(permissions.BasePermission):
    def __init__(self):
        pass

    # pylint: disable=R1710
    def has_permission(self, request, view):
        return request.user is not None


class IsNotAuthenticated(permissions.BasePermission):
    def has_permission(self, request, view):
        return True
