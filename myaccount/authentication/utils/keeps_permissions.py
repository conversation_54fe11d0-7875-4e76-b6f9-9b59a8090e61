from typing import Sequence

from common.models import UserRoleWorkspace
from config.settings import MY<PERSON><PERSON>UNT_ID
from custom.keeps_exception_handler import KeepsClientHeaderNotFoundCompanyError, KeepsUnauthorizedError
from django.contrib.auth.models import AnonymousUser
from rest_framework.permissions import BasePermission
from rest_framework.request import Request

USER = "account_admin"
ADMIN = "company_admin"


class KeepsBasePermission(BasePermission):
    @staticmethod
    def _get_priority_user_role(roles: Sequence[str]) -> str:
        if ADMIN in roles:
            return ADMIN
        return USER

    def get_priority_user_role_by_token(self, token: str, workspace_id: str = None) -> str:
        user_roles = self.get_user_roles(token, workspace_id)
        return self._get_priority_user_role(user_roles)

    @staticmethod
    def _check_role(request: Request, role: str) -> bool:
        user = request.user
        if not user or isinstance(user, AnonymousUser):
            raise KeepsUnauthorizedError()
        workspace_id = user.get("client_id")
        if not workspace_id:
            raise KeepsClientHeaderNotFoundCompanyError()

        return UserRoleWorkspace.objects.filter(
            workspace_id=workspace_id, user_id=user["sub"], role__application_id=MYACCOUNT_ID, role__key=role
        ).exists()


class KeepsUserPermission(KeepsBasePermission):
    def has_permission(self, request, view):
        return self._check_role(request, USER)


class KeepsAdminPermission(KeepsBasePermission):
    def has_permission(self, request, view):
        return self._check_role(request, ADMIN)


class KeepsAuthenticated(BasePermission):
    def has_permission(self, request, view):
        return request.user and not isinstance(request.user, AnonymousUser)


# pylint: disable=unsupported-binary-operation
ANY_PERMISSION = (KeepsAdminPermission | KeepsUserPermission,)
