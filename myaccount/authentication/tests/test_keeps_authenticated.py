from unittest import mock

from django.test import TestCase
from rest_framework.request import Request

from authentication.utils.keeps_permissions import KeepsAuthenticated


class TestKeepsAuthenticated(TestCase):
    def setUp(self) -> None:
        pass

    def test_should_has_permission_when_request_contains_user_authenticated(self):
        request = mock.MagicMock(Request)
        request.user.return_value = {"sub": "1234", "email": "<EMAIL>"}
        permission_class = KeepsAuthenticated()
        self.assertTrue(permission_class.has_permission(request, None))
