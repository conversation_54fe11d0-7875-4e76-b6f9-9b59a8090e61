import pytest
from django.test import TestCase
from unittest.mock import <PERSON><PERSON><PERSON>
from django.contrib.auth.models import Anonymous<PERSON>ser
from mock import mock
from rest_framework.request import Request

from authentication.utils.keeps_permissions import KeepsAdminPermission, KeepsUserPermission, USER, ADMIN
from common.models import UserRoleWorkspace
from config.settings import MY<PERSON>COUNT_ID
from custom.keeps_exception_handler import KeepsUnauthorizedError, KeepsClientHeaderNotFoundCompanyError


class TestKeepsPermission(TestCase):
    @mock.patch.object(UserRoleWorkspace, "objects")
    def test_has_permission_user(self, objects: MagicMock):
        permission = KeepsUserPermission()
        request = MagicMock(spec=Request)
        workspace_id = 1
        user_id = 1
        role = USER
        request.user = {"sub": user_id, "client_id": workspace_id}

        objects.filter.return_value.exists.return_value = True

        assert permission.has_permission(request, None)
        objects.filter.assert_called_with(
            workspace_id=workspace_id, user_id=user_id, role__application_id=MY<PERSON>COUNT_ID, role__key=role
        )

    @mock.patch.object(UserRoleWorkspace, "objects")
    def test_has_permission_admin(self, objects: MagicMock):
        permission = KeepsAdminPermission()
        request = MagicMock(spec=Request)
        workspace_id = 1
        user_id = 1
        role = ADMIN
        request.user = {"sub": user_id, "client_id": workspace_id}

        objects.filter.return_value.exists.return_value = True

        assert permission.has_permission(request, None)
        objects.filter.assert_called_with(
            workspace_id=workspace_id, user_id=user_id, role__application_id=MYACCOUNT_ID, role__key=role
        )

    def test_has_permission_anonymous(self):
        permission = KeepsUserPermission()
        request = MagicMock(spec=Request)
        request.user = AnonymousUser()

        with pytest.raises(KeepsUnauthorizedError):
            permission.has_permission(request, None)

    def test_has_permission_no_client_header(self):
        permission = KeepsUserPermission()
        request = MagicMock(spec=Request)
        request.user = {"sub": 1}

        with pytest.raises(KeepsClientHeaderNotFoundCompanyError):
            permission.has_permission(request, None)
