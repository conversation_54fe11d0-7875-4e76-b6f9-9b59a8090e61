"""
Scheduler configured to run with supervisor

See .ebextensions/99.celery.config
"""
import datetime
import os

import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from apscheduler.schedulers.blocking import BlockingScheduler
from config.settings import AWS_KAFKA_INTEGRATION_ERROR_REPORT_GENERATOR_HOURS_INTERVAL
from kafka_worker.error_report_generator.error_report_generator_service import ErrorReportGeneratorService
from tasks import scheduler  # noqa: E402
from utils.task_transaction import task_transaction


def health_check():
    print(f"{datetime.datetime.now()} Schedulers alive")


def generate_kafka_integration_error_report():
    with task_transaction("KAFKA WORKER ERROR REPORT GENERATOR") as container:
        ErrorReportGeneratorService(container.aws_s3_client, 24).generate_reports()


if __name__ == "__main__":
    print("Running schedulers...")
    scheduler.start_background_schedulers()
    LOCK_SCHEDULER = BlockingScheduler(timezone="America/Sao_Paulo")
    LOCK_SCHEDULER.add_job(health_check, trigger="interval", minutes=60)
    LOCK_SCHEDULER.add_job(
        generate_kafka_integration_error_report,
        trigger="interval",
        minutes=AWS_KAFKA_INTEGRATION_ERROR_REPORT_GENERATOR_HOURS_INTERVAL,
    )
    LOCK_SCHEDULER.start()
