# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: users.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'users.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0busers.proto\x12\x05users\"I\n\x12\x43reateUsersRequest\x12\x1e\n\x05users\x18\x01 \x03(\x0b\x32\x0f.users.UserData\x12\x13\n\x0bpermissions\x18\x02 \x03(\t\";\n\x13\x43reateUsersResponse\x12$\n\x05users\x18\x01 \x03(\x0b\x32\x15.users.BatchResultDto\"U\n\x0e\x42\x61tchResultDto\x12\r\n\x05\x65mail\x18\x01 \x01(\t\x12\x12\n\x05\x65rror\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x0f\n\x02id\x18\x03 \x01(\tH\x01\x88\x01\x01\x42\x08\n\x06_errorB\x05\n\x03_id\"\xd9\t\n\x08UserData\x12\r\n\x05\x65mail\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x10\n\x03\x65in\x18\x03 \x01(\tH\x00\x88\x01\x01\x12\x10\n\x03job\x18\x04 \x01(\tH\x01\x88\x01\x01\x12\x15\n\x08password\x18\x05 \x01(\tH\x02\x88\x01\x01\x12\x37\n\x11identity_provider\x18\x06 \x01(\x0b\x32\x17.users.IdentityProviderH\x03\x88\x01\x01\x12/\n\x07profile\x18\x07 \x01(\x0b\x32\x19.users.EmployeeInfoCreateH\x04\x88\x01\x01\x12\x1c\n\x0f\x61\x64\x64itional_data\x18\x08 \x01(\tH\x05\x88\x01\x01\x12\x15\n\x08nickname\x18\t \x01(\tH\x06\x88\x01\x01\x12\x1c\n\x0fsecondary_email\x18\n \x01(\tH\x07\x88\x01\x01\x12\x12\n\x05phone\x18\x0b \x01(\tH\x08\x88\x01\x01\x12\x13\n\x06gender\x18\x0c \x01(\tH\t\x88\x01\x01\x12\x15\n\x08\x62irthday\x18\r \x01(\tH\n\x88\x01\x01\x12\x14\n\x07\x61\x64\x64ress\x18\x0e \x01(\tH\x0b\x88\x01\x01\x12\x13\n\x06\x61vatar\x18\x0f \x01(\tH\x0c\x88\x01\x01\x12\x13\n\x06status\x18\x10 \x01(\x08H\r\x88\x01\x01\x12 \n\x13is_user_integration\x18\x11 \x01(\x08H\x0e\x88\x01\x01\x12\x18\n\x0blanguage_id\x18\x12 \x01(\tH\x0f\x88\x01\x01\x12\x14\n\x07\x63ountry\x18\x13 \x01(\tH\x10\x88\x01\x01\x12#\n\x16related_user_leader_id\x18\x14 \x01(\tH\x11\x88\x01\x01\x12 \n\x13related_user_leader\x18\x15 \x01(\tH\x12\x88\x01\x01\x12\x1b\n\x0e\x65mail_verified\x18\x16 \x01(\x08H\x13\x88\x01\x01\x12\x16\n\ttime_zone\x18\x17 \x01(\tH\x14\x88\x01\x01\x12\x1b\n\x0e\x61\x64mission_date\x18\x18 \x01(\tH\x15\x88\x01\x01\x12\x1a\n\rcontract_type\x18\x19 \x01(\tH\x16\x88\x01\x01\x12\x10\n\x03\x63pf\x18\x1a \x01(\tH\x17\x88\x01\x01\x12\x16\n\teducation\x18\x1b \x01(\tH\x18\x88\x01\x01\x12\x16\n\tethnicity\x18\x1c \x01(\tH\x19\x88\x01\x01\x12\x1f\n\x12hierarchical_level\x18\x1d \x01(\tH\x1a\x88\x01\x01\x12\x1b\n\x0emarital_status\x18\x1e \x01(\tH\x1b\x88\x01\x01\x42\x06\n\x04_einB\x06\n\x04_jobB\x0b\n\t_passwordB\x14\n\x12_identity_providerB\n\n\x08_profileB\x12\n\x10_additional_dataB\x0b\n\t_nicknameB\x12\n\x10_secondary_emailB\x08\n\x06_phoneB\t\n\x07_genderB\x0b\n\t_birthdayB\n\n\x08_addressB\t\n\x07_avatarB\t\n\x07_statusB\x16\n\x14_is_user_integrationB\x0e\n\x0c_language_idB\n\n\x08_countryB\x19\n\x17_related_user_leader_idB\x16\n\x14_related_user_leaderB\x11\n\x0f_email_verifiedB\x0c\n\n_time_zoneB\x11\n\x0f_admission_dateB\x10\n\x0e_contract_typeB\x06\n\x04_cpfB\x0c\n\n_educationB\x0c\n\n_ethnicityB\x15\n\x13_hierarchical_levelB\x11\n\x0f_marital_status\"x\n\x10IdentityProvider\x12\x12\n\x05\x61lias\x18\x01 \x01(\tH\x00\x88\x01\x01\x12\x14\n\x07user_id\x18\x02 \x01(\tH\x01\x88\x01\x01\x12\x16\n\tuser_name\x18\x03 \x01(\tH\x02\x88\x01\x01\x42\x08\n\x06_aliasB\n\n\x08_user_idB\x0c\n\n_user_name\"\xd8\x02\n\x12\x45mployeeInfoCreate\x12\x0f\n\x02id\x18\x01 \x01(\tH\x00\x88\x01\x01\x12\x1c\n\x0fjob_function_id\x18\x02 \x01(\tH\x01\x88\x01\x01\x12\x1c\n\x0fjob_position_id\x18\x03 \x01(\tH\x02\x88\x01\x01\x12\x15\n\x08\x64irector\x18\x04 \x01(\tH\x03\x88\x01\x01\x12\x14\n\x07manager\x18\x05 \x01(\tH\x04\x88\x01\x01\x12\x1d\n\x10\x61rea_of_activity\x18\x06 \x01(\tH\x05\x88\x01\x01\x12\x14\n\x07user_id\x18\x07 \x01(\tH\x06\x88\x01\x01\x12\x19\n\x0cworkspace_id\x18\x08 \x01(\tH\x07\x88\x01\x01\x42\x05\n\x03_idB\x12\n\x10_job_function_idB\x12\n\x10_job_position_idB\x0b\n\t_directorB\n\n\x08_managerB\x13\n\x11_area_of_activityB\n\n\x08_user_idB\x0f\n\r_workspace_id\"\xed\x01\n\x0fGetUsersRequest\x12\x0c\n\x04page\x18\x01 \x01(\x05\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06search\x18\x03 \x01(\t\x12\x11\n\tsearch_by\x18\x04 \x03(\t\x12\x0e\n\x06select\x18\x05 \x03(\t\x12\x0f\n\x07sort_by\x18\x06 \x03(\t\x12\x34\n\x07\x66ilters\x18\x07 \x03(\x0b\x32#.users.GetUsersRequest.FiltersEntry\x1a\x43\n\x0c\x46iltersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\"\n\x05value\x18\x02 \x01(\x0b\x32\x13.users.FilterValues:\x02\x38\x01\"\x1e\n\x0c\x46ilterValues\x12\x0e\n\x06values\x18\x01 \x03(\t\"Z\n\x10UserListResponse\x12\x1a\n\x05items\x18\x01 \x03(\x0b\x32\x0b.users.User\x12\r\n\x05total\x18\x02 \x01(\x05\x12\x0c\n\x04page\x18\x03 \x01(\x05\x12\r\n\x05limit\x18\x04 \x01(\x05\"?\n\x04User\x12\n\n\x02id\x18\x01 \x01(\t\x12\r\n\x05\x65mail\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\x08\")\n\x17ResendInvitationRequest\x12\x0e\n\x06userId\x18\x01 \x01(\t\"*\n\x18ResendInvitationResponse\x12\x0e\n\x06status\x18\x01 \x01(\t2\x8a\x01\n\x0bUserService\x12\x36\n\x03Get\x12\x16.users.GetUsersRequest\x1a\x17.users.UserListResponse\x12\x43\n\nCreateUser\x12\x19.users.CreateUsersRequest\x1a\x1a.users.CreateUsersResponse2h\n\x11InvitationService\x12S\n\x10ResendInvitation\x12\x1e.users.ResendInvitationRequest\x1a\x1f.users.ResendInvitationResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'users_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_GETUSERSREQUEST_FILTERSENTRY']._loaded_options = None
  _globals['_GETUSERSREQUEST_FILTERSENTRY']._serialized_options = b'8\001'
  _globals['_CREATEUSERSREQUEST']._serialized_start=22
  _globals['_CREATEUSERSREQUEST']._serialized_end=95
  _globals['_CREATEUSERSRESPONSE']._serialized_start=97
  _globals['_CREATEUSERSRESPONSE']._serialized_end=156
  _globals['_BATCHRESULTDTO']._serialized_start=158
  _globals['_BATCHRESULTDTO']._serialized_end=243
  _globals['_USERDATA']._serialized_start=246
  _globals['_USERDATA']._serialized_end=1487
  _globals['_IDENTITYPROVIDER']._serialized_start=1489
  _globals['_IDENTITYPROVIDER']._serialized_end=1609
  _globals['_EMPLOYEEINFOCREATE']._serialized_start=1612
  _globals['_EMPLOYEEINFOCREATE']._serialized_end=1956
  _globals['_GETUSERSREQUEST']._serialized_start=1959
  _globals['_GETUSERSREQUEST']._serialized_end=2196
  _globals['_GETUSERSREQUEST_FILTERSENTRY']._serialized_start=2129
  _globals['_GETUSERSREQUEST_FILTERSENTRY']._serialized_end=2196
  _globals['_FILTERVALUES']._serialized_start=2198
  _globals['_FILTERVALUES']._serialized_end=2228
  _globals['_USERLISTRESPONSE']._serialized_start=2230
  _globals['_USERLISTRESPONSE']._serialized_end=2320
  _globals['_USER']._serialized_start=2322
  _globals['_USER']._serialized_end=2385
  _globals['_RESENDINVITATIONREQUEST']._serialized_start=2387
  _globals['_RESENDINVITATIONREQUEST']._serialized_end=2428
  _globals['_RESENDINVITATIONRESPONSE']._serialized_start=2430
  _globals['_RESENDINVITATIONRESPONSE']._serialized_end=2472
  _globals['_USERSERVICE']._serialized_start=2475
  _globals['_USERSERVICE']._serialized_end=2613
  _globals['_INVITATIONSERVICE']._serialized_start=2615
  _globals['_INVITATIONSERVICE']._serialized_end=2719
# @@protoc_insertion_point(module_scope)
