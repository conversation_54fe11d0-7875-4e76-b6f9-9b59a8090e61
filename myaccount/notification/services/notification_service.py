from typing import List, Optional

from common.models.user_role_workspace import UserRoleWorkspace
from django.conf import settings
from django.utils.translation import gettext_noop
from tasks.platform_service import PlatformIntegrationService

from notification.dtos.message import Message


class NotificationService:
    def __init__(self):
        self.integration_task = PlatformIntegrationService

    def notify_gamification_ranking_workspace_disabled(self, ranking):
        GAMIFICATION_RANKING_DISABLED = gettext_noop("Gamification ranking disabled")
        DESCRIPTION = gettext_noop(
            "The gamification {} ranking was disabled by service because it did not to reach 70% of the users."
        ).format(ranking.ranking)

        user_ids = UserRoleWorkspace.objects.filter(
            role__application_id=settings.KONQUEST_ID,
            role__key__in=["admin", "company_admin", "super_admin"],
            role__status=True,
        ).values_list("user_id", flat=True)

        type_key = "GAMIFICATION_RANKING_DISABLED"
        action = "CREATE_OR_UPDATE"
        message = Message(title=GAMIFICATION_RANKING_DISABLED, description=DESCRIPTION)
        message.description_values = {"ranking_name": ranking.ranking}

        self._create_notification(
            user_ids=user_ids, workspace_id=ranking.workspace_id, type_key=type_key, action=action, message=message
        )

    def _create_notification(
        self,
        user_ids: List[str],
        workspace_id: str,
        type_key: str,
        action: str,
        message: Message,
        **kwargs,
    ):
        if not isinstance(message, Message):
            raise ValueError(f"{message} is not a valid Message Type")
        self._valid_message(message)
        object_id = kwargs.get("object")
        url = kwargs.get("url")

        for user_id in user_ids:
            data = {
                "user_receiving_id": user_id,
                "type_key": type_key,
                "action": action,
                "messages": message.__dict__,
                "object": object_id,
                "url": url,
                "workspace_id": workspace_id,
            }
            self.integration_task.publish("NOTIFICATION", "CREATE_OR_UPDATE", data)

    @staticmethod
    def _valid_message(message: Message) -> Optional[str]:
        if not message.title_values:
            return None
        try:
            message_formatted = message.title % message.title_values
            return message_formatted
        except KeyError as error:
            raise KeyError(f"invalid message.title_values, invalid key: {error}") from error
