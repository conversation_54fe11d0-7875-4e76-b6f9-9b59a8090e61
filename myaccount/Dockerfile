FROM python:3.9-bullseye

WORKDIR /app

EXPOSE 8000

RUN apt-get update  \
    && apt-get install supervisor -y \
    && apt-get install gcc -y  \
    && apt-get install python-dev -y  \
    && apt-get install g++ -y  \
    && apt-get install build-essential -y  \
    && apt-get install swig -y  \
    && apt-get clean

ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    HOME=/app \
    PATH=/app/.local/bin/:$PATH

ARG arg_revision=''
ARG arg_build_date=''
ENV REVISION=$arg_revision \
    BUILD_DATE=$arg_build_date

COPY . /app
COPY supervisord.conf /etc/supervisord.conf

RUN pip3 install gunicorn && pip install pip==24.0 && pip install -r requirements.txt
RUN chmod ugo+rwx -R /app
RUN chmod ugo+x entrypoint.sh

ENTRYPOINT ["/app/entrypoint.sh"]
