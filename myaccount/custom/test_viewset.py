from django.urls import include, path
from rest_framework.response import Response
from rest_framework.routers import Default<PERSON>out<PERSON>
from rest_framework.viewsets import ViewSet


class TestViewSet(ViewSet):
    def list(self, request):
        return Response({"message": "GET method allowed"})

    def create(self, request):
        return Response({"message": "POST method allowed"})


router = DefaultRouter()
router.register(r"test", TestViewSet, basename="test")

urlpatterns = [
    path("", include(router.urls)),
]
