import logging

from custom.keeps_exception_handler import KeepsError, KeepsPermissionError, KeepsServiceError
from django.db import IntegrityError
from jose import ExpiredSignatureError, JWTError
from rest_framework.response import Response
from rest_framework.status import HTTP_403_FORBIDDEN
from rest_framework.views import exception_handler

LOGGER = logging.getLogger("keeps")


def filters(request):
    info = {
        "request_method": getattr(request, "method", "-"),
        "path_info": getattr(request, "path_info", "-"),
        "username": "-",
    }

    try:
        user = getattr(request, "user", None)
        if user:
            info["username"] = user.get("email")
    except Exception:
        info["username"] = "error_get_user"

    return info


def custom_exception_handler(exc, context):
    # TODO: Jesus que tanto IF, refatorar para usar o design patter factory por completo
    response = exception_handler(exc, context)
    if not isinstance(exc, (ExpiredSignatureError, JWTError)):
        LOGGER.error(response, extra=filters(context["request"]))
        LOGGER.error(exc)

    if isinstance(exc, KeepsError):
        return Response(
            {"i18n": exc.i18n, "detail": exc.detail, "status_code": exc.status_code}, status=exc.status_code
        )
    if isinstance(exc, KeepsPermissionError):
        return Response(
            {"i18n": "not_allowed", "detail": exc.description, "status_code": HTTP_403_FORBIDDEN},
            status=HTTP_403_FORBIDDEN,
        )
    if isinstance(exc, KeepsServiceError):
        return Response({"i18n": exc.description, "detail": exc.description, "status_code": 400}, status=400)
    if isinstance(exc, (ExpiredSignatureError, JWTError)):
        response = Response({"detail": str(exc), "status_code": 401}, status=401)
    elif isinstance(exc, IntegrityError):
        response = Response({"detail": str(exc), "status_code": 409}, status=409)
    elif response is not None:
        response.data["status_code"] = response.status_code

    return response
