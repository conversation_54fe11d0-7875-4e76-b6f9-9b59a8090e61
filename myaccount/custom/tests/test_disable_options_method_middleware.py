from custom.disable_options_method_middleware import DisableOptionsMethodMiddleware
from django.http import HttpResponse
from django.test import RequestFactory, TestCase


class DisableOptionsMethodMiddlewareTest(TestCase):
    def setUp(self):
        self.factory = RequestFactory()
        self.get_response = lambda request: HttpResponse()
        self.middleware = DisableOptionsMethodMiddleware(self.get_response)

    def test_options_method_not_allowed(self):
        request = self.factory.options('/test/')
        response = self.middleware(request)
        self.assertEqual(response.status_code, 405)

    def test_get_method_allowed(self):
        request = self.factory.get('/test/')
        response = self.middleware(request)
        self.assertEqual(response.status_code, 200)

    def test_post_method_allowed(self):
        request = self.factory.post('/test/')
        response = self.middleware(request)
        self.assertEqual(response.status_code, 200)
