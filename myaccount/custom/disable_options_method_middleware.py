from django.http import HttpResponseNotAllowed
from django.utils.deprecation import MiddlewareMixin


class DisableOptionsMethodMiddleware(MiddlewareMixin):
    @staticmethod
    def process_request(request):
        if not request.method == "OPTIONS":
            return
        # Get the view for the request
        resolver_match = request.resolver_match
        if not resolver_match:
            return HttpResponseNotAllowed([])
        view_cls = resolver_match.func.view_class
        if hasattr(view_cls, "action_map"):
            allowed_methods = set()
            for method in view_cls.action_map.values():
                allowed_methods.add(method.upper())
            if "OPTIONS" not in allowed_methods:
                return HttpResponseNotAllowed(allowed_methods)
