from rest_framework import status
from rest_framework.exceptions import APIException


class KeepsError(APIException):
    def __init__(self, detail, i18n, status_code):
        super().__init__()
        self.i18n = i18n
        self.detail = detail
        self.status_code = status_code


class KeepsPermissionError(Exception):
    def __init__(self, message):
        self.description = message
        super().__init__(message)


class KeepsBadRequestError(KeepsError):
    def __init__(self, detail, i18n):
        super().__init__(detail, i18n, status.HTTP_400_BAD_REQUEST)


class KeepsNotAllowedError(KeepsError):
    def __init__(self, detail, i18n):
        super().__init__(detail, i18n, status.HTTP_403_FORBIDDEN)


class KeepsNotFoundCompanyError(KeepsError):
    def __init__(self):
        super().__init__(
            "Company not found, check if Konquest service is active", "company_not_found", status.HTTP_403_FORBIDDEN
        )


class KeepsNoPermissionToEditWorkspace(KeepsPermissionError):
    def __init__(self):
        super().__init__("Not allowed to edit this workspace")


class KeepsServiceError(Exception):
    def __init__(self, description):
        self.description = description
        super().__init__(self.description)


class KeepsInvalidIdentityProvider(KeepsServiceError):
    def __init__(self):
        super().__init__("invalid_identity_provider_alias")


class KeepsClientHeaderNotFoundCompanyError(KeepsError):
    def __init__(self):
        super().__init__(
            "x_client_not_found", "Workspace not found (set x-client on header)", status.HTTP_403_FORBIDDEN
        )


class KeepsUnauthorizedError(KeepsError):
    def __init__(self):
        super().__init__("Unauthorized", "unauthorized", status.HTTP_401_UNAUTHORIZED)
