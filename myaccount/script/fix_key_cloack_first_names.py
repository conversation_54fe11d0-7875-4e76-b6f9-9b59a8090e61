import logging
import os

import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from common.models import User
from utils.keycloak import KeyCloakUserManager


def run():
    success = 0
    errors = 0
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    logging.basicConfig(
        filename="log/fix_key_cloack_first_names_PRD.log",
        level=logging.DEBUG,
        format="%(asctime)s:%(levelname)s:%(message)s",
    )

    key_cloak = KeyCloakUserManager()
    users = User.objects.filter()
    for user in users:
        try:
            key_cloak.update_user(user.id, {"firstName": user.name})
            success += 1
            logger.info(f"updated user({user.id}) firstName")

        except Exception as error:
            errors += 1
            logger.error(f"{error} user({user.id})")
        logger.info(f"{success} are updated")
    logger.info("script done")


if __name__ == "__main__":
    run()
