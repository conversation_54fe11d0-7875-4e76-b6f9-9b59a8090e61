import csv
import os

import django
from django.conf import settings
from keycloak import KeycloakAdmin

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from common.models import User, UserRoleWorkspace


class UserAdd:
    def __init__(self, company_id):
        self.keycloak_admin = KeycloakAdmin(
            server_url=settings.IAM_ADMIN_SERVER_URL,
            username=settings.IAM_ADMIN_USER_ADMIN,
            password=settings.IAM_ADMIN_PASS_ADMIN,
            realm_name=settings.IAM_ADMIN_REALM,
        )
        self.company_id = company_id
        self.user_result = {}

        # MyAccount AccountAdmin Role / Konquest User Role
        self.roles = ["3b16b975-0297-4edf-950b-e3700b0d0d01", "a6d23aea-807e-4374-964e-c725b817742d"]

    def execute(self, file):
        users_to_add = self.read_csv(file)

        for _user in users_to_add:
            user_payload = {
                "email": _user["email"],
                "username": _user["email"],
                "enabled": True,
                "emailVerified": True,
                "firstName": _user["nome"].upper(),
                "attributes": {"locale": ["pt-BR"]},
                "credentials": [{"value": "123456", "type": "password", "temporary": False}],
            }

            user_kc = self.create_kc_user(payload=user_payload)
            user_payload["id"] = user_kc
            user_payload["name"] = _user["nome"].upper()
            user_payload["job"] = _user["job"].upper()
            user_payload["address"] = _user["office"].upper()
            user_payload["ein"] = _user["matricula"].upper()
            user_payload["phone"] = _user["telefone"]
            user_payload["birthday"] = _user["data_nasc"]
            user_payload["language_id"] = "ea636f50-fdc4-49b0-b2de-9e5905de456b"

            user = self.create_myacc_user(user_payload)

            if user[1]:
                self.add_application_roles(user[0].id)
            else:
                print(f'{_user["email"]},NOT_ADDED')

            print(f'{_user["email"]},ADDED')

    @staticmethod
    def create_myacc_user(user_info):
        if User.objects.filter(email=user_info["email"]).exists():
            return User.objects.filter(email=user_info["email"]).first(), True

        user_instance = User()

        try:
            user_instance.id = user_info["id"]
            user_instance.email = user_info["email"]
            user_instance.name = user_info["name"]
            user_instance.ein = user_info["ein"]
            user_instance.phone = user_info["phone"]
            user_instance.job = user_info["job"]
            user_instance.address = user_info["address"]
            user_instance.save()
            return user_instance, True

        except Exception as e:
            return e, False

    def add_application_roles(self, user_id):
        """
        Add roles defined into init.

        :param user_id:
        """

        for role in self.roles:
            try:
                user_role = UserRoleWorkspace()
                user_role.company_id = self.company_id
                user_role.role_id = role
                user_role.user_id = user_id
                user_role.self_sign_up = True
                user_role.save()
            except Exception as e:
                print(f"{user_id},{role},ROLE_NOT_ADDED,{e}")

    def create_kc_user(self, payload):
        try:
            user_kc = self.keycloak_admin.create_user(payload)
            self.keycloak_admin.set_user_password(user_kc, payload["credentials"][0]["value"], False)
        except Exception:
            user_kc = None

        return user_kc

    @staticmethod
    def read_csv(files):
        """
        Read an CSV file and return into dict

        :param files: path to file
        :return: users info

        """
        with open(files) as file:
            users_info = [{k: v for k, v in row.items()} for row in csv.DictReader(file, skipinitialspace=True)]

        return users_info


COMPANY_ID = "37bf7109-5649-4336-a671-231161993bf4"

IMPORTER = UserAdd(COMPANY_ID)
IMPORTER.execute(f"{settings.BASE_DIR}/script/users.csv")
