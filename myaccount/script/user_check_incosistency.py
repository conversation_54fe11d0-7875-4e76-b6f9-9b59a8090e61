import csv
import os

import django
from django.conf import settings
from django.contrib.gis.gdal.prototypes.geom import is_empty
from keycloak import KeycloakAdmin

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from common.models import User


class UserInconsistency:
    def __init__(self):
        self.keycloak_admin = KeycloakAdmin(
            server_url=settings.IAM_ADMIN_SERVER_URL,
            username=settings.IAM_ADMIN_USER_ADMIN,
            password=settings.IAM_ADMIN_PASS_ADMIN,
            realm_name=settings.IAM_ADMIN_REALM,
        )

    def execute(self, file):
        users_to_add = self.read_csv(file)
        print("kc_email,kc_username,kc_id,myacc_id,myacc_email,inconsistency")

        for _user in users_to_add:
            try:
                user_kc = self.keycloak_admin.get_users({"username": _user["username"]})
                user_kc = [x for x in user_kc if x["username"] == _user["username"]]

                if len(user_kc) == 1:
                    field_1 = user_kc[0]["email"]
                    field_2 = user_kc[0]["username"]
                    field_3 = user_kc[0]["id"]
                elif is_empty(user_kc):
                    field_1 = _user["email"]
                    field_2 = "KC user not found"
                    field_3 = "KC_ERROR"
                else:
                    field_1 = _user["email"]
                    field_2 = "KC has 2 or more users"
                    field_3 = "KC_ERROR"

                if len(user_kc) == 1 and User.objects.filter(id=user_kc[0]["id"]).exists():
                    user_myacc = User.objects.filter(id=user_kc[0]["id"]).first()
                    field_4 = user_myacc.id
                    field_5 = user_myacc.email
                else:
                    field_4 = "MYACC_ERROR"
                    field_5 = "email not found"

                if str(field_5) == str(field_1) == str(_user["email"]):
                    field_6 = False
                else:
                    field_6 = True

                print(f'{field_1},{field_2},{field_3},{field_4},{field_5},{field_6},{_user["email"]}')
            except Exception as e:
                print(f'{_user["email"]},{_user["username"]},NO_ID_FOUND,NO_ID_FOUND,{e},')

    @staticmethod
    def read_csv(files):
        """
        Read an CSV file and return into dict

        :param files: path to file
        :return: users info

        """
        with open(files) as file:
            users_info = [{k: v for k, v in row.items()} for row in csv.DictReader(file, skipinitialspace=True)]

        return users_info


INCONSISTENCY = UserInconsistency()
INCONSISTENCY.execute(f"{settings.BASE_DIR}/script/users.csv")
