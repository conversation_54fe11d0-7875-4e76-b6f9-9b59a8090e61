import os

import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from common.models import Company, User, Workspace
from config import settings

OLD_URL = "https://s3.amazonaws.com/keeps.myaccount.media.hml/"
NEW_URL = f"{settings.AWS_BASE_CDN_URL}/{settings.AWS_BUCKET_NAME}/{settings.AWS_BUCKET_PATH}/"


def update_users_s3_url():
    users_updated = []
    users = User.objects.exclude(avatar=None).exclude(avatar="").exclude(avatar__icontains=NEW_URL)
    for user in users:
        print(user.avatar)
        user.avatar = user.avatar.replace(OLD_URL, NEW_URL)
        users_updated.append(user)
        print(user.avatar)
        print("--")
    print("-------------------")
    # User.objects.bulk_update(users_updated, fields=['avatar'])


def update_companies_s3_url():
    companies_updated = []
    companies = Company.objects.exclude(icon_url=None).exclude(icon_url="").exclude(icon_url__icontains=NEW_URL)
    for company in companies:
        print(company.icon_url)
        company.icon_url = company.icon_url.replace(OLD_URL, NEW_URL)
        companies_updated.append(company)
        print(company.icon_url)
        print("--")
    print("-------------------")
    # Company.objects.bulk_update(companies_updated, fields=['icon_url'])


def update_workspaces_s3_url():
    workspaces_updated = []
    workspaces = (
        Workspace.objects.exclude(icon_url=None)
        .exclude(icon_url="")
        .exclude(logo_url=None)
        .exclude(logo_url="")
        .exclude(icon_url__icontains=NEW_URL)
        .exclude(logo_url__icontains=NEW_URL)
    )
    for workspace in workspaces:
        print(workspace.icon_url)
        print(workspace.logo_url)
        workspace.icon_url = workspace.icon_url.replace(OLD_URL, NEW_URL)
        workspace.logo_url = workspace.logo_url.replace(OLD_URL, NEW_URL)
        workspaces_updated.append(workspace)
        print("--")
        print(workspace.icon_url)
        print(workspace.logo_url)
        print("----------")
    print("-------------------")
    # Workspace.objects.bulk_update(workspaces_updated, fields=['icon_url','logo_url'])


update_workspaces_s3_url()
