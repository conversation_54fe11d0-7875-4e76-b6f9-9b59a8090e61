import os

import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from common.models import User

USER_TO_UPDATE = User.objects.filter().filter(created_date__range=("2021-01-01", "2021-02-24")).all()

for _user in USER_TO_UPDATE:
    if _user.email:
        print(_user.email, len(_user.email))
        _user.email = _user.email.strip()
        _user.save()
        print(_user.email, len(_user.email))
