import csv
import os

import django
from django.conf import settings
from keycloak import KeycloakAdmin

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from common.models import User


class UserDelete:
    def __init__(self):
        self.keycloak_admin = KeycloakAdmin(
            server_url=settings.IAM_ADMIN_SERVER_URL,
            username=settings.IAM_ADMIN_USER_ADMIN,
            password=settings.IAM_ADMIN_PASS_ADMIN,
            realm_name=settings.IAM_ADMIN_REALM,
        )

    def execute(self, file):  # noqa: C901
        users = self.read_csv(file)
        users_myacc = User.objects.filter(email__contains="@condor").count()
        for user in users_myacc:
            try:
                if user.email in [x["email"] for x in users]:
                    print(
                        f"{user.id},{user.email},myacc_not_deleted",
                    )
                else:
                    user.delete()
                    print(
                        f"{user.id},{user.email},myacc_deleted",
                    )
            except Exception as e:
                print(f"{user.id},{user.email},myacc_error_to_delete,{str(e)}")

        users_kc = self.keycloak_admin.get_users({"email": "@condor"})

        for user in users_kc:
            try:
                if user["email"] in [x["email"] for x in users]:
                    print(
                        f'{user["id"]},{user["email"]},kc_not_deleted',
                    )
                else:
                    self.keycloak_admin.delete_user(user["id"])
                    print(
                        f'{user["id"]},{user["email"]},kc_deleted',
                    )
            except Exception as e:
                print(f'{user["id"]},{user["email"]},kc_error_to_delete,{str(e)}')

    @staticmethod
    def read_csv(files):
        """
        Read an CSV file and return into dict

        :param files: path to file
        :return: users info

        """
        with open(files) as file:
            users_info = [{k: v for k, v in row.items()} for row in csv.DictReader(file, skipinitialspace=True)]

        return users_info


RESET_PASS = UserDelete()
RESET_PASS.execute(f"{settings.BASE_DIR}/script/users_ok.csv")
