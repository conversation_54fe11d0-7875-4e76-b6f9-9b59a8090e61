#!/usr/bin/env python3
"""
Script para importação ilimitada de usuários via CSV com multithreading
"""

import json
import logging
import os
import sys
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
from pathlib import Path

import django

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from common.models import User
from common.serializers.user_serializer import UserSerializer
from common.services.import_user_service import ImportUserService


def setup_logging():
    log_dir = Path(__file__).parent / "logs"
    log_dir.mkdir(exist_ok=True)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"import_users_{timestamp}.log"

    formatter = logging.Formatter(
        '%(asctime)s.%(msecs)03d+00:00 [%(levelname)s] %(message)s',
        datefmt='%Y-%m-%dT%H:%M:%S'
    )

    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(formatter)

    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)

    logger = logging.getLogger('import_users')
    logger.setLevel(logging.INFO)
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger, log_file


def get_file_path():
    while True:
        file_path = input("Digite o caminho completo do arquivo CSV: ").strip()
        if not file_path:
            print("Por favor, forneça um caminho válido.")
            continue
        if not os.path.exists(file_path):
            print(f"Arquivo não encontrado: {file_path}")
            continue
        if not file_path.lower().endswith(('.xls', '.xlsx')):
            print("O arquivo deve ser um arquivo Excel (.xls ou .xlsx)")
            continue
        return file_path


def get_permissions():
    while True:
        permissions_input = input("Digite os IDs das permissões (separados por vírgula): ").strip()
        if not permissions_input:
            print("Por favor, forneça pelo menos um ID de permissão.")
            continue
        try:
            permissions = [perm.strip() for perm in permissions_input.split(',')]
            for perm in permissions:
                if not perm:
                    raise ValueError("ID de permissão vazio")
            return permissions
        except ValueError as e:
            print(f"Erro ao processar permissões: {e}")
            print("Por favor, forneça IDs válidos separados por vírgula.")


def get_workspace_id():
    while True:
        workspace_id = input("Digite o ID do workspace: ").strip()
        if not workspace_id:
            print("Por favor, forneça um ID de workspace válido.")
            continue
        return workspace_id


def get_temporary_password():
    while True:
        temp_pass = input("Usar senha temporária? (s/n): ").strip().lower()
        if temp_pass in ['s', 'sim', 'y', 'yes']:
            return True
        elif temp_pass in ['n', 'não', 'nao', 'no']:
            return False
        else:
            print("Por favor, responda com 's' para sim ou 'n' para não.")


def threaded_import_users(import_service, users, permissions, workspace_id, temporary_password, logger):
    imported = []
    errors = []

    def import_single_user(user_data):
        try:
            instance = import_service.import_user(permissions, temporary_password, user_data, workspace_id)
            return ("success", instance)
        except Exception as e:
            user_data["error"] = "Erro ao importar usuário"
            user_data["log_error"] = str(e)
            user_data.pop("language", None)
            return ("error", user_data)

    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(import_single_user, user) for user in users]

        for future in as_completed(futures):
            status, result = future.result()
            if status == "success":
                imported.append(result)
            else:
                errors.append(result)

    return imported, errors


def main():
    logger, log_file = setup_logging()

    print("=== Script de Importação Ilimitada de Usuários ===")
    print(f"Log será salvo em: {log_file}")
    print()

    logger.info("Iniciando script de importação de usuários")

    try:
        file_path = "script/Cadastro_Denso.xlsx"
        workspace_id = "15c24286-be9e-4bd0-801e-179c30031abd"
        permissions = ["08404086-5e4e-48c6-91d7-dbeb360c7205","4ddf7c3a-13ab-47a2-98fd-ab0b177ef823","a6d23aea-807e-4374-964e-c725b817742d"]
        temporary_password = True

        params = {
            "file_path": file_path,
            "workspace_id": workspace_id,
            "permissions": permissions,
            "temporary_password": temporary_password
        }
        logger.info(f"Parâmetros configurados: {json.dumps(params, indent=2)}")

        print("\nParâmetros configurados:")
        print(f"  Arquivo: {file_path}")
        print(f"  Workspace ID: {workspace_id}")
        print(f"  Permissões: {', '.join(permissions)}")
        print(f"  Senha temporária: {'Sim' if temporary_password else 'Não'}\n")

        confirm = input("Deseja continuar com a importação? (s/n): ").strip().lower()
        if confirm not in ['s', 'sim', 'y', 'yes']:
            logger.info("Importação cancelada pelo usuário")
            print("Importação cancelada.")
            return

        import_service = ImportUserService()

        logger.info("Iniciando processamento do arquivo")
        print("Processando arquivo CSV...")
        users = import_service.parser_users(file_path, workspace_id)
        logger.info(f"Encontrados {len(users)} usuários para importação")
        print(f"Encontrados {users.__len__()} usuários para importação.")
        emails = [user["email"] for user in users]
        found_emails = User.objects.filter(email__in=emails).values_list("email", flat=True)
        not_found_user = [user for user in users if user["email"] not in found_emails]

        print(f"Encontrados {len(not_found_user)} usuários não cadastrados ainda.")
        users = not_found_user


        logger.info("Iniciando importação dos usuários com multithreading")
        print("Iniciando importação com múltiplas threads...")
        imported, errors = threaded_import_users(
            import_service, users, permissions, workspace_id, temporary_password, logger
        )

        result_summary = {
            "total_users": len(users),
            "imported_count": len(imported),
            "errors_count": len(errors),
            "success_rate": f"{(len(imported) / len(users)) * 100:.2f}%" if users else "0%"
        }
        logger.info(f"Resultados da importação: {json.dumps(result_summary, indent=2)}")

        print("\n=== RESULTADOS DA IMPORTAÇÃO ===")
        print(f"Usuários importados com sucesso: {len(imported)}")
        print(f"Erros encontrados: {len(errors)}")

        if errors:
            print("\n--- ERROS ---")
            error_details = []
            for error in errors:
                error_info = {
                    "name": error.get('name', 'Nome não informado'),
                    "email": error.get('email', 'Email não informado'),
                    "error": error.get('error', 'Erro desconhecido'),
                    "log_error": error.get('log_error')
                }
                error_details.append(error_info)
                print(f"- {error_info['name']} ({error_info['email']})")
                print(f"  Erro: {error_info['error']}")
                if error_info['log_error']:
                    print(f"  Log: {error_info['log_error']}")
                print()
            logger.error(f"Erros encontrados: {json.dumps(error_details, indent=2)}")

        logger.info("Importação concluída com sucesso")
        print("Importação concluída!")

    except Exception as e:
        logger.error(f"Erro fatal durante a importação: {str(e)}", exc_info=True)
        print(f"Erro durante a importação: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
