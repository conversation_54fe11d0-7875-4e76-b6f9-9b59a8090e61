import csv
import os

import django
from django.conf import settings

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from common.models import User


class UserUpdate:
    def __init__(self):
        pass

    def execute(self, file):
        users_to_update = self.read_csv(file)

        for _user in users_to_update:
            user_instance = User.objects.filter(email=_user["UserPrincipalName"].lower()).first()
            user_leader = User.objects.filter(ein=_user["MAT_LIDER1"]).first()
            if not user_leader:
                print(f'{user_instance.email},{_user["MAT_LIDER1"]},no leader found')
            else:
                user_instance.related_user_leader = user_leader
                user_instance.save()

    @staticmethod
    def read_csv(files):
        """
        Read an CSV file and return into dict

        :param files: path to file
        :return: users info

        """
        with open(files) as file:
            users_info = [{k: v for k, v in row.items()} for row in csv.DictReader(file, skipinitialspace=True)]

        return users_info


IMPORTER = UserUpdate()
IMPORTER.execute(f"{settings.BASE_DIR}/script/users.csv")
