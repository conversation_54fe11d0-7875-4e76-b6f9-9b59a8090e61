import os

import django
from config.settings import BASE_DIR

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from common.models import Company, Workspace

WORKSPACE_FILE = f"{BASE_DIR}/script/workspaces.csv"


def read_csv(files) -> list:
    with open(files) as file:
        instances = []
        for line in file.readlines():
            instances.append(line.rstrip("\n").split(","))
    return instances


def get_workspaces_id_to_company(file) -> dict:
    instances = read_csv(file)
    workspaces_company = {}
    for _instance in instances:
        workspaces_company.setdefault(_instance[2], []).append(_instance[0])
    return workspaces_company


def execute():
    companies_workspace_config = get_workspaces_id_to_company(WORKSPACE_FILE)
    # Empresa configurada no CSV
    for company_workspace in companies_workspace_config:
        workspace_ids = companies_workspace_config[company_workspace]
        workspaces = Workspace.objects.filter(id__in=workspace_ids).order_by("-created_date")
        first_workspace = workspaces.first()
        company = Company.objects.filter(name=company_workspace).first()
        if company:
            workspaces.update(company_id=company.id)
            continue
        company = Company(
            name=company_workspace,
            description=first_workspace.description,
            status=first_workspace.status,
            address=first_workspace.address,
            city=first_workspace.city,
            state=first_workspace.state,
            post_code=first_workspace.post_code,
            country=first_workspace.country,
            icon_url=first_workspace.icon_url,
        )
        company.save()
        workspaces.update(company_id=company.id)

    # Lista workspaces sem company_id
    # Criar Company para cada workspace
    workspaces_sem_company = Workspace.objects.filter(company_id__isnull=True)
    for workspace in workspaces_sem_company:
        company = Company.objects.filter(name=workspace.name).first()
        if company:
            workspace.update(company_id=company.id)
            continue
        company = Company(
            name=workspace.name,
            description=workspace.description,
            status=workspace.status,
            address=workspace.address,
            city=workspace.city,
            state=workspace.state,
            post_code=workspace.post_code,
            country=workspace.country,
            icon_url=workspace.icon_url,
        )
        company.save()
        workspace.company_id = company.id
        workspace.save()


if __name__ == "__main__":
    execute()
