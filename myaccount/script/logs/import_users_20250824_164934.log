2025-08-24T16:49:34.354+00:00 [INFO] Iniciando script de importação de usuários
2025-08-24T16:49:34.354+00:00 [INFO] Parâmetros configurados: {
  "file_path": "script/Cadastro_Denso.xlsx",
  "workspace_id": "15c24286-be9e-4bd0-801e-179c30031abd",
  "permissions": [
    "********-5e4e-48c6-91d7-dbeb360c7205",
    "4ddf7c3a-13ab-47a2-98fd-ab0b177ef823",
    "a6d23aea-807e-4374-964e-c725b817742d"
  ],
  "temporary_password": true
}
2025-08-24T16:49:36.557+00:00 [INFO] Iniciando processamento do arquivo
2025-08-24T16:49:39.794+00:00 [ERROR] Erro fatal durante a importação: Error to parse XLS file.
Traceback (most recent call last):
  File "/home/<USER>/projects/keeps/keeps-myaccount-server/myaccount/common/services/import_user_service.py", line 148, in parser_users
    user["status"] = line[26]
IndexError: list index out of range

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/projects/keeps/keeps-myaccount-server/myaccount/script/import_users_by_xlsx.py", line 171, in main
    users = import_service.parser_users(file_path, workspace_id)
  File "/home/<USER>/projects/keeps/keeps-myaccount-server/myaccount/common/services/import_user_service.py", line 158, in parser_users
    raise KeepsBadRequestError(
custom.keeps_exception_handler.KeepsBadRequestError: Error to parse XLS file.
