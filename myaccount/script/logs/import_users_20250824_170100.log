2025-08-24T17:01:00.336+00:00 [INFO] Iniciando script de importação de usuários
2025-08-24T17:01:00.336+00:00 [INFO] Parâmetros configurados: {
  "file_path": "script/Cadastro_Denso.xlsx",
  "workspace_id": "15c24286-be9e-4bd0-801e-179c30031abd",
  "permissions": [
    "********-5e4e-48c6-91d7-dbeb360c7205",
    "4ddf7c3a-13ab-47a2-98fd-ab0b177ef823",
    "a6d23aea-807e-4374-964e-c725b817742d"
  ],
  "temporary_password": true
}
2025-08-24T17:01:03.424+00:00 [INFO] Iniciando processamento do arquivo
2025-08-24T17:06:06.139+00:00 [ERROR] Erro fatal durante a importação: {'535': {'secondary_email': [ErrorDetail(string='Enter a valid email address.', code='invalid')]}}
Traceback (most recent call last):
  File "/home/<USER>/projects/keeps/keeps-myaccount-server/myaccount/script/import_users_by_xlsx.py", line 171, in main
    users = import_service.parser_users(file_path, workspace_id)
  File "/home/<USER>/projects/keeps/keeps-myaccount-server/myaccount/common/services/import_user_service.py", line 156, in parser_users
    raise validation_error
  File "/home/<USER>/projects/keeps/keeps-myaccount-server/myaccount/common/services/import_user_service.py", line 150, in parser_users
    validated_data = self.validate_user_data(user, workspace_id)
  File "/home/<USER>/projects/keeps/keeps-myaccount-server/myaccount/common/services/import_user_service.py", line 259, in validate_user_data
    serializer.is_valid(True)
  File "/home/<USER>/projects/keeps/keeps-myaccount-server/myaccount/common/serializers/workspace_user_serializer.py", line 64, in is_valid
    super().is_valid(True)
  File "/home/<USER>/.pyenv/versions/myaccount/lib/python3.9/site-packages/rest_framework/serializers.py", line 244, in is_valid
    raise ValidationError(self.errors)
rest_framework.exceptions.ValidationError: {'535': {'secondary_email': [ErrorDetail(string='Enter a valid email address.', code='invalid')]}}
