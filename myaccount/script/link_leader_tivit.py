import csv
import os

import django
from django.conf import settings

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from common.models import User


class RelatedUserLeader:
    def __init__(self):
        pass

    def execute(self, file):
        users_to_add = self.read_csv(file)

        for _user in users_to_add:
            try:
                user = User.objects.filter(email=_user["UserPrincipalName"])
                leader = User.objects.filter(ein=_user["MAT_LIDER1"])
                if leader:
                    user.related_user_leader = leader
                    user.save()
                    print(f'{_user["UserPrincipalName"]},{leader.name}')
                else:
                    print(f'{_user["UserPrincipalName"]},leader not found')

            except Exception as e:
                print(f'{_user["UserPrincipalName"]},{str(e)}')

        return True

    @staticmethod
    def read_csv(files):
        """
        Read an CSV file and return into dict

        :param files: path to file
        :return: users info

        """
        with open(files) as file:
            users_info = [{k: v for k, v in row.items()} for row in csv.DictReader(file, skipinitialspace=True)]

        return users_info


IMPORTER = RelatedUserLeader()
RESULT = IMPORTER.execute(f"{settings.BASE_DIR}/script/users.csv")
