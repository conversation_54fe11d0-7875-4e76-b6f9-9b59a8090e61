import csv
import os

import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from utils.keycloak.user_manager import KeyCloakUserManager

keycloak = KeyCloakUserManager()


def update_password_from_csv(csv_path):
    try:
        with open(csv_path, mode="r") as file:
            csv_reader = csv.reader(file, delimiter=";")
            next(csv_reader)

            for row in csv_reader:
                if len(row) < 2:
                    print(f"Linha ignorada devido a dados incompletos: {row}")
                    continue

                id, new_password = row[0], row[1]
                response = update_password(id, new_password)

                if response:
                    print(f"Senha atualizada para {response}")
                else:
                    print(f"Falha ao atualizar senha para {id}")

    except FileNotFoundError:
        print(f"Erro: Arquivo '{csv_path}' não encontrado.")
    except Exception as e:
        print(f"Erro ao processar o arquivo CSV: {e}")


def update_password(id, new_password):
    print(f"Atualizando senha do usuário {id} para {new_password}")
    try:
        keycloak.set_user_password(user_id=id, password=new_password, temporary=False)
        return id
    except Exception as e:
        print(f"Erro ao atualizar senha para {id}: {e}")
        return None


if __name__ == "__main__":
    update_password_from_csv("myaccount/script/users.csv")
