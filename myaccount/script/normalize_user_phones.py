import os

import django
import phonenumbers
from phonenumbers import NumberParseException

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from common.models import User


class NormalizeUserPhone:
    def __init__(self):
        self.count_success = 0
        self.total = 0

    def run(self):
        users = User.objects.filter(phone__isnull=False).exclude(phone="")
        self.total = users.count()
        print(f"Total: {self.total}")
        for user in users:
            phone = user.phone
            print(f"User phone: {phone}")
            if "+" not in phone:
                self.save(phone)
                continue
            try:
                phone_number = phonenumbers.parse(phone, "BR")
                is_valid_number = phonenumbers.is_valid_number(phone_number)
                if is_valid_number:
                    phone = f"{phone_number.country_code}{phone_number.national_number}"
                else:
                    phone = None
            except NumberParseException:
                phone = None
            user.phone = phone
            user.save()
            self.save(phone)

    def save(self, phone):
        self.count_success += 1
        print(f"Formatted Phone: {phone}")
        print(f"Progress: {round(self.count_success / self.total, 2)*100}%")
        print("-----")


if __name__ == "__main__":
    NormalizeUserPhone().run()
