import csv
import os

import django
from django.conf import settings
from keycloak import KeycloakAdmin

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from common.models import User, UserRoleWorkspace


# pylint: disable=R0902
class ActiveDirectoryUser:
    def __init__(self, company_id, adfs_alias, ad_context, locale):
        self.keycloak_admin = KeycloakAdmin(
            server_url=settings.IAM_ADMIN_SERVER_URL,
            username=settings.IAM_ADMIN_USER_ADMIN,
            password=settings.IAM_ADMIN_PASS_ADMIN,
            realm_name=settings.IAM_ADMIN_REALM,
        )
        self.company_id = company_id
        self.adfs_alias = adfs_alias
        self.ad_context = ad_context
        self.locale = locale
        self.import_result = []
        self.user_result = {}
        # MyAccount AccountAdmin Role / Konquest User Role
        self.roles = ["3b16b975-0297-4edf-950b-e3700b0d0d01", "a6d23aea-807e-4374-964e-c725b817742d"]
        self.language_map = {
            "pt-BR": "ea636f50-fdc4-49b0-b2de-9e5905de456b",
            "es": "8b58683a-0e0b-4c5c-8fbc-5f07d043a365",
        }

    def execute(self, file):
        users_to_add = self.read_csv(file)

        for _user in users_to_add:
            user_fed_id = _user["UserPrincipalName"].lower()
            user_fed_name = _user["UserPrincipalName"].lower()
            _user["username"] = _user["UserPrincipalName"].lower()

            user_payload = {
                "email": _user["username"],
                "username": _user["username"],
                "enabled": True,
                "emailVerified": True,
                "firstName": _user["Name"],
                "attributes": {"locale": [self.locale]},
                "credentials": [{"value": "123456", "type": "password", "temporary": False}],
                "federatedIdentities": [
                    {"identityProvider": self.adfs_alias, "userId": user_fed_id, "userName": user_fed_name}
                ],
            }

            self.user_result = {
                "keycloak": {"status": None, "message": None},
                "keycloak_id_provider_link": {"status": None, "message": None},
                "myacc": {"status": None, "message": None},
                "myacc_roles": {"status": None, "message": None},
            }

            user_kc = self.create_kc_user(payload=user_payload)
            self.identity_provider_link(user_kc, user_fed_id, user_fed_name)

            user_payload["id"] = user_kc
            user_payload["name"] = _user["Name"].upper()
            user_payload["nickname"] = _user["SamAccountName"].upper()
            user_payload["job"] = _user["Title"].upper()
            user_payload["address"] = _user["Office"].upper()
            user_payload["country"] = _user["Pais"].upper()
            user_payload["ein"] = _user["MATRICULA"].upper()
            user_payload["language_id"] = self.language_map[self.locale]

            self.create_myacc_user(user_payload)

            self.add_application_roles(user_kc)

            self.import_result.append({_user["username"]: self.user_result})

        print("email,kc,kc_ad,myacc,myacc_roles")
        for results in self.import_result:
            for key, value in results.items():
                print(
                    f'{key},{value["keycloak"]["status"]},{value["keycloak_id_provider_link"]["status"]},{value["myacc"]["status"]},{value["myacc_roles"]["status"]}'
                )

        return self.import_result

    def create_kc_user(self, payload):
        try:
            user_kc = self.keycloak_admin.create_user(payload)
            self.user_result["keycloak"]["status"] = "OK"
            self.user_result["keycloak"]["message"] = "Added user to KC"
            self.keycloak_admin.set_user_password(user_kc, payload["credentials"][0]["value"], False)

        except Exception as e:
            self.user_result["keycloak"]["status"] = "ERROR"
            self.user_result["keycloak"]["message"] = str(e)
            user_kc = self.keycloak_admin.get_users({"email": payload["email"]})[0]
        return user_kc

    def identity_provider_link(self, kc_user_id, user_id_provider, user_name_provider):
        try:
            self.keycloak_admin.add_user_social_login(kc_user_id, self.adfs_alias, user_id_provider, user_name_provider)
            self.user_result["keycloak_id_provider_link"]["status"] = "OK"
            self.user_result["keycloak_id_provider_link"]["message"] = "Linked user to ADFS"
        except Exception as e:
            self.user_result["keycloak_id_provider_link"]["status"] = "ERROR"
            self.user_result["keycloak_id_provider_link"]["message"] = str(e)

    def create_myacc_user(self, user_info):
        """
        After add user into Keycloak, add to MyAcc

        :param user_info:
            id, email, Name, Title, Office
        """

        if User.objects.filter(email=user_info["email"]).exists():
            self.user_result["myacc"]["status"] = "WARNING"
            self.user_result["myacc"]["message"] = "MyAcc User Already Exist"
            return

        user_instance = User()

        try:
            user_instance.id = user_info["id"]
            user_instance.email = user_info["email"]
            user_instance.name = user_info["name"]
            user_instance.job = user_info["job"]
            user_instance.address = user_info["address"]
            user_instance.language_id = user_info["language_id"]
            user_instance.country = user_info["country"]
            user_instance.ein = user_info["ein"]
            user_instance.save()
            self.user_result["myacc"]["status"] = "OK"
            self.user_result["myacc"]["message"] = "Created MyAcc User"

        except Exception as e:
            self.user_result["myacc"]["status"] = "ERROR"
            self.user_result["myacc"]["message"] = str(e)

    def add_application_roles(self, user_id):
        """
        Add roles defined into init.

        :param user_id:
        """

        for role in self.roles:
            try:
                user_role = UserRoleWorkspace()
                user_role.company_id = self.company_id
                user_role.role_id = role
                user_role.user_id = user_id
                user_role.self_sign_up = True
                user_role.save()
                self.user_result["myacc_roles"]["status"] = "OK"
                self.user_result["myacc_roles"]["message"] = "Added Roles"
            except Exception as e:
                self.user_result["myacc_roles"]["status"] = "ERROR"
                self.user_result["myacc_roles"]["message"] = str(e)

    @staticmethod
    def read_csv(files):
        """
        Read an CSV files and return into dict

        :param files: path to file
        :return: users info

        """
        with open(files) as file:
            users_info = [{k: v for k, v in row.items()} for row in csv.DictReader(file, skipinitialspace=True)]

        return users_info


ADFS_ALIAS = "adfs-idp-tivit"
AD_PATH = "TIVIT\\"
COMPANY_ID = "a6b23c1f-60e9-474a-8bb6-59085b90355f"
LOCALE = "es"

IMPORTER = ActiveDirectoryUser(COMPANY_ID, ADFS_ALIAS, AD_PATH, LOCALE)
RESULT = IMPORTER.execute(f"{settings.BASE_DIR}/script/users.csv")
