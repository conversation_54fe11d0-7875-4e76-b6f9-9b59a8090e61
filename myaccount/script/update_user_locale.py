import os

import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from common.models.user import User
from common.services.user_service import UserService


# pylint: disable=R0903, W0212
class UserUpdate:
    def __init__(self):
        pass

    def execute(self):
        users = User.objects.filter(updated_date__range=("2020-01-01", "2020-12-31")).order_by("-updated_date").all()
        locale = "pt-BR"
        count = 0
        for user in users:
            count = +1
            try:
                if user.language:
                    _locale_parts = locale.split("-")

                    if len(_locale_parts) > 1:
                        locale = f"{_locale_parts[0].lower()}-{_locale_parts[1].upper()}"
                else:
                    user.language_id = "ea636f50-fdc4-49b0-b2de-9e5905de456b"
                    user.save()
                service = UserService()
                service._update_keycloak_user(user)
                print(f"{user.email} | OK")
            except Exception as e:
                print(f"{user.email} | {e}")

        print(count)


UserUpdate().execute()
