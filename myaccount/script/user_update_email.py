import csv
import os

import django
from django.conf import settings
from keycloak import KeycloakAdmin

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from common.models import User


class UserUpdateEmail:
    def __init__(self):
        self.keycloak_admin = KeycloakAdmin(
            server_url=settings.IAM_ADMIN_SERVER_URL,
            username=settings.IAM_ADMIN_USER_ADMIN,
            password=settings.IAM_ADMIN_PASS_ADMIN,
            realm_name=settings.IAM_ADMIN_REALM,
        )

    def execute(self, file):
        users_to_update = self.read_csv(file)
        for _user in users_to_update:
            try:
                user_myacc = User.objects.filter(email=_user["secondary_email"]).first()
                user_kc = self.keycloak_admin.get_user(str(user_myacc.id))

                if str(user_myacc.id) == str(user_kc["id"]):
                    payload = {"email": _user["email"], "username": _user["email"]}
                    self.keycloak_admin.update_user(user_kc["id"], payload)

                    user_myacc.email = _user["email"]
                    user_myacc.secondary_email = _user["secondary_email"]
                    user_myacc.save()
                    print(f'{user_kc["id"]},{_user["email"]}')

                else:
                    print(f'check: {_user["email"]}')

            except Exception as e:
                print(f'{_user["email"]},{e}')

    @staticmethod
    def read_csv(files):
        """
        Read an CSV file and return into dict

        :param files: path to file
        :return: users info

        """
        with open(files) as file:
            users_info = [{k: v for k, v in row.items()} for row in csv.DictReader(file, skipinitialspace=True)]

        return users_info


INCONSISTENCY = UserUpdateEmail()
INCONSISTENCY.execute(f"{settings.BASE_DIR}/script/users.csv")
