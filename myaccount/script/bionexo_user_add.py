import csv
import os
import logging
import secrets
from datetime import datetime
import django
from django.conf import settings
from keycloak import Key<PERSON>loakAdmin

from myaccount.config.settings import KONQUEST_DEFAULT_ROLE, MYACCOUNT_DEFAULT_ROLE

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from common.models import User, UserRoleWorkspace

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s.%(msecs)03d %(levelname)s: %(message)s',
    datefmt='%Y-%m-%dT%H:%M:%S',
    handlers=[
        logging.FileHandler('user_import-9b8a7a6a-0d19-44df-bf7a-72d6769a4c3a.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class UserAdd:
    def __init__(self, company_id):
        self.keycloak_admin = KeycloakAdmin(
            server_url=settings.IAM_ADMIN_SERVER_URL,
            username=settings.IAM_ADMIN_USER_ADMIN,
            password=settings.IAM_ADMIN_PASS_ADMIN,
            realm_name=settings.IAM_ADMIN_REALM,
        )
        self.company_id = company_id
        self.user_result = {}
        self.roles = [MYACCOUNT_DEFAULT_ROLE, KONQUEST_DEFAULT_ROLE]

    def execute(self, file):
        logger.info(f"Iniciando importação de usuários do arquivo: {file}")
        users_to_add = self.read_csv(file)

        for _user in users_to_add:
            try:
                exists = User.objects.filter(email=_user["email"]).exists()
                if exists:
                    logger.warning(f"Usuário já existe no MyAccount: {_user['email']}")
                    continue

                logger.info(f"Processando usuário: {_user['email']}")
                user_payload = {
                    "email": _user["email"],
                    "username": _user["email"],
                    "enabled": True,
                    "emailVerified": True,
                    "firstName": _user["nome"].upper(),
                    "attributes": {"locale": ["pt-BR"]},
                    "credentials": [{"value": secrets.token_urlsafe(8), "type": "password", "temporary": False}],
                }

                user_kc = self.create_kc_user(payload=user_payload)
                if not user_kc:
                    logger.error(f"Falha ao criar usuário no Keycloak: {_user['email']}")
                    continue

                user_payload["id"] = user_kc
                user_payload["name"] = _user["nome"].upper()
                user_payload["language"] = "ea636f50-fdc4-49b0-b2de-9e5905de456b"

                user, success = self.create_myacc_user(user_payload)
                if not success:
                    logger.error(f"Falha ao criar usuário no MyAccount: {_user['email']} - {str(user)}")
                    continue

                self.add_application_roles(user.id)
                logger.info(f"Usuário adicionado com sucesso: {_user['email']}")

            except Exception as e:
                logger.error(f"Erro ao processar usuário {_user['email']}: {str(e)}")

    @staticmethod
    def create_myacc_user(user_info):
        if User.objects.filter(email=user_info["email"]).exists():
            logger.warning(f"Usuário já existe no MyAccount: {user_info['email']}")
            return User.objects.filter(email=user_info["email"]).first(), True

        user_instance = User()

        try:
            user_instance.id = user_info["id"]
            user_instance.email = user_info["email"]
            user_instance.name = user_info["name"]
            user_instance.save()
            return user_instance, True

        except Exception as e:
            return e, False

    def add_application_roles(self, user_id):
        for role in self.roles:
            try:
                UserRoleWorkspace.objects.create(
                    workspace_id=self.company_id,
                    role_id=role,
                    user_id=user_id,
                    self_sign_up=True,
                )
                logger.info(f"Role {role} adicionada para o usuário {user_id}")
            except Exception as e:
                logger.error(f"Falha ao adicionar role {role} para o usuário {user_id}: {str(e)}")

    def create_kc_user(self, payload):
        try:
            user_kc = self.keycloak_admin.create_user(payload)
            self.keycloak_admin.set_user_password(user_kc, payload["credentials"][0]["value"], False)
            logger.info(f"Usuário criado no Keycloak: {payload['email']} (ID: {user_kc})")
            return user_kc
        except Exception as e:
            logger.error(f"Erro ao criar usuário no Keycloak {payload['email']}: {str(e)}")
            return None

    @staticmethod
    def read_csv(files):
        try:
            with open(files) as file:
                users_info = [{k: v for k, v in row.items()} for row in csv.DictReader(file, skipinitialspace=True)]
            logger.info(f"CSV lido com sucesso: {len(users_info)} usuários encontrados")
            return users_info
        except Exception as e:
            logger.error(f"Erro ao ler arquivo CSV: {str(e)}")
            raise

if __name__ == "__main__":
    COMPANY_ID = "e98d47e5-e26d-4db8-9cc3-0552de437398"
    
    try:
        logger.info("Iniciando script de importação de usuários")
        IMPORTER = UserAdd(COMPANY_ID)
        IMPORTER.execute(f"{settings.BASE_DIR}/script/bionexo_user-{COMPANY_ID}.csv")
        logger.info("Importação concluída com sucesso")
    except Exception as e:
        logger.error(f"Erro fatal durante a importação: {str(e)}", exc_info=True)