import os

import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from common.services.billing_konquest_service import BillingKonquestService
from common.services.billing_smartzap_service import BillingSmartzapService

dates = [("2022-07-01", "2022-07-31"), ("2022-08-01", "2022-08-31"), ("2022-09-01", "2022-09-30")]

for date in dates:
    BillingKonquestService().generate(date[0], date[1])
    BillingSmartzapService().generate(date[0], date[1])
