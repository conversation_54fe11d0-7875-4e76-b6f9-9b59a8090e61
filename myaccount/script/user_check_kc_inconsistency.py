import csv
import os

import django
from django.conf import settings

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from keycloak import KeycloakAdmin


class UserInconsistency:
    def __init__(self):
        self.keycloak_admin = KeycloakAdmin(
            server_url=settings.IAM_ADMIN_SERVER_URL,
            username=settings.IAM_ADMIN_USER_ADMIN,
            password=settings.IAM_ADMIN_PASS_ADMIN,
            realm_name=settings.IAM_ADMIN_REALM,
        )

    def execute(self, file):
        users_to_add = self.read_csv(file)
        for _user in users_to_add:
            try:
                user_kc = self.keycloak_admin.get_users({"username": _user["email"]})
                if len(user_kc) == 1:
                    print(f'{user_kc[0]["id"]},{user_kc[0]["email"]},{user_kc[0]["username"]}')

            except Exception as e:
                print(f'{_user["email"]},{e}')

    @staticmethod
    def read_csv(files):
        """
        Read an CSV file and return into dict

        :param files: path to file
        :return: users info

        """
        with open(files) as file:
            users_info = [{k: v for k, v in row.items()} for row in csv.DictReader(file, skipinitialspace=True)]

        return users_info


INCONSISTENCY = UserInconsistency()
INCONSISTENCY.execute(f"{settings.BASE_DIR}/script/users_ok.csv")
