import csv
import os

import django
from django.conf import settings
from keycloak import KeycloakAdmin

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from common.models import User


class UserResetPass:
    def __init__(self):
        self.keycloak_admin = KeycloakAdmin(
            server_url=settings.IAM_ADMIN_SERVER_URL,
            username=settings.IAM_ADMIN_USER_ADMIN,
            password=settings.IAM_ADMIN_PASS_ADMIN,
            realm_name=settings.IAM_ADMIN_REALM,
        )

    def execute(self, file):
        users = self.read_csv(file)

        for _user in users:
            email = _user["email"].lower()

            try:
                instance = User.objects.filter(email=email).first()
                result = self.keycloak_admin.set_user_password(instance.id, _user["password"], temporary=False)
                print(f'{str(instance.id)},{instance.email},{_user["password"]},{result}')

            except Exception as e:
                print(f"error,{email},{e},")

    @staticmethod
    def read_csv(files):
        """
        Read an CSV file and return into dict

        :param files: path to file
        :return: users info

        """
        with open(files) as file:
            users_info = [{k: v for k, v in row.items()} for row in csv.DictReader(file, skipinitialspace=True)]

        return users_info


RESET_PASS = UserResetPass()
RESET_PASS.execute(f"{settings.BASE_DIR}/script/user_reset_pass.csv")
