import csv
import os

import django
from django.conf import settings

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from common.models import User, UserRoleWorkspace


class UserUpdate:
    def __init__(self, company_id):
        self.company_id = company_id
        self.user_result = {}
        self.roles = ["08404086-5e4e-48c6-91d7-dbeb360c7205"]

    def execute(self, file):
        users_to_add = self.read_csv(file)

        for _user in users_to_add:
            user_payload = {
                "email": _user["email"],
                "name": _user["name"],
                "phone": _user["phone"],
                "job": _user["job"],
            }
            user = self.update_myacc_user(user_payload)

            if user[1]:
                self.add_application_roles(user[0].id)
                print(f"{user[0].id},{user[0].email},UPDATED")
            else:
                print(f'NONE,{_user["email"]},NOT_UPDATED')

    @staticmethod
    def update_myacc_user(user_info):
        if User.objects.filter(email=user_info["email"].lower()).exists():
            instance = User.objects.filter(email=user_info["email"].lower()).first()

            try:
                instance.phone = user_info["phone"]
                instance.job = user_info["job"].upper()
                instance.name = user_info["name"]
                instance.save()

                return instance, True

            except Exception as e:
                return e, False

        else:
            return False, False

    def add_application_roles(self, user_id):
        """
        Add roles defined into init.

        :param user_id:
        """

        for role in self.roles:
            try:
                user_role = UserRoleWorkspace()
                user_role.company_id = self.company_id
                user_role.role_id = role
                user_role.user_id = user_id
                user_role.self_sign_up = True
                user_role.save()
            except Exception as e:
                print(f"{user_id},{role},ROLE_NOT_ADDED,{e}")

    @staticmethod
    def read_csv(files):
        """
        Read an CSV file and return into dict

        :param files: path to file
        :return: users info

        """
        with open(files) as file:
            users_info = [{k: v for k, v in row.items()} for row in csv.DictReader(file, skipinitialspace=True)]

        return users_info


COMPANY_ID = ""

IMPORTER = UserUpdate(COMPANY_ID)
IMPORTER.execute(f"{settings.BASE_DIR}/script/x.csv")
