import csv
import os

import django
from django.db import connections

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()


def get_company_name(company_id):
    with connections["default"].cursor() as cursor:
        cursor.execute(f"SELECT name from workspace WHERE workspace.id='{company_id}';")
        name = cursor.fetchone()
        return name[0]


def count_konquest_users(company_id, stat_date, end_date):
    with connections["konquest"].cursor() as cursor:
        query = (
            f"SELECT DISTINCT(learn_content_activity.user_id) FROM learn_content_activity "
            f" JOIN mission_enrollment ON mission_enrollment.id = learn_content_activity.mission_enrollment_id "
            f" WHERE mission_enrollment.company_id = '{company_id}' "
            f" AND learn_content_activity.created_date >= '{stat_date}' "
            f" AND learn_content_activity.created_date <= '{end_date}';"
        )

        cursor.execute(query)
        missions_users = list(cursor.fetchall())

        query = (
            f"SELECT DISTINCT (user_id) from learn_content_activity "
            f" JOIN pulse ON pulse.id=learn_content_activity.pulse_id "
            f" JOIN pulse_channel on pulse_channel.pulse_id=pulse.id "
            f" JOIN channel on pulse_channel.channel_id=channel.id "
            f" WHERE channel.company_id='{company_id}' "
            f" AND learn_content_activity.created_date >= '{stat_date}' "
            f" AND learn_content_activity.created_date <= '{end_date}';"
        )

        cursor.execute(query)
        pulses_users = list(cursor.fetchall())

        total = missions_users + pulses_users
        return len(total)


def count_smartzap_messages(company_id, start_date, end_date):
    with connections["smartzap"].cursor() as cursor:
        query = (
            f"SELECT count(*) from public.schedule "
            f" WHERE schedule.company_id='{company_id}' "
            f" AND schedule.send_date >= '{start_date}' "
            f" AND schedule.send_date <= '{end_date}' "
            f" AND schedule.status != 'ERROR';"
        )
        cursor.execute(query)
        name = cursor.fetchone()
        return name[0]


def get_companies():
    companies = {}

    with connections["default"].cursor() as cursor:
        cursor.execute(
            """
            SELECT company_id, application.name FROM service
            JOIN application ON service.application_id=application.id
            JOIN service_workspace ON service_workspace.service_id=service.id
            GROUP BY (company_id, application.name);
            """
        )
        row = cursor.fetchall()

        for column in row:
            if column[1] != "Learning Analytics" and column[1] != "My Account":
                company = companies.get(str(column[0]), None)
                if not company:
                    companies[str(column[0])] = {str(column[1]): 0}
                    companies[str(column[0])]["company_id"] = str(column[0])
                    companies[str(column[0])]["company_name"] = get_company_name(str(column[0]))

                else:
                    company[str(column[1])] = 0
                    companies[str(column[0])] = company

    return companies


def get_users(company_id):
    users = []
    query = f"SELECT DISTINCT(user_id) from user_role_company WHERE user_role_company.company_id='{company_id}';"
    with connections["default"].cursor() as cursor:
        cursor.execute(query)
        row = cursor.fetchall()
        for column in row:
            users.append(str(column[0]))
    return users


def main(start_date, end_date):
    companies = get_companies()
    for company in companies:
        companies[company]["Konquest"] = count_konquest_users(company, start_date, end_date)
        companies[company]["Smartzap"] = count_smartzap_messages(company, start_date, end_date)

    data = []

    for company in companies:
        data.append(companies[company])

    with open(f"{str(start_date)}_{str(end_date)}_billing.csv", "w+") as outf:
        writer = csv.DictWriter(outf, data[0].keys())
        writer.writeheader()
        for row in data:
            writer.writerow(row)


main("2022-06-01", "2022-06-30")
