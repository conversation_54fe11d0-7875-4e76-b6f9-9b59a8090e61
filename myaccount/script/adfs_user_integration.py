import csv
import os

import django
from django.conf import settings
from keycloak import KeycloakAdmin

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from common.models import User, UserRoleWorkspace


class ActiveDirectoryUser:
    def __init__(self, company_id, adfs_alias, ad_context):
        self.keycloak_admin = KeycloakAdmin(
            server_url=settings.IAM_ADMIN_SERVER_URL,
            username=settings.IAM_ADMIN_USER_ADMIN,
            password=settings.IAM_ADMIN_PASS_ADMIN,
            realm_name=settings.IAM_ADMIN_REALM,
        )
        self.company_id = company_id
        self.adfs_alias = adfs_alias
        self.ad_context = ad_context
        self.import_result = []
        self.user_result = {}

        # MyAccount AccountAdmin Role / Konquest User Role
        self.roles = [
            "3b16b975-0297-4edf-950b-e3700b0d0d01",
            "a6d23aea-807e-4374-964e-c725b817742d",
        ]

    def execute(self, file):
        users_to_add = self.read_csv(file)

        for _user in users_to_add:
            user_fed_id = f'{self.ad_context}{_user["ad_user"]}'
            user_fed_name = f'{self.ad_context.lower()}{_user["ad_user"]}'
            _user["username"] = _user["username"].lower()

            user_payload = {
                "email": _user["username"],
                "username": _user["username"],
                "enabled": True,
                "emailVerified": True,
                "firstName": _user["name"],
                "credentials": [{"value": "123456", "type": "password", "temporary": False}],
                "federatedIdentities": [
                    {"identityProvider": self.adfs_alias, "userId": user_fed_id, "userName": user_fed_name}
                ],
            }

            self.user_result = {
                "keycloak": {"status": None, "message": None},
                "keycloak_id_provider_link": {"status": None, "message": None},
                "myacc": {"status": None, "message": None},
                "myacc_roles": {"status": None, "message": None},
            }

            user_kc = self.create_kc_user(payload=user_payload)
            self.identity_provider_link(user_kc, user_fed_id, user_fed_name)

            user_payload["id"] = user_kc
            user_payload["name"] = _user["name"].upper()
            user_payload["job"] = _user["job"].upper()
            user_payload["address"] = _user["office"].upper()
            user_payload["secondary_email"] = _user["secondary_email"].lower() if _user["secondary_email"] else None

            self.create_myacc_user(user_payload)

            self.add_application_roles(user_kc)

            self.import_result.append({_user["username"]: self.user_result})

        print("email,kc,kc_ad,myacc,myacc_roles")
        for results in self.import_result:
            for key, value in results.items():
                print(
                    f'{key},{value["keycloak"]["status"]},{value["keycloak_id_provider_link"]["status"]},{value["myacc"]["status"]},{value["myacc_roles"]["status"]}'
                )

        return self.import_result

    def create_kc_user(self, payload):
        try:
            user_kc = self.keycloak_admin.create_user(payload)
            self.user_result["keycloak"]["status"] = "OK"
            self.user_result["keycloak"]["message"] = "Added user to KC"
            self.keycloak_admin.set_user_password(user_kc, payload["credentials"][0]["value"], False)

        except Exception as e:
            self.user_result["keycloak"]["status"] = "ERROR"
            self.user_result["keycloak"]["message"] = str(e)
            user_kc = self.keycloak_admin.get_users({"email": payload["email"]})[0]
        return user_kc

    def identity_provider_link(self, kc_user_id, user_id_provider, user_name_provider):
        try:
            self.keycloak_admin.add_user_social_login(kc_user_id, self.adfs_alias, user_id_provider, user_name_provider)
            self.user_result["keycloak_id_provider_link"]["status"] = "OK"
            self.user_result["keycloak_id_provider_link"]["message"] = "Linked user to ADFS"
        except Exception as e:
            self.user_result["keycloak_id_provider_link"]["status"] = "ERROR"
            self.user_result["keycloak_id_provider_link"]["message"] = str(e)

    def create_myacc_user(self, user_info):
        """
        After add user into Keycloak, add to MyAcc

        :param user_info:
            id, email, Name, Title, Office
        """

        if User.objects.filter(email=user_info["email"]).exists():
            self.user_result["myacc"]["status"] = "WARNING"
            self.user_result["myacc"]["message"] = "MyAcc User Already Exist"
            return

        user_instance = User()

        try:
            user_instance.id = user_info["id"]
            user_instance.email = user_info["email"]
            user_instance.name = user_info["name"]
            user_instance.job = user_info["job"]
            user_instance.address = user_info["address"]
            user_instance.secondary_email = user_info["secondary_email"]
            user_instance.save()
            self.user_result["myacc"]["status"] = "OK"
            self.user_result["myacc"]["message"] = "Created MyAcc User"

        except Exception as e:
            self.user_result["myacc"]["status"] = "ERROR"
            self.user_result["myacc"]["message"] = str(e)

    def add_application_roles(self, user_id):
        """
        Add roles defined into init.

        :param user_id:
        """

        for role in self.roles:
            try:
                user_role = UserRoleWorkspace()
                user_role.company_id = self.company_id
                user_role.role_id = role
                user_role.user_id = user_id
                user_role.self_sign_up = True
                user_role.save()
                self.user_result["myacc_roles"]["status"] = "OK"
                self.user_result["myacc_roles"]["message"] = "Added Roles"
            except Exception as e:
                self.user_result["myacc_roles"]["status"] = "ERROR"
                self.user_result["myacc_roles"]["message"] = str(e)

    @staticmethod
    def read_csv(files):
        """
        Read an CSV file and return into dict

        :param files: path to file
        :return: users info

        """
        with open(files) as file:
            users_info = [{k: v for k, v in row.items()} for row in csv.DictReader(file, skipinitialspace=True)]

        return users_info


ADFS_ALIAS = "adfs-idp-condor"
AD_PATH = "CONDOR\\"
COMPANY_ID = "5b53ffb0-65dd-485e-9be3-4d7357beadef"

IMPORTER = ActiveDirectoryUser(COMPANY_ID, ADFS_ALIAS, AD_PATH)
RESULT = IMPORTER.execute(f"{settings.BASE_DIR}/script/users.csv")
