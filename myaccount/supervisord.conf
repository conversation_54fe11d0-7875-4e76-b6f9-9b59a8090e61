[supervisord]
nodaemon=true

[program:scheduler]
directory=/app
command=/bin/bash -c "python cron_schedulers.py"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
redirect_stderr=true
autostart=true
autorestart=true
startsecs=10
stopwaitsecs = 600
priority=998


[program:celery]
directory=/app
command=/bin/bash -c "celery -A tasks worker -P solo --loglevel=INFO -n worker.%%h"
process_name=%(program_name)s_%(process_num)s
numprocs=1
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
redirect_stderr=true
autostart=true
autorestart=true
startsecs=10
stopwaitsecs = 600
killasgroup=true
priority=998


[program:kafka_worker]
directory=/app
command=/bin/bash -c "python -u kafka_consumer.py"
process_name=%(program_name)s_%(process_num)s
numprocs=1
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
redirect_stderr=true
autostart=true
autorestart=true
startsecs=10
stopwaitsecs=600
killasgroup=true
priority=995
