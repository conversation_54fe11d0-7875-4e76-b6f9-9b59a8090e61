{"swagger": "2.0", "info": {"title": "MyAccount API", "description": "", "termsOfService": "", "contact": {"email": "<EMAIL>"}, "license": {"name": "BSD License"}, "version": "v1"}, "basePath": "/", "consumes": ["application/json"], "produces": ["application/json"], "securityDefinitions": {}, "paths": {"/applications": {"get": {"operationId": "applications_list", "description": "A viewset that provides the standard actions", "parameters": [{"name": "name", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"type": "array", "items": {"$ref": "#/definitions/Application"}}}}, "tags": ["applications"]}, "parameters": []}, "/applications/roles": {"get": {"operationId": "applications_roles_list", "description": "A viewset that provides the standard actions", "parameters": [{"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"type": "array", "items": {"$ref": "#/definitions/ApplicationRole"}}}}, "tags": ["applications/roles"]}, "parameters": []}, "/applications/services": {"get": {"operationId": "applications_services_list", "description": "A viewset that provides the standard actions", "parameters": [{"name": "name", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"type": "array", "items": {"$ref": "#/definitions/ApplicationService"}}}}, "tags": ["applications/services"]}, "parameters": []}, "/applications/{application_id}/roles": {"get": {"operationId": "applications_roles_list", "description": "A viewset that provides the standard actions", "parameters": [{"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"type": "array", "items": {"$ref": "#/definitions/Role"}}}}, "tags": ["applications/roles"]}, "parameters": [{"name": "application_id", "in": "path", "required": true, "type": "string"}]}, "/applications/{application_id}/roles/{id}": {"get": {"operationId": "applications_roles_list", "description": "A viewset that provides the standard actions", "parameters": [{"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"type": "array", "items": {"$ref": "#/definitions/Role"}}}}, "tags": ["applications/roles"]}, "parameters": [{"name": "application_id", "in": "path", "required": true, "type": "string"}, {"name": "id", "in": "path", "required": true, "type": "string"}]}, "/applications/{id}": {"get": {"operationId": "applications_list", "description": "A viewset that provides the standard actions", "parameters": [{"name": "name", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"type": "array", "items": {"$ref": "#/definitions/Application"}}}}, "tags": ["applications"]}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}]}, "/applications/{id}/workspaces": {"get": {"operationId": "applications_workspaces_list", "description": "List all workspaces allowed for logged user that have application ID.", "parameters": [], "responses": {"200": {"description": "", "schema": {"type": "array", "items": {"$ref": "#/definitions/WorkspaceApplication"}}}}, "tags": ["applications/workspaces"]}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}]}, "/auth": {"post": {"operationId": "auth_create", "description": "", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["auth"]}, "parameters": []}, "/auth/reset-password": {"post": {"operationId": "auth_reset-password_create", "description": "", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["auth/reset-password"]}, "parameters": []}, "/billing": {"get": {"operationId": "billing_list", "description": "", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["billing"]}, "parameters": []}, "/companies": {"get": {"operationId": "companies_list", "description": "A viewset that provides the standard actions", "parameters": [{"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"type": "array", "items": {"$ref": "#/definitions/Company"}}}}, "tags": ["companies"]}, "post": {"operationId": "companies_create", "description": "A viewset that provides the standard actions", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Company"}}], "responses": {"201": {"description": "", "schema": {"$ref": "#/definitions/Company"}}}, "tags": ["companies"]}, "parameters": []}, "/companies/{id}": {"get": {"operationId": "companies_read", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/Company"}}}, "tags": ["companies"]}, "put": {"operationId": "companies_update", "description": "A viewset that provides the standard actions", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Company"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/Company"}}}, "tags": ["companies"]}, "patch": {"operationId": "companies_partial_update", "description": "A viewset that provides the standard actions", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Company"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/Company"}}}, "tags": ["companies"]}, "delete": {"operationId": "companies_delete", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"204": {"description": ""}}, "tags": ["companies"]}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}]}, "/health-check": {"get": {"operationId": "health-check_list", "description": "", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["health-check"]}, "parameters": []}, "/job-functions": {"get": {"operationId": "job-functions_list", "description": "", "parameters": [{"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"type": "array", "items": {"$ref": "#/definitions/JobFunction"}}}}, "tags": ["job-functions"]}, "post": {"operationId": "job-functions_create", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/JobFunction"}}], "responses": {"201": {"description": "", "schema": {"$ref": "#/definitions/JobFunction"}}}, "tags": ["job-functions"]}, "parameters": []}, "/job-functions/{id}": {"get": {"operationId": "job-functions_read", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/JobFunction"}}}, "tags": ["job-functions"]}, "put": {"operationId": "job-functions_update", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/JobFunction"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/JobFunction"}}}, "tags": ["job-functions"]}, "patch": {"operationId": "job-functions_partial_update", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/JobFunction"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/JobFunction"}}}, "tags": ["job-functions"]}, "delete": {"operationId": "job-functions_delete", "description": "", "parameters": [], "responses": {"204": {"description": ""}}, "tags": ["job-functions"]}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}]}, "/jobs": {"get": {"operationId": "jobs_list", "description": "", "parameters": [{"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"type": "array", "items": {"$ref": "#/definitions/Job"}}}}, "tags": ["jobs"]}, "post": {"operationId": "jobs_create", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Job"}}], "responses": {"201": {"description": "", "schema": {"$ref": "#/definitions/Job"}}}, "tags": ["jobs"]}, "parameters": []}, "/jobs/{id}": {"get": {"operationId": "jobs_read", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/Job"}}}, "tags": ["jobs"]}, "put": {"operationId": "jobs_update", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Job"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/Job"}}}, "tags": ["jobs"]}, "patch": {"operationId": "jobs_partial_update", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Job"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/Job"}}}, "tags": ["jobs"]}, "delete": {"operationId": "jobs_delete", "description": "", "parameters": [], "responses": {"204": {"description": ""}}, "tags": ["jobs"]}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}]}, "/languages": {"get": {"operationId": "languages_list", "description": "A viewset that provides the standard actions", "parameters": [{"name": "name", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "status", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"type": "array", "items": {"$ref": "#/definitions/LanguagePreference"}}}}, "tags": ["languages"]}, "parameters": []}, "/profiles/areas-of-activity": {"get": {"operationId": "profiles_areas-of-activity_list", "description": "", "parameters": [{"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}, {"name": "page", "in": "query", "description": "A page number within the paginated result set.", "required": false, "type": "integer"}, {"name": "per_page", "in": "query", "description": "Number of results to return per page.", "required": false, "type": "integer"}], "responses": {"200": {"description": "", "schema": {"required": ["count", "results"], "type": "object", "properties": {"count": {"type": "integer"}, "next": {"type": "string", "format": "uri", "x-nullable": true}, "previous": {"type": "string", "format": "uri", "x-nullable": true}, "results": {"type": "array", "items": {"$ref": "#/definitions/AreaOfActivity"}}}}}}, "tags": ["profiles/areas-of-activity"]}, "parameters": []}, "/profiles/directors": {"get": {"operationId": "profiles_directors_list", "description": "", "parameters": [{"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}, {"name": "page", "in": "query", "description": "A page number within the paginated result set.", "required": false, "type": "integer"}, {"name": "per_page", "in": "query", "description": "Number of results to return per page.", "required": false, "type": "integer"}], "responses": {"200": {"description": "", "schema": {"required": ["count", "results"], "type": "object", "properties": {"count": {"type": "integer"}, "next": {"type": "string", "format": "uri", "x-nullable": true}, "previous": {"type": "string", "format": "uri", "x-nullable": true}, "results": {"type": "array", "items": {"$ref": "#/definitions/Director"}}}}}}, "tags": ["profiles/directors"]}, "parameters": []}, "/profiles/managers": {"get": {"operationId": "profiles_managers_list", "description": "", "parameters": [{"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}, {"name": "page", "in": "query", "description": "A page number within the paginated result set.", "required": false, "type": "integer"}, {"name": "per_page", "in": "query", "description": "Number of results to return per page.", "required": false, "type": "integer"}], "responses": {"200": {"description": "", "schema": {"required": ["count", "results"], "type": "object", "properties": {"count": {"type": "integer"}, "next": {"type": "string", "format": "uri", "x-nullable": true}, "previous": {"type": "string", "format": "uri", "x-nullable": true}, "results": {"type": "array", "items": {"$ref": "#/definitions/Manager"}}}}}}, "tags": ["profiles/managers"]}, "parameters": []}, "/services": {"get": {"operationId": "services_list", "description": "A viewset that provides the standard actions", "parameters": [{"name": "name", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "application", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"type": "array", "items": {"$ref": "#/definitions/Service"}}}}, "tags": ["services"]}, "parameters": []}, "/services/{id}/": {"get": {"operationId": "services_list", "description": "A viewset that provides the standard actions", "parameters": [{"name": "name", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "application", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"type": "array", "items": {"$ref": "#/definitions/Service"}}}}, "tags": ["services"]}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}]}, "/swagger-spec/": {"get": {"operationId": "swagger-spec_list", "description": "", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["swagger-spec"]}, "parameters": []}, "/users": {"get": {"operationId": "users_list", "description": "A viewset that provides the standard actions", "parameters": [{"name": "id", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "name", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "nickname", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "email", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "email_verified", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "secondary_email", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "phone", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "avatar", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "gender", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "birthday", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "address", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "country", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "language", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "ein", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "related_user_leader", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "cpf", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "admission_date", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "ethnicity", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "marital_status", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "education", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "hierarchical_level", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "contract_type", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "is_user_integration", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "status", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "time_zone", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "created_date", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "updated_date", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "application_id", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "role_id", "in": "query", "description": "Multiple values may be separated by commas.", "required": false, "type": "string"}, {"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}, {"name": "page", "in": "query", "description": "A page number within the paginated result set.", "required": false, "type": "integer"}, {"name": "per_page", "in": "query", "description": "Number of results to return per page.", "required": false, "type": "integer"}], "responses": {"200": {"description": ""}}, "tags": ["users"]}, "parameters": []}, "/users-roles": {"get": {"operationId": "users-roles_list", "description": "A viewset that provides the standard actions", "parameters": [{"name": "user__name", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "user__email", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "user__status", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "workspace__id", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "role__id", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "role_id", "in": "query", "description": "Multiple values may be separated by commas.", "required": false, "type": "string"}, {"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}, {"name": "page", "in": "query", "description": "A page number within the paginated result set.", "required": false, "type": "integer"}, {"name": "per_page", "in": "query", "description": "Number of results to return per page.", "required": false, "type": "integer"}], "responses": {"200": {"description": "", "schema": {"required": ["count", "results"], "type": "object", "properties": {"count": {"type": "integer"}, "next": {"type": "string", "format": "uri", "x-nullable": true}, "previous": {"type": "string", "format": "uri", "x-nullable": true}, "results": {"type": "array", "items": {"$ref": "#/definitions/UserRoleWorkspaceListFull"}}}}}}, "tags": ["users-roles"]}, "parameters": []}, "/users/info": {"get": {"operationId": "users_info_list", "description": "", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["users/info"]}, "parameters": []}, "/users/public-by-id/{user_id}": {"get": {"operationId": "users_public-by-id_read", "description": "", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["users/public-by-id"]}, "parameters": [{"name": "user_id", "in": "path", "required": true, "type": "string"}]}, "/users/public/{email}": {"get": {"operationId": "users_public_read", "description": "", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["users/public"]}, "parameters": [{"name": "email", "in": "path", "required": true, "type": "string"}]}, "/users/roles": {"post": {"operationId": "users_roles_create", "description": "A viewset that provides the standard actions", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UserRoleWorkspace"}}], "responses": {"201": {"description": "", "schema": {"$ref": "#/definitions/UserRoleWorkspace"}}}, "tags": ["users/roles"]}, "delete": {"operationId": "users_roles_delete", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"204": {"description": ""}}, "tags": ["users/roles"]}, "parameters": []}, "/users/roles/{id}": {"post": {"operationId": "users_roles_create", "description": "A viewset that provides the standard actions", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UserRoleWorkspace"}}], "responses": {"201": {"description": "", "schema": {"$ref": "#/definitions/UserRoleWorkspace"}}}, "tags": ["users/roles"]}, "delete": {"operationId": "users_roles_delete", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"204": {"description": ""}}, "tags": ["users/roles"]}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}]}, "/users/set-password": {"post": {"operationId": "users_set-password_create", "description": "Reset/Change user's password (Keycloak) informing user_id, new password and if this new pass is\ntemporary (need change in the first login).\n\n---\n    {\n        \"user_id\": \"uuid\",\n        \"password\": \"string alfa-numeric\",\n        \"temporary: true/false\n    }", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["users/set-password"]}, "parameters": []}, "/users/set-password/batch": {"post": {"operationId": "users_set-password_batch_create", "description": "Reset/Change user's password (Keycloak) informing a list of users to change (user_id), new password and\nif this new pass is temporary (need change in the first login).\n\n---\n    {\n        \"user_id\": [\"uuid\", \"uuid\", ...],\n        \"password\": \"string alfa-numeric\",\n        \"temporary: true/false\n    }", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["users/set-password/batch"]}, "parameters": []}, "/users/{id}": {"get": {"operationId": "users_read", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["users"]}, "put": {"operationId": "users_update", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["users"]}, "patch": {"operationId": "users_partial_update", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["users"]}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}]}, "/users/{user_id}/avatar": {"post": {"operationId": "users_avatar_create", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["users/avatar"]}, "parameters": [{"name": "user_id", "in": "path", "required": true, "type": "string"}]}, "/users/{user_id}/workspaces": {"get": {"operationId": "users_workspaces_list", "description": "A viewset that provides the standard actions", "parameters": [{"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"type": "array", "items": {"$ref": "#/definitions/Workspace"}}}}, "tags": ["users/workspaces"]}, "post": {"operationId": "users_workspaces_create", "description": "A viewset that provides the standard actions", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Workspace"}}], "responses": {"201": {"description": "", "schema": {"$ref": "#/definitions/Workspace"}}}, "tags": ["users/workspaces"]}, "parameters": [{"name": "user_id", "in": "path", "required": true, "type": "string"}]}, "/users/{user_id}/workspaces/{id}": {"get": {"operationId": "users_workspaces_read", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/Workspace"}}}, "tags": ["users/workspaces"]}, "put": {"operationId": "users_workspaces_update", "description": "A viewset that provides the standard actions", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Workspace"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/Workspace"}}}, "tags": ["users/workspaces"]}, "patch": {"operationId": "users_workspaces_partial_update", "description": "A viewset that provides the standard actions", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Workspace"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/Workspace"}}}, "tags": ["users/workspaces"]}, "delete": {"operationId": "users_workspaces_delete", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"204": {"description": ""}}, "tags": ["users/workspaces"]}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "user_id", "in": "path", "required": true, "type": "string"}]}, "/workspaces": {"get": {"operationId": "workspaces_list", "description": "A viewset that provides the standard actions", "parameters": [{"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"type": "array", "items": {"$ref": "#/definitions/WorkspaceInput"}}}}, "tags": ["workspaces"]}, "post": {"operationId": "workspaces_create", "description": "A viewset that provides the standard actions", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/WorkspaceInput"}}], "responses": {"201": {"description": "", "schema": {"$ref": "#/definitions/WorkspaceInput"}}}, "tags": ["workspaces"]}, "parameters": []}, "/workspaces/filter-settings": {"get": {"operationId": "workspaces_filter-settings_list", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"type": "array", "items": {"$ref": "#/definitions/WorkspaceFilterSetting"}}}}, "tags": ["workspaces/filter-settings"]}, "parameters": []}, "/workspaces/filter-settings/{id}": {"patch": {"operationId": "workspaces_filter-settings_partial_update", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/WorkspaceFilterSetting"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/WorkspaceFilterSetting"}}}, "tags": ["workspaces/filter-settings"]}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}]}, "/workspaces/gamification/ranking": {"get": {"operationId": "workspaces_gamification_ranking_list", "description": "A viewset that provides the standard actions to enable and disable gamification and gamification's ranking.", "parameters": [], "responses": {"200": {"description": "", "schema": {"type": "array", "items": {"$ref": "#/definitions/GamificationWorkspaceList"}}}}, "tags": ["workspaces/gamification/ranking"]}, "parameters": []}, "/workspaces/gamification/ranking/{ranking_id}": {"put": {"operationId": "workspaces_gamification_ranking_update", "description": "A viewset that provides the standard actions to enable and disable gamification and gamification's ranking.", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/GamificationWorkspaceList"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/GamificationWorkspaceList"}}}, "tags": ["workspaces/gamification/ranking"]}, "parameters": [{"name": "ranking_id", "in": "path", "required": true, "type": "string"}]}, "/workspaces/hash-login-url/{hash_id}": {"get": {"operationId": "workspaces_hash-login-url_read", "description": "", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["workspaces/hash-login-url"]}, "parameters": [{"name": "hash_id", "in": "path", "required": true, "type": "string"}]}, "/workspaces/{id}": {"get": {"operationId": "workspaces_read", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/WorkspaceInput"}}}, "tags": ["workspaces"]}, "put": {"operationId": "workspaces_update", "description": "A viewset that provides the standard actions", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/WorkspaceInput"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/WorkspaceInput"}}}, "tags": ["workspaces"]}, "patch": {"operationId": "workspaces_partial_update", "description": "A viewset that provides the standard actions", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/WorkspaceInput"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/WorkspaceInput"}}}, "tags": ["workspaces"]}, "delete": {"operationId": "workspaces_delete", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"204": {"description": ""}}, "tags": ["workspaces"]}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}]}, "/workspaces/{id}/theme": {"get": {"operationId": "workspaces_theme_list", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["workspaces/theme"]}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}]}, "/workspaces/{workspace_id}/icon": {"post": {"operationId": "workspaces_icon_create", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["workspaces/icon"]}, "parameters": [{"name": "workspace_id", "in": "path", "required": true, "type": "string"}]}, "/workspaces/{workspace_id}/icon-svg": {"post": {"operationId": "workspaces_icon-svg_create", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["workspaces/icon-svg"]}, "parameters": [{"name": "workspace_id", "in": "path", "required": true, "type": "string"}]}, "/workspaces/{workspace_id}/logo": {"post": {"operationId": "workspaces_logo_create", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["workspaces/logo"]}, "parameters": [{"name": "workspace_id", "in": "path", "required": true, "type": "string"}]}, "/workspaces/{workspace_id}/services": {"get": {"operationId": "workspaces_services_list", "description": "A viewset that provides the standard actions", "parameters": [{"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"type": "array", "items": {"$ref": "#/definitions/ServiceWorkspaceList"}}}}, "tags": ["workspaces/services"]}, "parameters": [{"name": "workspace_id", "in": "path", "required": true, "type": "string"}]}, "/workspaces/{workspace_id}/services/{service_id}": {"post": {"operationId": "workspaces_services_create", "description": "A viewset that provides the standard actions", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ServiceWorkspaceList"}}], "responses": {"201": {"description": "", "schema": {"$ref": "#/definitions/ServiceWorkspaceList"}}}, "tags": ["workspaces/services"]}, "delete": {"operationId": "workspaces_services_delete", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"204": {"description": ""}}, "tags": ["workspaces/services"]}, "parameters": [{"name": "service_id", "in": "path", "required": true, "type": "string"}, {"name": "workspace_id", "in": "path", "required": true, "type": "string"}]}, "/workspaces/{workspace_id}/users": {"get": {"operationId": "workspaces_users_list", "description": "A viewset that provides the standard actions", "parameters": [{"name": "id", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "name", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "nickname", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "email", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "email_verified", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "secondary_email", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "phone", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "avatar", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "gender", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "birthday", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "address", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "country", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "language", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "ein", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "related_user_leader", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "cpf", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "admission_date", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "ethnicity", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "marital_status", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "education", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "hierarchical_level", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "contract_type", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "is_user_integration", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "status", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "time_zone", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "created_date", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "updated_date", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "application_id", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "role_id", "in": "query", "description": "Multiple values may be separated by commas.", "required": false, "type": "string"}, {"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}, {"name": "page", "in": "query", "description": "A page number within the paginated result set.", "required": false, "type": "integer"}, {"name": "per_page", "in": "query", "description": "Number of results to return per page.", "required": false, "type": "integer"}], "responses": {"200": {"description": ""}}, "tags": ["workspaces/users"]}, "post": {"operationId": "workspaces_users_create", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["workspaces/users"]}, "parameters": [{"name": "workspace_id", "in": "path", "required": true, "type": "string"}]}, "/workspaces/{workspace_id}/users/import": {"post": {"operationId": "workspaces_users_import_create", "description": "File xls could contain information to connect the user to one Identity provider (last 3 columns). These columns\nis identity_provider_alias, provider_user_id, provider_username and if are filled the field self_signup will be\nset to True.\n\nThe field self_signup control if the user will receive onboard communications and Keycloak will set the password\nto temporary. In case of user have an Identity Provider is not necessary send onboarding e-mail or reset pass.\n\n:param request: need receive file (form data) and roles (string if roles uuid and separated by comma)\n:return: errors (users with problem), imported (users ok)", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["workspaces/users/import"]}, "parameters": [{"name": "workspace_id", "in": "path", "required": true, "type": "string"}]}, "/workspaces/{workspace_id}/users/invite": {"post": {"operationId": "workspaces_users_invite_create", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["workspaces/users/invite"]}, "parameters": [{"name": "workspace_id", "in": "path", "required": true, "type": "string"}]}, "/workspaces/{workspace_id}/users/profile": {"get": {"operationId": "workspaces_users_profile_list", "description": "A viewset that provides the standard actions", "parameters": [{"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}, {"name": "page", "in": "query", "description": "A page number within the paginated result set.", "required": false, "type": "integer"}, {"name": "per_page", "in": "query", "description": "Number of results to return per page.", "required": false, "type": "integer"}], "responses": {"200": {"description": ""}}, "tags": ["workspaces/users/profile"]}, "post": {"operationId": "workspaces_users_profile_create", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["workspaces/users/profile"]}, "parameters": [{"name": "workspace_id", "in": "path", "required": true, "type": "string"}]}, "/workspaces/{workspace_id}/users/profile/{id}": {"get": {"operationId": "workspaces_users_profile_read", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["workspaces/users/profile"]}, "put": {"operationId": "workspaces_users_profile_update", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["workspaces/users/profile"]}, "patch": {"operationId": "workspaces_users_profile_partial_update", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["workspaces/users/profile"]}, "delete": {"operationId": "workspaces_users_profile_delete", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"204": {"description": ""}}, "tags": ["workspaces/users/profile"]}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "workspace_id", "in": "path", "required": true, "type": "string"}]}, "/workspaces/{workspace_id}/users/public": {"get": {"operationId": "workspaces_users_public_list", "description": "A viewset that provides the list action", "parameters": [{"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}, {"name": "page", "in": "query", "description": "A page number within the paginated result set.", "required": false, "type": "integer"}, {"name": "per_page", "in": "query", "description": "Number of results to return per page.", "required": false, "type": "integer"}], "responses": {"200": {"description": "", "schema": {"required": ["count", "results"], "type": "object", "properties": {"count": {"type": "integer"}, "next": {"type": "string", "format": "uri", "x-nullable": true}, "previous": {"type": "string", "format": "uri", "x-nullable": true}, "results": {"type": "array", "items": {"$ref": "#/definitions/PublicProfile"}}}}}}, "tags": ["workspaces/users/public"]}, "parameters": [{"name": "workspace_id", "in": "path", "required": true, "type": "string"}]}, "/workspaces/{workspace_id}/users/sign-up": {"post": {"operationId": "workspaces_users_sign-up_create", "description": "A viewset that provides the standard actions", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/User"}}], "responses": {"201": {"description": "", "schema": {"$ref": "#/definitions/User"}}}, "tags": ["workspaces/users/sign-up"]}, "parameters": [{"name": "workspace_id", "in": "path", "required": true, "type": "string"}]}, "/workspaces/{workspace_id}/users/{id}": {"get": {"operationId": "workspaces_users_read", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["workspaces/users"]}, "put": {"operationId": "workspaces_users_update", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["workspaces/users"]}, "patch": {"operationId": "workspaces_users_partial_update", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["workspaces/users"]}, "delete": {"operationId": "workspaces_users_delete", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"204": {"description": ""}}, "tags": ["workspaces/users"]}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "workspace_id", "in": "path", "required": true, "type": "string"}]}}, "definitions": {"Application": {"required": ["name"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "minLength": 1}, "description": {"title": "Description", "type": "string", "x-nullable": true}, "status": {"title": "Status", "type": "boolean"}, "created_date": {"title": "Created Date", "type": "string", "format": "date-time", "readOnly": true}, "updated_date": {"title": "Updated Date", "type": "string", "format": "date-time", "readOnly": true}}}, "ApplicationRole": {"required": ["name"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "roles": {"title": "Roles", "type": "string", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "minLength": 1}, "description": {"title": "Description", "type": "string", "x-nullable": true}, "status": {"title": "Status", "type": "boolean"}, "created_date": {"title": "Created Date", "type": "string", "format": "date-time", "readOnly": true}, "updated_date": {"title": "Updated Date", "type": "string", "format": "date-time", "readOnly": true}}}, "ApplicationService": {"required": ["name"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "services": {"title": "Services", "type": "string", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "minLength": 1}, "description": {"title": "Description", "type": "string", "x-nullable": true}, "status": {"title": "Status", "type": "boolean"}, "created_date": {"title": "Created Date", "type": "string", "format": "date-time", "readOnly": true}, "updated_date": {"title": "Updated Date", "type": "string", "format": "date-time", "readOnly": true}}}, "Role": {"required": ["key", "application"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "x-nullable": true}, "key": {"title": "Key", "type": "string", "maxLength": 200, "minLength": 1}, "description": {"title": "Description", "type": "string", "x-nullable": true}, "status": {"title": "Status", "type": "boolean"}, "created_date": {"title": "Created Date", "type": "string", "format": "date-time", "readOnly": true}, "updated_date": {"title": "Updated Date", "type": "string", "format": "date-time", "readOnly": true}, "application": {"title": "Application", "type": "string", "format": "uuid"}}}, "WorkspaceApplication": {"required": ["name"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "roles": {"title": "Roles", "type": "string", "readOnly": true}, "services": {"title": "Services", "type": "string", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "minLength": 1}, "duns_number": {"title": "DUNS Number", "type": "string", "maxLength": 200, "x-nullable": true}, "doc_number": {"title": "DOCS Number", "type": "string", "maxLength": 200, "x-nullable": true}, "description": {"title": "Description", "type": "string", "x-nullable": true}, "status": {"title": "Status", "type": "boolean"}, "hash_id": {"title": "Hash id", "type": "string", "maxLength": 125, "x-nullable": true}, "address": {"title": "Address", "type": "string", "x-nullable": true}, "city": {"title": "City", "type": "string", "maxLength": 200, "x-nullable": true}, "state": {"title": "State", "type": "string", "maxLength": 200, "x-nullable": true}, "post_code": {"title": "Post Code", "type": "string", "maxLength": 200, "x-nullable": true}, "country": {"title": "Country", "type": "string", "maxLength": 200, "x-nullable": true}, "icon_url": {"title": "Icon URL", "type": "string", "x-nullable": true}, "icon_svg_url": {"title": "Logo SVG URL", "type": "string", "x-nullable": true}, "logo_url": {"title": "Logo URL", "type": "string", "x-nullable": true}, "theme_id": {"title": "Theme Id", "type": "string", "x-nullable": true}, "theme_dark": {"title": "Theme Dark Active", "type": "boolean"}, "enable_email_notifications": {"title": "Enable email notifications", "type": "boolean"}, "created_date": {"title": "Created Date", "type": "string", "format": "date-time", "readOnly": true}, "updated_date": {"title": "Updated Date", "type": "string", "format": "date-time", "readOnly": true}, "logout_url": {"title": "Logout URL", "type": "string", "x-nullable": true}, "custom_color": {"title": "Custom Color", "type": "string", "maxLength": 16, "x-nullable": true}, "custom_login_url": {"title": "Custom Login URL", "type": "string", "x-nullable": true}, "notify_slack": {"title": "Notify Slack", "type": "boolean"}, "notify_teams": {"title": "Notify Teams", "type": "boolean"}, "alura_integration_active": {"title": "Alura Integration Active", "type": "boolean"}, "allow_list_public_categories": {"title": "Allow List Public Categories", "type": "boolean"}, "allow_list_public_channel": {"title": "Allow List Public Channel", "type": "boolean"}, "allow_create_public_channel": {"title": "Allow Create Public Channel", "type": "boolean"}, "allow_list_paid_channel": {"title": "Allow List Paid Channel", "type": "boolean"}, "allow_create_paid_channel": {"title": "Allow Create Paid Channel", "type": "boolean"}, "need_approve_channel": {"title": "Need Approve Channel", "type": "boolean"}, "allow_list_public_mission": {"title": "Allow List Public Mission", "type": "boolean"}, "allow_create_public_mission": {"title": "Allow Create Public Mission", "type": "boolean"}, "allow_list_paid_mission": {"title": "Allow List Paid Mission", "type": "boolean"}, "allow_create_paid_mission": {"title": "Allow Create Paid Mission", "type": "boolean"}, "need_approve_mission": {"title": "Need Approve Mission", "type": "boolean"}, "enrollment_goal_duration_days": {"title": "Enrollment Goal Duration", "type": "integer", "maximum": **********, "minimum": -**********}, "min_performance_certificate": {"title": "Minimum Performance Certificate", "type": "number", "maximum": 1.0, "minimum": 0.0}, "default_federated_identity_provider_alias": {"title": "Default Federated Identity Provider Alias", "type": "string", "maxLength": 100, "minLength": 1, "x-nullable": true}, "block_reenrollment": {"title": "Block re-enrollment", "type": "boolean"}, "company": {"required": ["name"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "minLength": 1}, "description": {"title": "Description", "type": "string", "x-nullable": true}, "status": {"title": "Status", "type": "boolean"}, "address": {"title": "Address", "type": "string", "x-nullable": true}, "city": {"title": "City", "type": "string", "maxLength": 200, "x-nullable": true}, "state": {"title": "State", "type": "string", "maxLength": 200, "x-nullable": true}, "post_code": {"title": "Post Code", "type": "string", "maxLength": 200, "x-nullable": true}, "country": {"title": "Country", "type": "string", "maxLength": 200, "x-nullable": true}, "icon_url": {"title": "Icon URL", "type": "string", "x-nullable": true}, "created_date": {"title": "Created Date", "type": "string", "format": "date-time", "readOnly": true}, "updated_date": {"title": "Updated Date", "type": "string", "format": "date-time", "readOnly": true}}, "readOnly": true}}}, "WorkspaceSimple": {"title": "Workspace", "required": ["name"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "minLength": 1}, "icon_url": {"title": "Icon URL", "type": "string", "x-nullable": true}, "logo_url": {"title": "Logo URL", "type": "string", "x-nullable": true}}}, "Company": {"required": ["workspace", "name"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "workspace": {"$ref": "#/definitions/WorkspaceSimple"}, "name": {"title": "Name", "type": "string", "maxLength": 200, "minLength": 1}, "description": {"title": "Description", "type": "string", "x-nullable": true}, "status": {"title": "Status", "type": "boolean"}, "address": {"title": "Address", "type": "string", "x-nullable": true}, "city": {"title": "City", "type": "string", "maxLength": 200, "x-nullable": true}, "state": {"title": "State", "type": "string", "maxLength": 200, "x-nullable": true}, "post_code": {"title": "Post Code", "type": "string", "maxLength": 200, "x-nullable": true}, "country": {"title": "Country", "type": "string", "maxLength": 200, "x-nullable": true}, "icon_url": {"title": "Icon URL", "type": "string", "x-nullable": true}, "created_date": {"title": "Created Date", "type": "string", "format": "date-time", "readOnly": true}, "updated_date": {"title": "Updated Date", "type": "string", "format": "date-time", "readOnly": true}}}, "JobFunction": {"required": ["name"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "name": {"title": "Job Function", "type": "string", "maxLength": 200}, "created_date": {"title": "Created Date", "type": "string", "format": "date-time", "readOnly": true}, "updated_date": {"title": "Updated Date", "type": "string", "format": "date-time", "readOnly": true}, "workspace": {"title": "Workspace", "type": "string", "format": "uuid", "readOnly": true}}}, "Job": {"required": ["name"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "name": {"title": "Job", "type": "string", "maxLength": 200}, "created_date": {"title": "Created Date", "type": "string", "format": "date-time", "readOnly": true}, "updated_date": {"title": "Updated Date", "type": "string", "format": "date-time", "readOnly": true}, "workspace": {"title": "Workspace", "type": "string", "format": "uuid", "readOnly": true}}}, "LanguagePreference": {"required": ["name"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "minLength": 1}, "status": {"title": "Status", "type": "boolean"}, "created_date": {"title": "Created Date", "type": "string", "format": "date-time", "readOnly": true}, "updated_date": {"title": "Updated Date", "type": "string", "format": "date-time", "readOnly": true}}}, "AreaOfActivity": {"type": "object", "properties": {"area_of_activity": {"title": "Area of activity.", "type": "string", "maxLength": 300, "x-nullable": true}}}, "Director": {"type": "object", "properties": {}}, "Manager": {"type": "object", "properties": {"manager": {"title": "Manager", "type": "string", "maxLength": 200, "x-nullable": true}}}, "ApplicationSimple": {"title": "Application", "required": ["name"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "minLength": 1}}}, "Service": {"required": ["application", "name"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "application": {"$ref": "#/definitions/ApplicationSimple"}, "name": {"title": "Name", "type": "string", "maxLength": 200, "minLength": 1}, "description": {"title": "Description", "type": "string", "x-nullable": true}, "status": {"title": "Status", "type": "boolean"}, "created_date": {"title": "Created Date", "type": "string", "format": "date-time", "readOnly": true}, "updated_date": {"title": "Updated Date", "type": "string", "format": "date-time", "readOnly": true}}}, "LanguageSimple": {"title": "Language", "required": ["name"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "minLength": 1}, "status": {"title": "Status", "type": "boolean"}}}, "PublicProfile": {"title": "User", "required": ["language"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "x-nullable": true}, "email": {"title": "Email", "type": "string", "format": "email", "readOnly": true, "minLength": 1}, "phone": {"title": "Phone", "type": "string", "pattern": "^\\+?1?\\d{9,15}$", "maxLength": 15, "x-nullable": true}, "ein": {"title": "Employer Identification Number", "type": "string", "maxLength": 100, "x-nullable": true}, "country": {"title": "Country", "type": "string", "maxLength": 20, "x-nullable": true}, "language": {"$ref": "#/definitions/LanguageSimple"}, "avatar": {"title": "Avatar", "type": "string", "x-nullable": true}}}, "RoleSimple": {"title": "Role", "required": ["key", "application"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "x-nullable": true}, "key": {"title": "Key", "type": "string", "maxLength": 200, "minLength": 1}, "application": {"$ref": "#/definitions/ApplicationSimple"}}}, "UserRoleWorkspaceListFull": {"required": ["user", "role", "workspace"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "user": {"$ref": "#/definitions/PublicProfile"}, "role": {"$ref": "#/definitions/RoleSimple"}, "workspace": {"$ref": "#/definitions/WorkspaceSimple"}}}, "UserRoleWorkspace": {"required": ["user", "role", "workspace"], "type": "object", "properties": {"user": {"title": "User", "type": "string", "format": "uuid"}, "role": {"title": "Role", "type": "string", "format": "uuid"}, "workspace": {"title": "Workspace", "type": "string", "format": "uuid"}, "self_sign_up": {"title": "Self Sign Up", "type": "boolean"}}}, "Workspace": {"required": ["name"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "roles": {"title": "Roles", "type": "string", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "minLength": 1}, "duns_number": {"title": "DUNS Number", "type": "string", "maxLength": 200, "x-nullable": true}, "doc_number": {"title": "DOCS Number", "type": "string", "maxLength": 200, "x-nullable": true}, "description": {"title": "Description", "type": "string", "x-nullable": true}, "status": {"title": "Status", "type": "boolean"}, "hash_id": {"title": "Hash id", "type": "string", "maxLength": 125, "x-nullable": true}, "address": {"title": "Address", "type": "string", "x-nullable": true}, "city": {"title": "City", "type": "string", "maxLength": 200, "x-nullable": true}, "state": {"title": "State", "type": "string", "maxLength": 200, "x-nullable": true}, "post_code": {"title": "Post Code", "type": "string", "maxLength": 200, "x-nullable": true}, "country": {"title": "Country", "type": "string", "maxLength": 200, "x-nullable": true}, "icon_url": {"title": "Icon URL", "type": "string", "x-nullable": true}, "icon_svg_url": {"title": "Logo SVG URL", "type": "string", "x-nullable": true}, "logo_url": {"title": "Logo URL", "type": "string", "x-nullable": true}, "theme_id": {"title": "Theme Id", "type": "string", "x-nullable": true}, "theme_dark": {"title": "Theme Dark Active", "type": "boolean"}, "enable_email_notifications": {"title": "Enable email notifications", "type": "boolean"}, "created_date": {"title": "Created Date", "type": "string", "format": "date-time", "readOnly": true}, "updated_date": {"title": "Updated Date", "type": "string", "format": "date-time", "readOnly": true}, "logout_url": {"title": "Logout URL", "type": "string", "x-nullable": true}, "custom_color": {"title": "Custom Color", "type": "string", "maxLength": 16, "x-nullable": true}, "custom_login_url": {"title": "Custom Login URL", "type": "string", "x-nullable": true}, "notify_slack": {"title": "Notify Slack", "type": "boolean"}, "notify_teams": {"title": "Notify Teams", "type": "boolean"}, "alura_integration_active": {"title": "Alura Integration Active", "type": "boolean"}, "allow_list_public_categories": {"title": "Allow List Public Categories", "type": "boolean"}, "allow_list_public_channel": {"title": "Allow List Public Channel", "type": "boolean"}, "allow_create_public_channel": {"title": "Allow Create Public Channel", "type": "boolean"}, "allow_list_paid_channel": {"title": "Allow List Paid Channel", "type": "boolean"}, "allow_create_paid_channel": {"title": "Allow Create Paid Channel", "type": "boolean"}, "need_approve_channel": {"title": "Need Approve Channel", "type": "boolean"}, "allow_list_public_mission": {"title": "Allow List Public Mission", "type": "boolean"}, "allow_create_public_mission": {"title": "Allow Create Public Mission", "type": "boolean"}, "allow_list_paid_mission": {"title": "Allow List Paid Mission", "type": "boolean"}, "allow_create_paid_mission": {"title": "Allow Create Paid Mission", "type": "boolean"}, "need_approve_mission": {"title": "Need Approve Mission", "type": "boolean"}, "enrollment_goal_duration_days": {"title": "Enrollment Goal Duration", "type": "integer", "maximum": **********, "minimum": -**********}, "min_performance_certificate": {"title": "Minimum Performance Certificate", "type": "number", "maximum": 1.0, "minimum": 0.0}, "default_federated_identity_provider_alias": {"title": "Default Federated Identity Provider Alias", "type": "string", "maxLength": 100, "minLength": 1, "x-nullable": true}, "block_reenrollment": {"title": "Block re-enrollment", "type": "boolean"}, "company": {"required": ["name"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "minLength": 1}, "description": {"title": "Description", "type": "string", "x-nullable": true}, "status": {"title": "Status", "type": "boolean"}, "address": {"title": "Address", "type": "string", "x-nullable": true}, "city": {"title": "City", "type": "string", "maxLength": 200, "x-nullable": true}, "state": {"title": "State", "type": "string", "maxLength": 200, "x-nullable": true}, "post_code": {"title": "Post Code", "type": "string", "maxLength": 200, "x-nullable": true}, "country": {"title": "Country", "type": "string", "maxLength": 200, "x-nullable": true}, "icon_url": {"title": "Icon URL", "type": "string", "x-nullable": true}, "created_date": {"title": "Created Date", "type": "string", "format": "date-time", "readOnly": true}, "updated_date": {"title": "Updated Date", "type": "string", "format": "date-time", "readOnly": true}}, "readOnly": true}}}, "WorkspaceInput": {"required": ["name"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "company": {"title": "Company", "type": "string", "format": "uuid"}, "name": {"title": "Name", "type": "string", "maxLength": 200, "minLength": 1}, "duns_number": {"title": "DUNS Number", "type": "string", "maxLength": 200, "x-nullable": true}, "doc_number": {"title": "DOCS Number", "type": "string", "maxLength": 200, "x-nullable": true}, "description": {"title": "Description", "type": "string", "x-nullable": true}, "status": {"title": "Status", "type": "boolean"}, "hash_id": {"title": "Hash id", "type": "string", "maxLength": 125, "x-nullable": true}, "address": {"title": "Address", "type": "string", "x-nullable": true}, "city": {"title": "City", "type": "string", "maxLength": 200, "x-nullable": true}, "state": {"title": "State", "type": "string", "maxLength": 200, "x-nullable": true}, "post_code": {"title": "Post Code", "type": "string", "maxLength": 200, "x-nullable": true}, "country": {"title": "Country", "type": "string", "maxLength": 200, "x-nullable": true}, "icon_url": {"title": "Icon URL", "type": "string", "x-nullable": true}, "icon_svg_url": {"title": "Logo SVG URL", "type": "string", "x-nullable": true}, "logo_url": {"title": "Logo URL", "type": "string", "x-nullable": true}, "theme_id": {"title": "Theme Id", "type": "string", "x-nullable": true}, "theme_dark": {"title": "Theme Dark Active", "type": "boolean"}, "use_own_smtp": {"title": "Use own smtp", "type": "boolean"}, "enable_email_notifications": {"title": "Enable email notifications", "type": "boolean"}, "smtp_host": {"title": "Smtp Host", "type": "string", "maxLength": 400, "x-nullable": true}, "smtp_port": {"title": "Smtp Port", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "smtp_secure": {"title": "Smtp Secure", "type": "boolean", "x-nullable": true}, "smtp_auth_user": {"title": "Smtp Auth User", "type": "string", "maxLength": 400, "x-nullable": true}, "smtp_auth_pass": {"title": "Smtp Auth User", "type": "string", "maxLength": 1000, "x-nullable": true}, "smtp_sender_email": {"title": "Smtp Sender Email", "type": "string", "maxLength": 400, "x-nullable": true}, "smtp_reject_unauthorized": {"title": "Smtp Reject Unauthorized", "type": "boolean", "x-nullable": true}, "created_date": {"title": "Created Date", "type": "string", "format": "date-time", "readOnly": true}, "updated_date": {"title": "Updated Date", "type": "string", "format": "date-time", "readOnly": true}, "logout_url": {"title": "Logout URL", "type": "string", "x-nullable": true}, "custom_color": {"title": "Custom Color", "type": "string", "maxLength": 16, "x-nullable": true}, "custom_login_url": {"title": "Custom Login URL", "type": "string", "x-nullable": true}, "notify_slack": {"title": "Notify Slack", "type": "boolean"}, "notify_teams": {"title": "Notify Teams", "type": "boolean"}, "alura_integration_active": {"title": "Alura Integration Active", "type": "boolean"}, "allow_list_public_categories": {"title": "Allow List Public Categories", "type": "boolean"}, "allow_list_public_channel": {"title": "Allow List Public Channel", "type": "boolean"}, "allow_create_public_channel": {"title": "Allow Create Public Channel", "type": "boolean"}, "allow_list_paid_channel": {"title": "Allow List Paid Channel", "type": "boolean"}, "allow_create_paid_channel": {"title": "Allow Create Paid Channel", "type": "boolean"}, "need_approve_channel": {"title": "Need Approve Channel", "type": "boolean"}, "allow_list_public_mission": {"title": "Allow List Public Mission", "type": "boolean"}, "allow_create_public_mission": {"title": "Allow Create Public Mission", "type": "boolean"}, "allow_list_paid_mission": {"title": "Allow List Paid Mission", "type": "boolean"}, "allow_create_paid_mission": {"title": "Allow Create Paid Mission", "type": "boolean"}, "need_approve_mission": {"title": "Need Approve Mission", "type": "boolean"}, "enrollment_goal_duration_days": {"title": "Enrollment Goal Duration", "type": "integer", "maximum": **********, "minimum": -**********}, "min_performance_certificate": {"title": "Minimum Performance Certificate", "type": "number", "maximum": 1.0, "minimum": 0.0}, "default_federated_identity_provider_alias": {"title": "Default Federated Identity Provider Alias", "type": "string", "maxLength": 100, "minLength": 1, "x-nullable": true}, "block_reenrollment": {"title": "Block re-enrollment", "type": "boolean"}}}, "WorkspaceFilterSetting": {"required": ["workspace", "enrollment_status_filter"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "status_code": {"title": "Status code", "type": "string", "readOnly": true, "minLength": 1}, "is_enabled": {"title": "Is enabled", "type": "boolean"}, "created_at": {"title": "Created at", "type": "string", "format": "date-time", "readOnly": true}, "updated_at": {"title": "Updated at", "type": "string", "format": "date-time", "readOnly": true}, "workspace": {"title": "Workspace", "type": "string", "format": "uuid"}, "enrollment_status_filter": {"title": "Enrollment status filter", "type": "string", "format": "uuid"}}}, "GamificationWorkspaceList": {"required": ["ranking"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "ranking": {"title": "Ranking", "type": "string", "enum": ["general", "multiplier", "director", "manager", "leader", "activity_area"]}, "status": {"title": "Status", "type": "boolean"}, "can_enable": {"title": "Can enable", "type": "string", "readOnly": true}}}, "ServiceSimple": {"title": "Service", "required": ["name"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "minLength": 1}}}, "ServiceWorkspaceList": {"required": ["service"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "service": {"$ref": "#/definitions/ServiceSimple"}, "status": {"title": "Status", "type": "boolean"}}}, "User": {"type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "password": {"title": "Password", "type": "string", "minLength": 1}, "roles": {"title": "Roles", "type": "string", "readOnly": true}, "profiles": {"title": "Profiles", "type": "string", "readOnly": true}, "created": {"title": "Created", "type": "string", "readOnly": true}, "job": {"title": "Job", "type": "string", "readOnly": true}, "job_id": {"title": "Job id", "type": "string", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "x-nullable": true}, "nickname": {"title": "Nickname", "type": "string", "maxLength": 200, "x-nullable": true}, "email": {"title": "Email", "type": "string", "format": "email", "readOnly": true, "minLength": 1}, "email_verified": {"title": "<PERSON><PERSON>", "type": "boolean"}, "secondary_email": {"title": "Secondary Email", "type": "string", "format": "email", "maxLength": 200, "x-nullable": true}, "phone": {"title": "Phone", "type": "string", "pattern": "^\\+?1?\\d{9,15}$", "maxLength": 15, "x-nullable": true}, "avatar": {"title": "Avatar", "type": "string", "x-nullable": true}, "gender": {"title": "Gender", "type": "string", "enum": ["FEMALE", "MALE", "OTHER"], "x-nullable": true}, "birthday": {"title": "Birthday", "type": "string", "format": "date", "x-nullable": true}, "address": {"title": "Address", "type": "string", "x-nullable": true}, "country": {"title": "Country", "type": "string", "maxLength": 20, "x-nullable": true}, "ein": {"title": "Employer Identification Number", "type": "string", "maxLength": 100, "x-nullable": true}, "cpf": {"title": "CPF", "type": "string", "maxLength": 15, "x-nullable": true}, "admission_date": {"title": "Admission Date", "type": "string", "format": "date", "x-nullable": true}, "ethnicity": {"title": "Ethnicity", "type": "string", "maxLength": 100, "x-nullable": true}, "marital_status": {"title": "Marital Status", "type": "string", "maxLength": 100, "x-nullable": true}, "education": {"title": "Education Level", "type": "string", "maxLength": 100, "x-nullable": true}, "hierarchical_level": {"title": "Hierarchical Level", "type": "string", "maxLength": 100, "x-nullable": true}, "contract_type": {"title": "Contract Type", "type": "string", "maxLength": 100, "x-nullable": true}, "is_user_integration": {"title": "Is User Integration", "type": "boolean"}, "status": {"title": "Status", "type": "boolean"}, "time_zone": {"title": "Time Zone", "type": "string", "maxLength": 200, "minLength": 1}, "created_date": {"title": "Created Date", "type": "string", "format": "date-time", "readOnly": true}, "updated_date": {"title": "Updated Date", "type": "string", "format": "date-time", "readOnly": true}, "language": {"required": ["name"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "minLength": 1}, "status": {"title": "Status", "type": "boolean"}, "created_date": {"title": "Created Date", "type": "string", "format": "date-time", "readOnly": true}, "updated_date": {"title": "Updated Date", "type": "string", "format": "date-time", "readOnly": true}}, "readOnly": true}, "related_user_leader": {"type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "x-nullable": true}, "nickname": {"title": "Nickname", "type": "string", "maxLength": 200, "x-nullable": true}, "email": {"title": "Email", "type": "string", "format": "email", "readOnly": true, "minLength": 1}, "email_verified": {"title": "<PERSON><PERSON>", "type": "boolean"}, "secondary_email": {"title": "Secondary Email", "type": "string", "format": "email", "maxLength": 200, "x-nullable": true}, "phone": {"title": "Phone", "type": "string", "pattern": "^\\+?1?\\d{9,15}$", "maxLength": 15, "x-nullable": true}, "avatar": {"title": "Avatar", "type": "string", "x-nullable": true}, "gender": {"title": "Gender", "type": "string", "enum": ["FEMALE", "MALE", "OTHER"], "x-nullable": true}, "birthday": {"title": "Birthday", "type": "string", "format": "date", "x-nullable": true}, "address": {"title": "Address", "type": "string", "x-nullable": true}, "country": {"title": "Country", "type": "string", "maxLength": 20, "x-nullable": true}, "ein": {"title": "Employer Identification Number", "type": "string", "maxLength": 100, "x-nullable": true}, "cpf": {"title": "CPF", "type": "string", "maxLength": 15, "x-nullable": true}, "admission_date": {"title": "Admission Date", "type": "string", "format": "date", "x-nullable": true}, "ethnicity": {"title": "Ethnicity", "type": "string", "maxLength": 100, "x-nullable": true}, "marital_status": {"title": "Marital Status", "type": "string", "maxLength": 100, "x-nullable": true}, "education": {"title": "Education Level", "type": "string", "maxLength": 100, "x-nullable": true}, "hierarchical_level": {"title": "Hierarchical Level", "type": "string", "maxLength": 100, "x-nullable": true}, "contract_type": {"title": "Contract Type", "type": "string", "maxLength": 100, "x-nullable": true}, "is_user_integration": {"title": "Is User Integration", "type": "boolean"}, "status": {"title": "Status", "type": "boolean"}, "time_zone": {"title": "Time Zone", "type": "string", "maxLength": 200, "minLength": 1}, "created_date": {"title": "Created Date", "type": "string", "format": "date-time", "readOnly": true}, "updated_date": {"title": "Updated Date", "type": "string", "format": "date-time", "readOnly": true}, "language": {"title": "Language", "type": "string", "format": "uuid", "x-nullable": true}, "related_user_leader": {"title": "Related User Leader", "type": "string", "format": "uuid", "x-nullable": true}}, "readOnly": true}}}}}