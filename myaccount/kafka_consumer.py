import os
import socket
import traceback

import django
import elasticapm
from config import settings
from config.settings import KAFKA_WORKER_POLL_TIMEOUT, WORKSPACE_USER_TOPICS
from custom.discord_webhook_logger import DiscordWebhookLogger
from kafka_worker.user_grpc_client import UserServiceGrpcClient
from kafka_worker.workspace_user_topics import WorkspaceUserTopic

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

import confluent_kafka as kafka  # noqa: E402
from common.services.import_user_service import ImportUserService  # noqa: E402
from kafka_worker.user_integration_processor import UserIntegrationProcessor  # noqa: E402
from kafka_worker.user_integration_worker import UserIntegrationWorker  # noqa: E402

# See: https://docs.confluent.io/platform/current/clients/confluent-kafka-python/html/index.html#pythonclient-configuration
# Also: https://github.com/edenhill/librdkafka/blob/master/CONFIGURATION.md
kafka_config = {
    "client.id": socket.gethostname(),
    "group.id": settings.KAFKA_GROUP_ID,
    "bootstrap.servers": settings.KAFKA_SERVERS,
    "auto.offset.reset": "earliest",
    "enable.auto.commit": True,
}

apm_client: elasticapm.Client = None

if __name__ == "__main__":
    apm_client = elasticapm.get_client() or elasticapm.Client(
        service_name=settings.KAFKA_WORKER_APP_NAME,
        server_url=settings.ELASTIC_APM["SERVER_URL"],
        secret_token=settings.ELASTIC_APM["SECRET_TOKEN"],
        environment=settings.ELASTIC_APM["ENVIRONMENT"],
        # Fine-tuning - Ref.: https://www.elastic.co/guide/en/apm/agent/python/current/configuration.html
        transaction_sample_rate=0.1,  # default: 1.0
        span_stack_trace_min_duration=-1,  # default: 5ms
        span_compression_same_kind_max_duration="5ms",  # default: 0ms,
    )
    elasticapm.instrument()
    import_service = UserIntegrationProcessor(ImportUserService(), UserServiceGrpcClient(), DiscordWebhookLogger())
    workspace_user_topics = [WorkspaceUserTopic(**topic) for topic in WORKSPACE_USER_TOPICS]
    consumer = kafka.Consumer(kafka_config)
    worker = UserIntegrationWorker(
        kafka_config, consumer, import_service, apm_client, workspace_user_topics, KAFKA_WORKER_POLL_TIMEOUT
    )

    try:
        worker.start()
    except Exception as error:
        print(traceback.format_exc())
        apm_client.capture_exception(exc_info=error)
