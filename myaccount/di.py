import os

import injector
import utils
from common.rest_clients.notification_client import Notification<PERSON>lient
from common.services.hash_service import HashService
from common.services.workspace_service import WorkspaceService
from config import settings
from custom.discord_webhook_logger import DiscordWebhookLogger


class Container:
    def __init__(self):
        self.webhook_logger = DiscordWebhookLogger()
        tpl_emails_folder = os.path.join(os.path.abspath(os.path.dirname(__file__)), "assets/tpl_emails")

        self.whatsapp_twilio_client = utils.WhatsappTwilioClient(
            account_sid=settings.TWILIO_ACCOUNT_SID,
            auth_token=settings.TWILIO_AUTH_TOKEN,
            phone_number=settings.TWILIO_SUPPORT_PHONE_NUMBER,
        )

        self.whatsapp_messagebird_client = utils.WhatsappMessagebirdClient(
            access_key=settings.MESSAGEBIRD_ACCESS_KEY,
            channel_id=settings.MESSAGEBIRD_CHANNEL_ID,
            namespace=settings.MESSAGEBIRD_NAMESPACE,
            webhook_logger=self.webhook_logger,
        )

        self.firebase_client = utils.firebase.FirebaseService(
            firebase_key=settings.GCM_FIREBASE_KEY,
            firebase_url=settings.GCM_FIREBASE_URL,
            webhook_logger=self.webhook_logger,
        )

        self.aws_email_client = utils.aws_ses.AmazonEmailService(
            aws_ses_key=settings.AWS_ACCESS_KEY_ID,
            aws_ses_access_key=settings.AWS_SECRET_ACCESS_KEY,
            aws_ses_region=settings.AWS_REGION,
            mail_sender=settings.AWS_MAIL_SENDER,
            template_dir=tpl_emails_folder,
            webhook_logger=self.webhook_logger,
        )

        self.debounce_client = utils.debounce.DeBounceClient(
            api_url=settings.DEBOUNCE_API_URL, api_key=settings.DEBOUNCE_API_KEY
        )
        self.notification_client = NotificationClient()

        self.keycloak_user_client = utils.keycloak.KeyCloakUserManager()
        self.aws_s3_client = utils.aws.S3Client(
            aws_access_key=settings.AWS_ACCESS_KEY_ID,
            aws_secret_key=settings.AWS_SECRET_ACCESS_KEY,
            aws_region=settings.AWS_REGION_NAME,
            aws_base_s3_url=settings.AWS_BASE_S3_URL,
        )
        self._hash_service = HashService(settings.HASH_SECRET_KEY)
        self._workspace_service = WorkspaceService(self._hash_service)

    def workspace_service(self):
        return self._workspace_service

    def configure(self, binder):
        binder.bind(utils.WhatsappTwilioClient, to=injector.CallableProvider(self.whatsapp_twilio_client))
        binder.bind(utils.WhatsappMessagebirdClient, to=injector.CallableProvider(self.whatsapp_messagebird_client))
        binder.bind(utils.firebase.FirebaseService, to=injector.CallableProvider(self.firebase_client))
        binder.bind(utils.aws_ses.AmazonEmailService, to=injector.CallableProvider(self.aws_email_client))
        binder.bind(utils.aws.S3Client, to=injector.CallableProvider(self.aws_s3_client))
        binder.bind(utils.keycloak.KeyCloakUserManager, to=injector.CallableProvider(self.keycloak_user_client))
        binder.bind(utils.debounce.DeBounceClient, to=injector.CallableProvider(self.debounce_client))
        binder.bind(NotificationClient, to=injector.CallableProvider(self.notification_client))
        binder.bind(WorkspaceService, to=injector.CallableProvider(self.workspace_service))
