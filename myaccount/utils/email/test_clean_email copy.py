import unittest

from utils.email.clean_email import clean_email


class TestCleanEmail(unittest.TestCase):
    def test_valid_email(self):
        self.assertEqual(clean_email("<EMAIL>"), "<EMAIL>")

    def test_email_with_invalid_characters(self):
        self.assertEqual(clean_email("user@exam!ple.com"), "<EMAIL>")

    def test_email_with_spaces(self):
        self.assertEqual(clean_email(" user @example.com "), "<EMAIL>")

    def test_email_with_special_characters(self):
        self.assertEqual(clean_email("user!@<EMAIL>"), "<EMAIL>")

    def test_empty_string(self):
        self.assertEqual(clean_email(""), "")
