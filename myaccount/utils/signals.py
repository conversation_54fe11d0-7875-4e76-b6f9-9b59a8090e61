import functools

from django.conf import settings
from django.dispatch import receiver as django_receiver


def receiver(signal, **decorator_kwargs):
    def our_wrapper(func):
        @django_receiver(signal, **decorator_kwargs)
        @functools.wraps(func)
        def fake_receiver(sender, **kwargs):
            if settings.SUSPEND_SIGNALS:
                return None
            return func(sender, **kwargs)

        return fake_receiver

    return our_wrapper
