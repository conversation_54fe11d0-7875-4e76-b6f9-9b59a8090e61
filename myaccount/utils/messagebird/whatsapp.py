import re

import messagebird
from custom.discord_webhook_logger import DiscordWebhookLogger
from messagebird.conversation_message import MESSAGE_TYPE_HSM


# pylint: disable=R0903
class WhatsappMessagebirdClient:
    def __init__(self, access_key, channel_id, namespace, webhook_logger: DiscordWebhookLogger):
        self.client = messagebird.Client(access_key)
        self.channel_id = channel_id
        self.namespace = namespace
        self.webhook_logger = webhook_logger

    def send_message(self, phone, template_name, language, params):
        phone = re.sub("[^A-Za-z0-9]+", "", phone)

        data = {
            "hsm": {
                "namespace": self.namespace,
                "templateName": template_name,
                "language": {"policy": "deterministic", "code": language},
                "params": params,
            }
        }

        try:
            self.client.conversation_start(
                {"channelId": self.channel_id, "to": phone, "type": MESSAGE_TYPE_HSM, "content": data}
            )

        except Exception as error:
            self.webhook_logger.emit_short_message("MESSAGEBIRD ERROR", error)
