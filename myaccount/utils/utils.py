import base64
import hashlib
import re
import uuid
from typing import IO

from config.settings import TEMP_UPLOAD_FOLDER
from django.core.files.uploadedfile import InMemoryUploadedFile


def temp_file(file: InMemoryUploadedFile) -> str:
    extension = file.name.split(".")[1]
    filename = f"{str(uuid.uuid4())}.{extension}"

    with open(f"{TEMP_UPLOAD_FOLDER}/{filename}", "wb+") as destination:
        for chunk in file.chunks():
            destination.write(chunk)

    return f"{TEMP_UPLOAD_FOLDER}/{filename}"


def create_file_name(file_d: IO) -> str:
    hash_file = hashlib.new("sha256")
    block = file_d.read(512)
    while block:
        hash_file.update(block)
        block = file_d.read(512)
    file_name = str(hash_file.hexdigest())
    return file_name[:30]


def workspace_hash(workspace_id: str) -> str:
    hash_value = base64.b64encode(str(workspace_id).encode()).decode()[:8]
    return hash_value[:8]


def convert_possible_float_to_string(string):
    float_search = re.search("^[0-9.0]", string)
    string = string.split(".")[0] if float_search else string
    return string
