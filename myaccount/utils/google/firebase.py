import json

import requests
from custom.discord_webhook_logger import DiscordWebhookLogger


# pylint: disable=R0903
class FirebaseService:
    def __init__(self, firebase_url, firebase_key, webhook_logger: DiscordWebhookLogger):
        self.firebase_url = firebase_url
        self.firebase_key = firebase_key
        self.webhook_logger = webhook_logger

    def dynamic_link(self, long_link):
        try:
            if long_link is None:
                return None

            data = {"longDynamicLink": f"https://keeps.page.link/?link={long_link}"}

            response = requests.post(url=f"{self.firebase_url}/v1/shortLinks?key={self.firebase_key}", data=data)
            result = json.loads(response.text)
            return result["shortLink"]

        except Exception as error:
            self.webhook_logger.emit_short_message("SHORT LINK", error)
            return long_link
