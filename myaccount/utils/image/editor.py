import io
import uuid

from config.settings import TEMP_UPLOAD_FOLDER
from PIL import Image


class Editor:
    def cut(self, image_data: bytes, width: int, height: int) -> str:
        image = Image.open(io.BytesIO(image_data))
        image_format = image.format
        image = self._prepare_image_size_for_cut(image, width, height)
        new_image = self._crop(image, width, height)
        new_image_path = f"{TEMP_UPLOAD_FOLDER}/{uuid.uuid4()}.{image_format.lower()}"
        new_image.save(new_image_path)
        return new_image_path

    @staticmethod
    def _crop(image, width: float, height: float):
        width_difference = image.width - width
        height_difference = image.height - height
        left = 0
        upper = 0
        if height_difference:
            left = height_difference / 2
        if width_difference:
            upper = width_difference / 2
        right = upper + width
        lower = left + height
        new_image = image.crop((upper, left, right, lower))
        return new_image

    @staticmethod
    def _compute_new_size_value(value: int, percentage: float) -> int:
        return abs(int(value * percentage) - value)

    def _prepare_image_size_for_cut(self, image, width: int, height: int):
        difference_percentage = self._get_resize_percentage(image, width, height)
        if not difference_percentage:
            return image
        return image.resize(self._get_new_sizes(image, difference_percentage))

    def _get_resize_percentage(self, image, width, height):
        difference_width = image.width - width
        difference_height = image.height - height
        percentage_by_height = difference_height / image.height
        percentage_by_width = difference_width / image.width

        new_width, new_height = self._get_new_sizes(image, percentage_by_height)
        if self._are_new_sizes_bigger_than_cut_sizes((new_width, new_height), (width, height)):
            return percentage_by_height

        new_width, new_height = self._get_new_sizes(image, percentage_by_width)
        if self._are_new_sizes_bigger_than_cut_sizes((new_width, new_height), (width, height)):
            return percentage_by_width
        return 0

    @staticmethod
    def _are_new_sizes_bigger_than_cut_sizes(new_sizes: tuple, cut_sizes: tuple):
        return new_sizes[0] >= cut_sizes[0] and new_sizes[1] >= cut_sizes[1]

    def _get_new_sizes(self, image, difference_percentage: float):
        new_width = self._compute_new_size_value(image.width, difference_percentage)
        new_height = self._compute_new_size_value(image.height, difference_percentage)
        return new_width, new_height
