import json
from dataclasses import dataclass
from re import sub
from typing import List

from config.settings import IAM_OPENID_REALM, KEYCLOAK
from custom.keeps_exception_handler import KeepsInvalidIdentityProvider
from django.conf import settings
from django.db import connections
from keycloak import KeycloakAdmin
from keycloak.exceptions import KeycloakAuthenticationError


@dataclass
class SocialLogin:
    identity_provider: str
    user_id: str
    user_name: str


class KeyCloakUserManager:
    kc_admin: KeycloakAdmin = None  # static ref

    def __init__(self):
        # static one-time initialization
        if not KeyCloakUserManager.kc_admin:
            KeyCloakUserManager.kc_admin = KeycloakAdmin(
                server_url=settings.IAM_ADMIN_SERVER_URL,
                username=settings.IAM_ADMIN_USER_ADMIN,
                password=settings.IAM_ADMIN_PASS_ADMIN,
                realm_name=settings.IAM_ADMIN_REALM,
                auto_refresh_token=["get", "post", "put", "delete"],
            )

        # shared client
        self.keycloak_client = KeyCloakUserManager.kc_admin

    def get_users(self, kwargs):
        try:
            return self.keycloak_client.get_users(kwargs)
        except KeycloakAuthenticationError:
            self.keycloak_client.get_token()
            return self.keycloak_client.get_users(kwargs)

    # Deprecated. Use get_user_id_by_email()
    def get_user_by_email(self, email):
        try:
            return self.keycloak_client.get_user_id(email)
        except KeycloakAuthenticationError:
            self.keycloak_client.get_token()
            return self.keycloak_client.get_user_id(email)

    def get_user_id_by_email(self, email: str) -> str:
        users = self.keycloak_client.get_users({"email": email})
        for user in users:
            if user["email"] == email:
                return user["id"]

    def get_user(self, user_id):
        try:
            return self.keycloak_client.get_user(user_id)
        except KeycloakAuthenticationError:
            self.keycloak_client.get_token()
            return self.keycloak_client.get_user(user_id)

    def create_user(self, payload):
        try:
            return self.keycloak_client.create_user(payload)
        except KeycloakAuthenticationError:
            self.keycloak_client.get_token()
            return self.keycloak_client.create_user(payload)

    def update_user(self, user_id, payload):
        try:
            return self.keycloak_client.update_user(user_id, payload)
        except KeycloakAuthenticationError:
            self.keycloak_client.get_token()
            return self.keycloak_client.update_user(user_id, payload)

    def delete_user(self, user_id):
        try:
            return self.keycloak_client.delete_user(user_id)
        except KeycloakAuthenticationError:
            self.keycloak_client.get_token()
            return self.keycloak_client.delete_user(user_id)

    def set_user_password(self, user_id, password, temporary):
        try:
            return self.keycloak_client.set_user_password(user_id, password, temporary)
        except KeycloakAuthenticationError:
            self.keycloak_client.get_token()
            return self.keycloak_client.set_user_password(user_id, password, temporary)

    def send_reset_password(self, user_id):
        try:
            return self.keycloak_client.send_update_account(user_id, payload=json.dumps(["UPDATE_PASSWORD"]))
        except KeycloakAuthenticationError:
            self.keycloak_client.get_token()
            return self.keycloak_client.send_update_account(user_id, payload=json.dumps(["UPDATE_PASSWORD"]))

    def get_clients(self):
        try:
            return self.keycloak_client.get_clients()
        except KeycloakAuthenticationError:
            self.keycloak_client.get_token()
            return self.keycloak_client.get_clients()

    def get_client_roles(self, client_id):
        try:
            return self.keycloak_client.get_client_roles(client_id)
        except KeycloakAuthenticationError:
            self.keycloak_client.get_token()
            return self.keycloak_client.get_client_roles(client_id)

    def assign_client_role(self, user_id, client_id, roles):
        try:
            return self.keycloak_client.assign_client_role(user_id, client_id, roles)
        except KeycloakAuthenticationError:
            self.keycloak_client.get_token()
            return self.keycloak_client.assign_client_role(user_id, client_id, roles)

    @staticmethod
    def _identity_provider_exists(identity_provider: str) -> bool:
        with connections[KEYCLOAK].cursor() as cursor:
            cursor.execute(
                "SELECT 1 FROM identity_provider WHERE provider_alias = %s AND realm_id = %s",
                [identity_provider, IAM_OPENID_REALM],
            )
            return cursor.fetchone() is not None

    def save_identity_provider(self, user_id, identity_provider_alias, provider_user_id, provider_username):
        if not self._identity_provider_exists(identity_provider_alias):
            raise KeepsInvalidIdentityProvider()

        social_logins = self._get_user_social_logins(user_id)
        if social_logins:
            self._delete_user_social_login_by_provider(identity_provider_alias, social_logins, user_id)

        return self.keycloak_client.add_user_social_login(
            user_id, identity_provider_alias, provider_user_id, provider_username
        )

    def _delete_user_social_login_by_provider(
        self, identity_provider_alias: str, social_logins: List[SocialLogin], user_id: str
    ):
        same_provides = self._filter_social_login_by_provider(social_logins, identity_provider_alias)
        if same_provides:
            self.keycloak_client.delete_user_social_login(user_id, identity_provider_alias)

    @staticmethod
    def _filter_social_login_by_provider(social_logins: List[SocialLogin], identity_provider_alias: str):
        return [
            social_login for social_login in social_logins if social_login.identity_provider == identity_provider_alias
        ]

    def _get_user_social_logins(self, user_id: str) -> List[SocialLogin]:
        return [
            self._format_dict_to_dataclass(social_login, SocialLogin)
            for social_login in self.keycloak_client.get_user_social_logins(user_id)
        ]

    def _format_dict_to_dataclass(self, data: dict, new_dataclass: dataclass):
        formatted_data = {}
        for key in data:
            formatted_data[self._format_key_to_snake_case(key)] = data[key]
        return new_dataclass(**formatted_data)

    @staticmethod
    def _format_key_to_snake_case(key: str) -> str:
        return "_".join(sub("([A-Z][a-z]+)", r" \1", sub("([A-Z]+)", r" \1", key.replace("-", " "))).split()).lower()
