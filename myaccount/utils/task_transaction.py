import uuid
from contextlib import contextmanager
from datetime import datetime

import django
from config import settings
from di import Container


@contextmanager
def task_transaction(method_name):
    container: Container = Container()
    task_uuid = uuid.uuid4()
    try:
        print(f"{datetime.now()} | START:{task_uuid}-{method_name}")
        yield container
    except Exception as error:
        if settings.ENVIRONMENT_TEST:
            raise error
        logger = container.webhook_logger
        logger.emit_short_message(f"{method_name.replace('_', ' ')} - Worker", error)
    finally:
        print(f"{datetime.now()} | FINISH:{task_uuid}-{method_name}")
        if not settings.ENVIRONMENT_TEST:
            django.db.connections.close_all()
