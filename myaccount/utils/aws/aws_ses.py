from collections.abc import Sequence
from datetime import datetime

import boto3
import htmlmin
from custom.discord_webhook_logger import DiscordWebhookLogger
from jinja2 import Environment, FileSystemLoader


class AmazonEmailService:
    def __init__(
        self,
        aws_ses_key,
        aws_ses_access_key,
        aws_ses_region,
        mail_sender,
        template_dir,
        webhook_logger: DiscordWebhookLogger,
    ):
        self.webhook_logger = webhook_logger
        self.env = Environment(loader=FileSystemLoader(template_dir), extensions=["jinja2.ext.i18n"])
        self.client = boto3.client(
            "sesv2", region_name=aws_ses_region, aws_access_key_id=aws_ses_key, aws_secret_access_key=aws_ses_access_key
        )

        self.mail_sender = mail_sender

    def list_suppressed_destinations(
        self, start_date: datetime = None, page_size: int = 200, next_token: str = None
    ) -> dict:
        args = {"Reasons": ["BOUNCE", "COMPLAINT"], "PageSize": page_size}
        if next_token:
            args.update({"NextToken": next_token})
        if start_date:
            args.update({"StartDate": start_date})
        return self.client.list_suppressed_destinations(**args)

    def list_all_suppressed_destinations(self, start_date: datetime = None) -> Sequence:
        main_response = self.list_suppressed_destinations(start_date)
        main_results = main_response.get("SuppressedDestinationSummaries")
        next_token = main_response.get("NextToken")
        if not main_results:
            return []

        while next_token:
            page_response = self.list_suppressed_destinations(start_date, next_token=next_token)
            page_results = page_response["SuppressedDestinationSummaries"]
            main_results.extend(page_results)
            next_token = page_response.get("NextToken")

        return main_results

    def render_email(self, template, locale_instance, **kwargs):
        """
        Create email message
        :param template:  nome of template
        :param locale_instance:  gettext language instance
        :param kwargs: user_name, company, app_web_link, user_login, user_password, company_logo
        :return: message
        """
        try:
            self.env.install_gettext_translations(locale_instance)

            html = self.env.get_template(template).render(
                user_name=kwargs.get("user_name", None),
                company=kwargs.get("company", None),
                user_login=kwargs.get("user_login", None),
                user_email=kwargs.get("user_email", None),
                user_pass=kwargs.get("user_password", None),
                app_web_link=kwargs.get("app_web_link", None),
                app_apple_link=kwargs.get("app_apple_link", None),
                app_android_link=kwargs.get("app_android_link", None),
                company_logo=kwargs.get("company_logo", None),
            )
            return htmlmin.minify(html)

        except Exception as error:
            self.webhook_logger.emit_short_message("AmazonEmailService", error)

    def sender(self, email_list, subject, message):
        charset = "UTF-8"
        self.client.send_email(
            Destination={"ToAddresses": email_list},
            Content={
                "Simple": {
                    "Body": {
                        "Html": {"Charset": charset, "Data": message},
                        "Text": {"Charset": charset, "Data": message},
                    },
                    "Subject": {
                        "Charset": charset,
                        "Data": subject,
                    },
                }
            },
            FromEmailAddress=self.mail_sender,
        )
