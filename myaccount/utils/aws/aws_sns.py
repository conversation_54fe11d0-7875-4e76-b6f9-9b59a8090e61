#!/usr/bin/env python
import logging

import boto3
from config import settings


# pylint: disable=R0903
class SnsNotificationService:
    def __init__(self, logger=None):
        self.logger = logger

        if not logger:
            logging.basicConfig()
            self.logger = logging.getLogger(__name__)

        self.client = boto3.client(
            "sns",
            region_name=settings.AWS_REGION,
            aws_access_key_id=settings.AWS_SMS_SENDER_ACCESS_KEY,
            aws_secret_access_key=settings.AWS_SMS_SENDER_SECRET_KEY,
        )

    def send_sms(self, phone, message):
        pass
