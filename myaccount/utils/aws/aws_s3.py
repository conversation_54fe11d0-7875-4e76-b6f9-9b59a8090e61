import boto3
from boto3.s3.transfer import TransferConfig
from config import settings
from unidecode import unidecode

FILE_TYPES = {"image": ["png", "jpg", "jpeg"], "others": ["csv"]}


class S3Client:
    def __init__(self, aws_access_key, aws_secret_key, aws_region, aws_base_s3_url) -> None:
        self._aws_access_key = aws_access_key
        self._aws_secret_key = aws_secret_key
        self._aws_region = aws_region
        self._aws_base_s3_url = aws_base_s3_url
        self.bucket_name = settings.AWS_BUCKET_NAME
        self.base_url = settings.AWS_BASE_S3_URL
        self._client = boto3.client(
            "s3",
            aws_access_key_id=self._aws_access_key,
            aws_secret_access_key=self._aws_secret_key,
            region_name=self._aws_region,
        )

        self.default_config = TransferConfig(
            multipart_threshold=64 * 1024 * 1024,
            max_concurrency=10,
            num_download_attempts=10,
            multipart_chunksize=16 * 1024 * 1024,
            max_io_queue=10000,
        )

    @staticmethod
    def file_types():
        return FILE_TYPES

    def send_file(self, file, name, bucket_folder, content_type, bucket_path: str = None) -> None:
        if not bucket_path:
            bucket_path = settings.AWS_BUCKET_PATH
        filename = bucket_path + "/" + bucket_folder + "/" + unidecode(name).replace(" ", "_")
        config = TransferConfig(
            multipart_threshold=64 * 1024 * 1024,
            max_concurrency=10,
            num_download_attempts=10,
            multipart_chunksize=16 * 1024 * 1024,
            max_io_queue=10000,
        )
        self._client.upload_fileobj(
            file,
            self.bucket_name,
            filename,
            Config=config,
            ExtraArgs={"ACL": "public-read", "ContentType": content_type},
        )
        response = {"name": filename, "url": f"{settings.AWS_BASE_CDN_URL}/{filename}"}
        return response

    def send_file_path(self, file_path: str, destiny_name: str, content_type: str, bucket_name: str = None) -> dict:
        if not bucket_name:
            bucket_name = self.bucket_name
        filename = f"{self._client}/{destiny_name}"

        self._client.upload_file(
            file_path,
            bucket_name,
            filename,
            Config=self.default_config,
            ExtraArgs={"ACL": "public-read", "ContentType": content_type},
        )

        response = {"name": filename, "url": f"{self.base_url}/{filename}"}
        return response
