import json
import urllib
from typing import Dict

from twilio.base.exceptions import TwilioRestException
from twilio.rest import Client


class WhatsappTwilioClient:
    def __init__(self, account_sid, auth_token, phone_number):
        self.account_sid = account_sid
        self.auth_token = auth_token
        self.phone_number = phone_number

    @staticmethod
    def parse_channel_url(url):
        """
        Case workspace have an private channel (twilio number), Is necessary configure
        the params into workspace DB record (private_whatsapp)

        The URL pattern is
        twilio://user:pass@phone

        """
        if not url:
            return "", "", ""

        schema = url.rfind("/")
        if schema >= 0:
            url = url[schema + 1 :]
        colon = url.find(":")
        if colon < 0:
            return "", "", ""
        at_sign = url.find("@")
        if at_sign < 0:
            return "", "", ""
        username = urllib.parse.unquote(url[:colon])
        password = urllib.parse.unquote(url[colon + 1 : at_sign])
        phone = urllib.parse.unquote(url[at_sign + 1 :])
        if not username or not password or not phone:
            return "", "", ""
        return username, password, phone

    def _build_client(self, private_channel):
        username, password, phone = self.parse_channel_url(private_channel)
        if not username or not password or not phone:
            return Client(self.account_sid, self.auth_token), self.phone_number
        return Client(username, password), phone

    def send_message(self, phone: str, template_sid: str, content_variables: Dict):
        callback_url = None
        private_channel = ""

        try:
            client, sender_number = self._build_client(private_channel)
            params = {
                "from_": f"whatsapp:{sender_number}",
                "content_sid": template_sid,
                "to": f"whatsapp:{phone}",
                "status_callback": callback_url,
                "content_variables": json.dumps(content_variables, default=str),
            }
            message = client.messages.create(**params)
            return message.sid, True
        except TwilioRestException as e:
            print(str(e))
            return str(e), False
