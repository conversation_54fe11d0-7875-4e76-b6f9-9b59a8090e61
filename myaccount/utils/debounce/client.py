import json

import requests

SAFE_TO_SEND = "Safe to Send"


# pylint: disable=R0903
class DeBounceClient:
    def __init__(self, api_url, api_key):
        self.api_url = api_url
        self.api_key = api_key

    def check_email(self, email: str) -> bool:
        querystring = {"api": self.api_key, "email": email}
        headers = {"Accept": "application/json"}
        response = requests.request("GET", self.api_url, headers=headers, params=querystring)
        data = json.loads(response.text)
        return data["debounce"]["result"] == SAFE_TO_SEND
