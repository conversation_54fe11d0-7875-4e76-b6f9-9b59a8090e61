"""
MyAccount URL Configuration
"""

from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import include, path, re_path
from django.views.generic import RedirectView

from common.views.health_check import HealthCheckViewSet
from common.views.language_preference_viewset import LanguagePreferenceViewSet
from config.docs import swagger_spec_view

_read_only = {"get": "list"}

_list = {"get": "list", "post": "create"}

_detail = {"get": "retrieve", "put": "update", "patch": "partial_update", "delete": "destroy"}


urlpatterns = [
    re_path("^$", RedirectView.as_view(pattern_name="swagger-spec", permanent=True), name="swagger-spec"),
    path("swagger-spec/", swagger_spec_view, name="swagger-spec"),
    path("admin/", admin.site.urls),
    path("languages", LanguagePreferenceViewSet.as_view(_read_only), name="languages-preferences-list"),
    path("health-check", HealthCheckViewSet.as_view(), name="health-check"),
    path("auth", include("authentication.urls")),
    path("companies", include("common.company_urls")),
    path("workspaces", include("common.workspace_urls")),
    path("services", include("common.service_urls")),
    path("users", include("common.user_urls")),
    path("users-roles", include("common.user_roles_urls")),
    path("applications", include("common.application_urls")),
    path("billing", include("common.billing_urls")),
    path("jobs", include("common.job_urls")),
    path("job-functions", include("common.job_function_urls")),
    path("profiles", include("common.profile_urls")),
]

if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
