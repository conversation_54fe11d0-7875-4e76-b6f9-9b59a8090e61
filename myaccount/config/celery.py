from __future__ import absolute_import

import os

from celery import Celery
from django.conf import settings

from config.settings import CELERY_IGNORE_RESULT

# set the default Django settings module for the 'celery' program.
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
app = Celery("myaccount")

# Using a string here means the worker will not have to
# pickle the object when using Windows.
app.config_from_object("django.conf:settings")
app.autodiscover_tasks(lambda: settings.INSTALLED_APPS)
app.autodiscover_tasks(lambda: ["tasks.notifications"])
app.conf.task_ignore_result = CELERY_IGNORE_RESULT


@app.task(bind=True)
def debug_task(self):
    print("Request: {0!r}".format(self.request))
