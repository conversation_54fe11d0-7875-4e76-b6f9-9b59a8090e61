"""
Django settings for config project.
"""
import json
import os

from corsheaders.defaults import default_headers
from distutils import util as distutils
from kombu import Exchange, Queue

BASE_DIR = os.path.dirname(os.path.dirname(__file__))
ENVIRONMENT = os.getenv("ENVIRONMENT", "development")

SECRET_KEY = "w8w&3k@4s+9%j2*6_)(u7@%)tcf&eky9966m9_31y9yn&1)5h*"
KEEPS_SECRET_TOKEN_INTEGRATION = os.getenv("KEEPS_SECRET_TOKEN_INTEGRATION")

HASH_SECRET_KEY = os.getenv("HASH_SECRET_KEY", "AAAAAAAAAAAAAAAA")

_debug = os.getenv("DEBUG", "True")
DEBUG = True if _debug == "True" or _debug is True else False

ENVIRONMENT_TEST = os.getenv("ENVIRONMENT_TEST", "false")
ALLOWED_HOSTS = ["*"]
APPEND_SLASH = False
SUSPEND_SIGNALS = bool(distutils.strtobool(os.getenv("SUSPEND_SIGNALS", "False")))
KEYCLOAK = "keycloak"

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.contenttypes",
    "django.contrib.staticfiles",
    "corsheaders",
    "drf_yasg",
    "rest_framework",
    "django_injector",
    "common.apps.CommonConfig",
    "notification.apps.NotificationConfig",
]

if ENVIRONMENT in ["stage", "staging", "production"]:  # or True to enable local APM monitoring
    INSTALLED_APPS.append("elasticapm.contrib.django")

MIDDLEWARE = [
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "django_injector.middleware.inject_request_middleware",
    "custom.disable_options_method_middleware.DisableOptionsMethodMiddleware"
]

ROOT_URLCONF = "config.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": ["templates/"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "config.wsgi.application"

DATABASE_ENGINE = "django.db.backends.postgresql"
DATABASES = {
    "default": {
        "ENGINE": DATABASE_ENGINE,
        "NAME": os.environ.get("DATABASE_NAME", "myaccount_tst"),
        "USER": os.environ.get("DATABASE_USER", "postgres"),
        "PASSWORD": os.environ.get("DATABASE_PASSWORD", "postgres"),
        "HOST": os.environ.get("DATABASE_HOST", "localhost"),
        "PORT": os.environ.get("DATABASE_PORT", "5432"),
    },
    "konquest": {
        "ENGINE": DATABASE_ENGINE,
        "NAME": os.environ.get("DATABASE_KONQUEST_NAME", "konquest_tst"),
        "USER": os.environ.get("DATABASE_USER", "postgres"),
        "PASSWORD": os.environ.get("DATABASE_PASSWORD", "postgres"),
        "HOST": os.environ.get("DATABASE_HOST", "localhost"),
        "PORT": os.environ.get("DATABASE_PORT", "5432"),
    },
    "smartzap": {
        "ENGINE": DATABASE_ENGINE,
        "NAME": os.environ.get("DATABASE_SMARTZAP_NAME", "smartzap_tst"),
        "USER": os.environ.get("DATABASE_USER", "postgres"),
        "PASSWORD": os.environ.get("DATABASE_PASSWORD", "postgres"),
        "HOST": os.environ.get("DATABASE_HOST", "localhost"),
        "PORT": os.environ.get("DATABASE_PORT", "5432"),
    },
    KEYCLOAK: {
        "ENGINE": DATABASE_ENGINE,
        "NAME": os.environ.get("DATABASE_KEYCLOAK_NAME", "keycloak_tst"),
        "USER": os.environ.get("DATABASE_USER", "postgres"),
        "PASSWORD": os.environ.get("DATABASE_PASSWORD", "postgres"),
        "HOST": os.environ.get("DATABASE_HOST", "localhost"),
        "PORT": os.environ.get("DATABASE_PORT", "5432"),
    },
}
# Internationalization
LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_L10N = True

USE_TZ = False

# Static files (CSS, JavaScript, Images)
STATIC_URL = "/static/"

STATIC_ROOT = os.path.join(BASE_DIR, "static")

# Rest Framework
REST_FRAMEWORK = {
    "DEFAULT_PERMISSION_CLASSES": [
        "authentication.utils.keeps_authentication.KeepsIsAuthenticatedPermission",
    ],
    "DEFAULT_AUTHENTICATION_CLASSES": ["authentication.utils.keeps_authentication.KeepsAuthentication"],
    "EXCEPTION_HANDLER": "custom.custom_exception_handler.custom_exception_handler",
}

# SWAGGER
SWAGGER_SETTINGS = {
    "USE_SESSION_AUTH": False,
    "SECURITY_DEFINITIONS": {},
    "VALIDATOR_URL": "",
    "OPERATIONS_SORTER": "method",
    "TAGS_SORTER": None,
    "DOC_EXPANSION": "list",
    "DEEP_LINKING": False,
    "SHOW_EXTENSIONS": True,
    "DEFAULT_AUTO_SCHEMA_CLASS": "config.docs.SwaggerAutoSchemaCustom",
    "DEFAULT_INFO": "config.docs.api_info",
    "DEFAULT_MODEL_RENDERING": "model",
    "DEFAULT_MODEL_DEPTH": 2,
}

CORS_ALLOW_HEADERS = default_headers + ("x-client", "traceparent", "tracestate")

CORS_ALLOW_METHODS = (
    "DELETE",
    "GET",
    "OPTIONS",
    "PATCH",
    "POST",
    "PUT",
)

CORS_ORIGIN_ALLOW_ALL = True

CORS_ORIGIN_WHITELIST = (
    "http://localhost:4200",
    "http://127.0.0.1:4200",
    "http://localhost:4100",
    "http://127.0.0.1:4100",
    "http://kizup-api.keepsdev.com",
    "http://konquest-api.keepsdev.com",
    "http://myaccount-api.keepsdev.com",
    "http://kontent-api.keepsdev.com",
    "http://smartzap-api.keepsdev.com",
    "http://kizup.keepsdev.com",
    "http://konquest.keepsdev.com",
    "http://myaccount.keepsdev.com",
    "http://account.keepsdev.com",
    "http://kontent.keepsdev.com",
    "http://kizup-api-stage.keepsdev.com",
    "http://konquest-api-stage.keepsdev.com",
    "http://myaccount-api-stage.keepsdev.com",
    "http://kontent-api-stage.keepsdev.com",
    "http://smartzap-api-stage.keepsdev.com",
    "http://kizup-stage.keepsdev.com",
    "http://konquest-stage.keepsdev.com",
    "http://myaccount-stage.keepsdev.com",
    "http://kontent-stage.keepsdev.com",
    # HTTPS
    "https://kizup-api.keepsdev.com",
    "https://konquest-api.keepsdev.com",
    "https://myaccount-api.keepsdev.com",
    "https://kontent-api.keepsdev.com",
    "https://smartzap-api.keepsdev.com",
    "https://kizup.keepsdev.com",
    "https://konquest.keepsdev.com",
    "https://myaccount.keepsdev.com",
    "https://account.keepsdev.com",
    "https://kontent.keepsdev.com",
    "https://kizup-api-stage.keepsdev.com",
    "https://konquest-api-stage.keepsdev.com",
    "https://myaccount-api-stage.keepsdev.com",
    "https://kontent-api-stage.keepsdev.com",
    "https://smartzap-api-stage.keepsdev.com",
    "https://kizup-stage.keepsdev.com",
    "https://konquest-stage.keepsdev.com",
    "https://myaccount-stage.keepsdev.com",
    "https://learning-platform-api-stage.keepsdev.com",
)

# ADMIN KEYCLOAK
IAM_ADMIN_SERVER_URL = os.environ.get("IAM_ADMIN_SERVER_URL", "https://iam.keepsdev.com/auth/")
IAM_ADMIN_REALM = os.environ.get("IAM_ADMIN_REALM", "keeps-dev")
IAM_ADMIN_USER_ADMIN = os.environ.get("IAM_ADMIN_USER_ADMIN", "<EMAIL>")
IAM_ADMIN_PASS_ADMIN = os.environ.get("IAM_ADMIN_PASS_ADMIN", "keeps011001")

# OPENID KEYCLOAK
IAM_OPENID_TOKEN_URL = os.environ.get("IAM_OPENID_TOKEN_URL", "/protocol/openid-connect/token")
IAM_OPENID_SERVER_URL = os.environ.get("IAM_OPENID_SERVER_URL", "https://iam.keepsdev.com/auth/")
IAM_OPENID_REALM = os.environ.get("IAM_OPENID_REALM", "keeps-dev")
IAM_OPENID_CLIENT_ID = os.environ.get("IAM_OPENID_CLIENT_ID", "myaccount")
IAM_OPENID_SECRET_KEY = os.environ.get("IAM_OPENID_SECRET_KEY", "6e361440-6429-45e9-8b91-b01d777f0458")

IAM_OPENID_PUBLIC_KEY = os.environ.get(
    "IAM_OPENID_PUBLIC_KEY",
    "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtmbT6XgN9buXZMF6EAjHRNmfGaarVUbik+KtON1YBp0KgTdJn0lCtxhCq5DtvFLOOBrDccw6/RDjeSWlGEcgqggwjPnDIgdtAtgP83mofiILj0mQZfap3o/WknpR6LlveEeM09jnszCTy+WKGZ+HkOJRnhOo/JR7cKSmQxKjEI6NCCNzC4CkwUrPnE5nBhLDk5MvU9KLsBy1TyXoi7c5sh66SLJwD5yn2EzsIxxxje4fYOsXEf6PD6vsAHvjNQNlFELapQTomCECkAwg0fsDZlGAbyK+FoXt/jquKFZ/rcutoXu5tVVNNSfvs5Q4W931KAzqLozd/grR9HlSZAvVWwIDAQAB",
)

IAM_OPENID_PUBLIC_KEY = """-----BEGIN PUBLIC KEY-----
{}
-----END PUBLIC KEY-----""".format(
    IAM_OPENID_PUBLIC_KEY
)

KEYCLOAK_CONFIG = {
    "KEYCLOAK_SERVER_URL": IAM_OPENID_SERVER_URL,
    "KEYCLOAK_REALM": IAM_OPENID_REALM,
    "KEYCLOAK_CLIENT_ID": IAM_OPENID_CLIENT_ID,
    "KEYCLOAK_CLIENT_SECRET_KEY": IAM_OPENID_SECRET_KEY,
    "KEYCLOAK_CLIENT_PUBLIC_KEY": IAM_OPENID_PUBLIC_KEY,
}

MYACCOUNT_ID = os.environ.get("MYACCOUNT_ID", "ad7e5ad2-1552-43ab-a471-710954f0e66a")
MYACCOUNT_WEB_URL = os.environ.get("MYACCOUNT_WEB_URL", "http://myaccount-stage.keepsdev.com/")
MYACCOUNT_DEFAULT_ROLE = os.environ.get("MYACCOUNT_DEFAULT_ROLE", "3b16b975-0297-4edf-950b-e3700b0d0d01")
KONQUEST_DEFAULT_ROLE = os.environ.get("KONQUEST_DEFAULT_ROLE", "a6d23aea-807e-4374-964e-c725b817742d")
SMARTZAP_DEFAULT_ROLE = os.environ.get("SMARTZAP_DEFAULT_ROLE", "********-5e4e-48c6-91d7-dbeb360c7205")
ANALYTICS_DEFAULT_ROLE = os.environ.get("ANALYTICS_DEFAULT_ROLE", "4ddf7c3a-13ab-47a2-98fd-ab0b177ef823")

KONQUEST_ID = os.environ.get("KONQUEST_ID", "0abf08ea-d252-4d7c-ab45-ab3f9135c288")
KONQUEST_API_URL = os.environ.get("KONQUEST_API_URL", "https://learning-platform-api-stage.keepsdev.com/konquest/")
KONQUEST_WEB_URL = os.environ.get("KONQUEST_WEB_URL", "http://konquest-stage.keepsdev.com/")

GAME_WEB_URL = os.environ.get("GAME_WEB_URL", "http://kizup-stage.keepsdev.com/")
GAMIFICATION_SERVICE_ID = os.environ.get("GAMIFICATION_SERVICE_ID", "786f7dc6-5b82-4834-a7a4-6a938ed7dafc")

REGULATORY_COMPLIANCE_SERVICE_ID = os.environ.get("REGULATORY_COMPLIANCE_SERVICE_ID", "6064f5d7-e9cb-4bb8-8cb5-09030a14bf5f")

SMARTZAP_ID = os.environ.get("SMARTZAP_ID", "84d6715e-9b75-436d-ad44-b74c5a7f6729")
SMARTZAP_API_URL = os.environ.get(
    "SMARTZAP_API_URL", "https://learning-platform-api-stage.keepsdev.com/smartzap/api/1/"
)
SMARTZAP_WEB_URL = os.environ.get("SMARTZAP_WEB_URL", "http://smartzap-stage.keepsdev.com/")

LEARNING_ANALYTICS_ID = os.environ.get("LEARNING_ANALYTICS_ID", "c2928f23-a5a6-4f59-94a7-7e409cf1d4f4")
LEARNING_ANALYTICS_API_URL = os.environ.get(
    "LEARNING_ANALYTICS_API_URL", "https://learning-platform-api-stage.keepsdev.com/analytics/api/1/"
)
LEARNING_ANALYTICS_WEB_URL = os.environ.get("LEARNING_ANALYTICS_WEB_URL", "http://analytics-stage.keepsdev.com/")

# SMS FIRE ACCOUNT
SMS_FIRE_KEY = os.environ.get("SMS_FIRE", "a2VlcHNkZXY6S2VlcHMwMTEwMDFA")
SMS_FIRE_URL = os.environ.get("SMS_FIRE_URL", "https://api.smsfire.com.br/v1")
SMS_FIRE_TOKEN_WHATSAPP = os.environ.get("SMS_FIRE_TOKEN_WHATSAPP", "25a2fb618f39ef2f5e2448b7a1fad0cb")

AWS_BUCKET_NAME = os.getenv("AWS_BUCKET_NAME", "keeps-media-stage")
AWS_BASE_S3_URL = os.getenv("AWS_BASE_S3_URL", "https://s3.amazonaws.com")
AWS_BUCKET_PATH = os.getenv("AWS_BUCKET_PATH", "myaccount")
AWS_BASE_CDN_URL = os.getenv("AWS_BASE_CDN_URL", "https://media-stage.keepsdev.com")
AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID", "")
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY", "")

AWS_SMS_SENDER_ACCESS_KEY = os.getenv("AWS_SMS_SENDER_ACCESS_KEY", "********************")
AWS_SMS_SENDER_SECRET_KEY = os.getenv("AWS_SMS_SENDER_ACCESS_KEY", "wOAiqXO9SNrN0Hym/wrl+U9Bj3gqCOw/H6zQYUh5")
AWS_REGION_NAME = os.getenv("AWS_REGION_NAME", "us-east-1")
AWS_MAIL_SENDER = os.getenv("AWS_MAIL_SENDER", "Plataforma Aprendizagem <<EMAIL>>")
AWS_REGION = "us-east-1"

# GOOGLE CLOUD PLATFORM
GCM_FIREBASE_KEY = os.getenv("GCM_FIREBASE_KEY", "AIzaSyDTTd5FpDZyHF86-EYgWDyi2POuaEUOVHQ")
GCM_FIREBASE_URL = os.getenv("GCM_FIREBASE_URL", "https://firebasedynamiclinks.googleapis.com")

# Seed: keepsfuturo2020
KEEPS_SECRET_TOKEN_INTEGRATION = os.getenv("KEEPS_SECRET_TOKEN_INTEGRATION", "637a2f9e72daba2ebb03a699c7a4c08d")

# CELERY STUFF
BROKER_URL = os.getenv("BROKER_URL")
CELERY_BROKER_URL = os.getenv("CELERY_BROKER_URL")
CELERY_RESULT_BACKEND = os.getenv("CELERY_RESULT_BACKEND")
CELERY_ACCEPT_CONTENT = ["application/json"]
CELERY_TASK_SERIALIZER = "json"
CELERY_RESULT_SERIALIZER = "json"
CELERY_TIMEZONE = "America/Sao_Paulo"
CELERY_ACKS_LATE = True
CELERY_AMQP_TASK_RESULT_EXPIRES = 1000
CELERY_INTERVAL_START = os.environ.get("CELERY_INTERVAL_START", 0)
CELERY_INTERVAL_STEP = os.environ.get("CELERY_INTERVAL_STEP", 2)
CELERY_INTERVAL_MAX = os.environ.get("CELERY_INTERVAL_MAX", 20)
CELERY_MAX_RETRIES = os.environ.get("CELERY_INTERVAL_START", 10)
SYNC_PLATFORM_ENABLED = True
CELERY_QUEUE = os.getenv("CELERY_QUEUE", "myaccount-stage")
EXCHANGE_INTEGRATIONS = os.getenv("EXCHANGE_INTEGRATIONS", "integrations")

# Whether to store the task return values or not (tombstones).
# If you still want to store errors, just not successful return values, you can set task_store_errors_even_if_ignored.
CELERY_IGNORE_RESULT = bool(distutils.strtobool(os.getenv("CELERY_IGNORE_RESULT", "True")))

# celery queues setup
CELERY_DEFAULT_QUEUE = os.getenv("CELERY_QUEUE", "myaccount-stage")
CELERY_DEFAULT_EXCHANGE_TYPE = "topic"
CELERY_DEFAULT_ROUTING_KEY = os.getenv("CELERY_QUEUE", "myaccount-stage")
CELERY_QUEUES = (Queue(CELERY_QUEUE, Exchange(CELERY_QUEUE), routing_key=CELERY_QUEUE),)

# CELERY QUEUE INTEGRATIONS
QUEUE_KONQUEST_COMPANY = os.getenv("QUEUE_KONQUEST_COMPANY", "konquest-companies-stage")

NOTIFICATION_MESSAGE_QUEUE = os.getenv("NOTIFICATION_MESSAGE_QUEUE", "bell")
NOTIFICATION_EXCHANGE = os.getenv("NOTIFICATION_EXCHANGE_TYPE", "notification")
BELL_NOTIFICATION_ROUTING_KEY = os.getenv("BELL_NOTIFICATION_ROUTING_KEY", "bell")

DEFAULT_LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "filters": {
        "require_debug_false": {
            "()": "django.utils.log.RequireDebugFalse",
        },
        "require_debug_true": {
            "()": "django.utils.log.RequireDebugTrue",
        },
    },
    "formatters": {
        "django.server": {
            "()": "django.utils.log.ServerFormatter",
            "format": "[{server_time}] {message}",
            "style": "{",
        }
    },
    "handlers": {
        "console": {
            "level": "INFO",
            "filters": ["require_debug_true"],
            "class": "logging.StreamHandler",
        },
        "django.server": {
            "level": "INFO",
            "class": "logging.StreamHandler",
            "formatter": "django.server",
        },
        "mail_admins": {
            "level": "ERROR",
            "filters": ["require_debug_false"],
            "class": "django.utils.log.AdminEmailHandler",
        },
    },
    "loggers": {
        "django": {
            "handlers": ["console", "mail_admins"],
            "level": "INFO",
        },
        "django.server": {
            "handlers": ["django.server"],
            "level": "INFO",
            "propagate": False,
        },
    },
}

LOGGING = DEFAULT_LOGGING

LOGGING["handlers"]["slack_admins"] = {
    "level": "ERROR",
    "filters": ["require_debug_false"],
    "class": "custom.discord_webhook_logger.DiscordWebhookLogger",
}

LOGGING["loggers"]["django"] = {
    "handlers": ["console", "slack_admins"],
    "level": "INFO",
}


# https://www.elastic.co/guide/en/apm/agent/python/master/django-support.html#django-logging
LOGGING["loggers"]["elasticapm.errors"] = {
    "level": "ERROR",
    "handlers": ["console"],
    "propagate": False,
}
LOGGING["loggers"]["apm"] = {
    "level": "WARNING",
    "handlers": ["elasticapm"],
    "propagate": False,
}
LOGGING["handlers"]["elasticapm"] = {
    "level": "WARNING",
    "class": "elasticapm.contrib.django.handlers.LoggingHandler",
}


TEMP_UPLOAD_FOLDER = f"{BASE_DIR}/temp"
print(f"TEMP_UPLOAD_FOLDER: {TEMP_UPLOAD_FOLDER}")
MEDIA_ROOT = "temp"

WHATSAPP_BROKER = os.getenv("WHATSAPP_BROKER")
TWILIO_ACCOUNT_SID = os.getenv("TWILIO_ACCOUNT_SID")
TWILIO_AUTH_TOKEN = os.getenv("TWILIO_AUTH_TOKEN")
TWILIO_SUPPORT_PHONE_NUMBER = os.getenv("TWILIO_SUPPORT_PHONE_NUMBER")

MESSAGEBIRD_NAMESPACE = os.getenv("MESSAGEBIRD_NAMESPACE")
MESSAGEBIRD_ACCESS_KEY = os.getenv("MESSAGEBIRD_ACCESS_KEY")
MESSAGEBIRD_CHANNEL_ID = os.getenv("MESSAGEBIRD_CHANNEL_ID")

DEBOUNCE_API_URL = os.getenv("DEBOUNCE_API_URL")
DEBOUNCE_API_KEY = os.getenv("DEBOUNCE_API_KEY")

ROLE_BASIC_ANALYTIC_LEADER_ID = os.getenv("ROLE_LEADER_ID", "6a2b41b4-54c2-40d1-a587-cf25ab284aa0")

# SCHEDULER CONFIG
EVERY_MONDAY_AT_DAWN = "00 07 * * MON"
EVERY_FIRST_MONTH = "0 7 1 * *"

GENERATE_BILLING_WORKSPACES = os.getenv("GENERATE_BILLING_WORKSPACES", EVERY_FIRST_MONTH)

DISCORD_WEBHOOK = os.getenv(
    "DISCORD_WEBHOOK",
    "https://discord.com/api/webhooks/1019626923179118652/5c2fUKd6dNzGn2wtw5oRKnnLrQll4N25jf8u0F85g7KoQUFcxmTM_l0Y3Qi8sqijRozT",
)

ELASTIC_APM = {
    "SERVICE_NAME": os.getenv("ELASTIC_APM_SERVICE_NAME", "myaccount"),
    "SERVER_URL": os.getenv("ELASTIC_APM_SERVER_URL", "https://keeps.apm.us-east-1.aws.cloud.es.io"),
    "SECRET_TOKEN": os.getenv("ELASTIC_APM_SECRET_TOKEN"),
    "ENVIRONMENT": os.getenv("ELASTIC_APM_ENVIRONMENT", "development"),
    # "DEBUG": True,  # enable local APM monitoring
    # Fine-tuning - Ref.: https://www.elastic.co/guide/en/apm/agent/python/current/configuration.html
    "TRANSACTION_SAMPLE_RATE": 0.1,  # default: 1.0
    "SPAN_STACK_TRACE_MIN_DURATION": -1,  # default: 5ms
    "SPAN_COMPRESSION_SAME_KIND_MAX_DURATION": "5ms",  # default: 0ms,
}

NOTIFICATION_API_URL = os.getenv(
    "NOTIFICATION_API_URL", "https://learning-platform-api-stage.keepsdev.com/notification"
)

X_FORWARDED_HOST = os.getenv("X_FORWARDED_HOST", "127.0.0.1:8000")
FORCE_HTTPS_IN_FORWARDED_HOST = bool(distutils.strtobool(os.getenv("FORCE_HTTPS_IN_FORWARDED_HOST", "False")))

KAFKA_WORKER_POLL_TIMEOUT = int(os.getenv("KAFKA_WORKER_POLL_TIMEOUT", 1))
KAFKA_GROUP_ID = os.getenv("KAFKA_GROUP_ID", "myaccount-kafka-worker")
KAFKA_SERVERS = os.getenv("KAFKA_SERVERS", "dev-broker:29092")

KAFKA_WORKER_APP_NAME = (os.getenv("KAFKA_WORKER_APP_NAME", "myaccount-kafka-worker"),)

# Some workspaces have an integration with the MyAccount by kafka connectors, the WORKSPACE_USER_TOPICS is used to map each workspace's kafka topic
# exp: [{"workspace_id": "e76b5082-f4fe-4f41-be79-1977840e16a8", "topic_key": "users_by_csv_file_keeps_"}]
WORKSPACE_USER_TOPICS = json.loads(os.getenv("WORKSPACE_TOPICS", "[]"))

ALURA_INTEGRATION_API_URL = os.getenv("ALURA_INTEGRATION_API_URL", "https://learning-platform-api-stage.keepsdev.com/integration-alura")

DEFAULT_USER_LANGUAGE_PREFERENCE = os.getenv("DEFAULT_USER_LANGUAGE_PREFERENCE", "pt-br")
AWS_KAFKA_INTEGRATION_BUCKET = os.getenv("AWS_KAFKA_INTEGRATION_BUCKET", "keeps-integrations")
AWS_KAFKA_INTEGRATION_ERROR_REPORT_GENERATOR_HOURS_INTERVAL = int(
    os.getenv("AWS_KAFKA_INTEGRATION_ERROR_REPORT_GENERATOR_HOURS_INTERVAL", 24)
)
ALURA_INTEGRATION_API_URL = os.getenv("ALURA_INTEGRATION_API_URL", "https://learning-platform-api-stage.keepsdev.com/integration-alura")

GRPC_SERVER = os.getenv('GRPC_SERVER', 'myaccount-v2-svc:50051')
