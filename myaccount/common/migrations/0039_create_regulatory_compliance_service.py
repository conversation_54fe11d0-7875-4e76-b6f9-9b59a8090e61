# Generated by Django 2.2 on 2024-03-01 13:57
from django.conf import settings
from django.db import migrations


def create_regulatory_compliance_service(apps, schema_editor):
    service_model = apps.get_model("common", "Service")
    application_model = apps.get_model("common", "Application")

    application_model.objects.get_or_create(
        id=settings.KONQUEST_ID, name='Konquest'
    )
    service_model.objects.get_or_create(
        id=settings.REGULATORY_COMPLIANCE_SERVICE_ID, name='Regulatory Compliance',
        application_id=settings.KONQUEST_ID
    )


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0038_workspace_alura_integration_active'),
    ]

    operations = [
        migrations.RunPython(create_regulatory_compliance_service),
    ]
