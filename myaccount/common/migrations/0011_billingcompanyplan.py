# Generated by Django 2.2 on 2022-07-29 12:49

import uuid

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("common", "0010_auto_20220713_0530"),
    ]

    operations = [
        migrations.CreateModel(
            name="BillingCompanyPlan",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("current_plan", models.IntegerField(default=50, verbose_name="Current monthly plan")),
                ("created_date", models.DateTimeField(auto_now_add=True, verbose_name="Created Date")),
                ("updated_date", models.DateTimeField(auto_now=True, verbose_name="Updated Date")),
                (
                    "application",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to="common.Application", verbose_name="Application"
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to="common.Company", verbose_name="Workspace"
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Company application plan",
                "db_table": "company_billing_plan",
                "unique_together": {("application", "company")},
            },
        ),
    ]
