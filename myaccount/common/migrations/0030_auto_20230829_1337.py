# Generated by Django 2.2 on 2023-08-29 13:37

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0029_idpworkspace'),
    ]

    operations = [
        migrations.AddField(
            model_name='jobfunction',
            name='workspace',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='common.Workspace', verbose_name='Workspace'),
        ),
        migrations.AlterUniqueTogether(
            name='jobfunction',
            unique_together={('name', 'workspace')},
        ),
        migrations.RemoveField(
            model_name='jobfunction',
            name='job',
        ),
    ]
