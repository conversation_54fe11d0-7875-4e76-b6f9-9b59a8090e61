# Generated by Django 2.2 on 2022-05-02 16:32

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("common", "0003_auto_20220430_2252"),
    ]

    operations = [
        migrations.<PERSON>ameField(
            model_name="serviceworkspace",
            old_name="company",
            new_name="workspace",
        ),
        migrations.RenameField(
            model_name="userroleworkspace",
            old_name="company",
            new_name="workspace",
        ),
        migrations.AlterUniqueTogether(
            name="serviceworkspace",
            unique_together={("service", "workspace")},
        ),
        migrations.AlterUniqueTogether(
            name="userroleworkspace",
            unique_together={("user", "role", "workspace")},
        ),
    ]
