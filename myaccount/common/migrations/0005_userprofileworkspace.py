# Generated by Django 2.2 on 2022-06-09 15:18

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0004_new_leader_role'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserProfileWorkspace',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('job', models.CharField(blank=True, max_length=200, null=True, verbose_name='Job')),
                ('director', models.Char<PERSON>ield(blank=True, max_length=200, null=True, verbose_name='Director')),
                ('manager', models.Char<PERSON>ield(blank=True, max_length=200, null=True, verbose_name='Manager')),
                ('area_of_activity', models.CharField(blank=True, max_length=300, null=True, verbose_name='Area of activity.')),
                ('created_date', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated Date')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='common.User')),
                ('workspace', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='common.Workspace')),
            ],
            options={
                'verbose_name_plural': 'Users Profiles Workspaces',
                'db_table': 'user_profile_workspace',
                'unique_together': {('user', 'workspace')},
            },
        ),
    ]
