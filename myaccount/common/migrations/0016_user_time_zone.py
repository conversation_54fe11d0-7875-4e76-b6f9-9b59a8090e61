# Generated by Django 2.2 on 2022-10-21 13:19
import pytz
from django.db import migrations, models

from common.models import User


def normalize_users_time_zones(*args, **kwargs):
    time_zone_by_country = {
        "brasil": "America/Sao_Paulo",
        "chile": "America/Santiago",
        "pa": "America/Panama",
        "panama": "America/Panama",
        "peru": "Etc/GMT-5",
        "perú": "Etc/GMT-5",
        "argentina": "America/Argentina/Buenos_Aires",
        "ar": "America/Argentina/Buenos_Aires",
        "col": "America/Bogota",
        "colombia": "America/Bogota",
        "colômbia": "America/Bogota",
        "paraguay": "America/Asuncion",
        "mx": "Mexico/General",
        "mexico": "Mexico/General",
        "cr": "America/Costa_Rica",
        "gt": "America/Guatemala",
        "ec": "America/Guayaquil",
        "br": "America/Sao_Paulo",
    }
    for country in time_zone_by_country:
        zone = pytz.timezone(time_zone_by_country[country]).zone
        User.objects.filter(country__icontains=country).update(time_zone=zone)


def make_nothing(*args):
    pass


class Migration(migrations.Migration):

    dependencies = [
        ("common", "0015_merge_20220909_1238"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="time_zone",
            field=models.CharField(default="america/sao_paulo", max_length=200, verbose_name="Time Zone"),
        ),
        migrations.RunPython(normalize_users_time_zones, make_nothing),
    ]
