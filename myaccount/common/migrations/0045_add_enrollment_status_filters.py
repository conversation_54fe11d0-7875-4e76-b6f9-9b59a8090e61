from django.db import migrations

def add_enrollment_status_filters(apps, schema_editor):
    EnrollmentStatusFilter = apps.get_model('common', 'EnrollmentStatusFilter')
    
    filters = [
        {"id": "7254198f-1bd6-4799-8b9e-f49af8db1dc1", "status_code": "GIVE_UP", "status_name": "Give Up"},
        {"id": "734b1941-54e4-4fce-97e2-3dda2b3767d2", "status_code": "INACTIVATED", "status_name": "Inactivated"},
        {"id": "fe17e3c8-1379-4b6d-84aa-6689aa234560", "status_code": "COMPLETED", "status_name": "Completed"},
        {"id": "31bb493d-bffe-4d6a-9387-5ea1cd0095e2", "status_code": "WAITING_APPROVAL", "status_name": "Waiting Approval"},
        {"id": "e271fbdd-d71f-495f-b7c8-dfa030775e71", "status_code": "EXPIRED", "status_name": "Expired"},
        {"id": "6c920109-2bc2-47de-bdcd-6a2ca1c02396", "status_code": "PENDING_VALIDATION", "status_name": "Pending Validation"},
        {"id": "88a876e1-5845-40a9-84ed-0fdabfa73271", "status_code": "REQUEST_EXTENSION", "status_name": "Request Extension"},
        {"id": "f06daa30-37c2-4191-9642-ea3d69e265c8", "status_code": "REFUSED", "status_name": "Refused"},
        {"id": "770c5ba8-782a-45b1-8660-dd2f119e02c3", "status_code": "REPROVED", "status_name": "Reproved"}
    ]

    for filter_data in filters:
        obj, created = EnrollmentStatusFilter.objects.get_or_create(id=filter_data["id"], defaults=filter_data)
        if created:
            print(f"added: {filter_data['status_name']}")
        else:
            print(f"already exists: {filter_data['status_name']}")

class Migration(migrations.Migration):

    dependencies = [
        ('common', '0044_enrollmentstatusfilter_workspacefiltersetting'),
    ]

    operations = [
        migrations.RunPython(add_enrollment_status_filters),
    ]
