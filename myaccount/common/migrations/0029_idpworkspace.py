# Generated by Django 2.2 on 2023-08-26 19:04

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0028_delete_idpworkspace'),
    ]

    operations = [
        migrations.CreateModel(
            name='IdpWorkspace',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('idp', models.CharField(max_length=200, verbose_name='Identity Provider')),
                ('workspace', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='common.Workspace')),
            ],
            options={
                'db_table': 'idp_workspace',
                'unique_together': {('idp', 'workspace')},
            },
        ),
    ]
