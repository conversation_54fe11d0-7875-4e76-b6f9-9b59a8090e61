# Generated by Django 2.2 on 2022-04-30 22:52
from dateutil.utils import today
from django.db import migrations, connection

from common.models import Application
from config.settings import ROLE_BASIC_ANALYTIC_LEADER_ID, LEARNING_ANALYTICS_ID


def add_new_role(*args):
    analytics_exists = Application.objects.filter(id=LEARNING_ANALYTICS_ID).exists()
    if analytics_exists:
        return
    application = Application(
        id=LEARNING_ANALYTICS_ID,
        name="Learn Analytics",
        status=True
    )
    application.save()
    with connection.cursor() as cursor:
        cursor.execute(
            'INSERT INTO role (id, key, description, status, created_date, updated_date, application_id, name) '
            'VALUES (%s, %s, %s, %s, %s, %s, %s, %s);',
            [ROLE_BASIC_ANALYTIC_LEADER_ID, 'basic_analytics_leader', 'Can view data from your subordinates', True, today(),
             today(), LEARNING_ANALYTICS_ID, 'Analytics Leader']
        )


def delete_role(*args):
    with connection.cursor() as cursor:
        cursor.execute('DELETE FROM role WHERE id = (%s)', [ROLE_BASIC_ANALYTIC_LEADER_ID])


class Migration(migrations.Migration):
    dependencies = [
        ('common', '0003_auto_20220430_2252'),
    ]

    operations = [
        migrations.RunPython(add_new_role),
    ]
