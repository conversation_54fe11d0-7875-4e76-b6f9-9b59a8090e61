# Generated by Django 2.2 on 2024-03-02 01:59

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0036_auto_20240301_1357'),
    ]

    operations = [
        migrations.CreateModel(
            name='GamificationRankingWorkspace',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('ranking', models.CharField(choices=[('general', 'general ranking'), ('multiplier', 'ranking by multiplier'), ('director', 'ranking by director'), ('manager', 'ranking by manager'), ('leader', 'ranking by leader'), ('activity_area', 'ranking by activity area')], max_length=30, verbose_name='Ranking')),
                ('status', models.BooleanField(default=True, verbose_name='Status')),
                ('created_date', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated Date')),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='common.Service', verbose_name='Service')),
                ('workspace', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='common.Workspace', verbose_name='Company')),
            ],
            options={
                'verbose_name_plural': 'Gamification Ranking Workspace',
                'db_table': 'gamification_ranking_workspace',
                'unique_together': {('service', 'workspace', 'ranking')},
            },
        ),
    ]
