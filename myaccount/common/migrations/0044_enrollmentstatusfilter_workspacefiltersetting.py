# Generated by Django 2.2 on 2024-09-05 13:45

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0043_auto_20240731_1830'),
    ]

    operations = [
        migrations.CreateModel(
            name='EnrollmentStatusFilter',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('status_code', models.CharField(max_length=50, unique=True)),
                ('status_name', models.CharField(max_length=100)),
            ],
            options={
                'verbose_name': 'Enrollment Status Filter',
                'verbose_name_plural': 'Enrollment Status Filters',
                'db_table': 'enrollment_status_filters',
                'ordering': ['status_name'],
            },
        ),
        migrations.CreateModel(
            name='WorkspaceFilterSetting',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('is_enabled', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('enrollment_status_filter', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='common.EnrollmentStatusFilter')),
                ('workspace', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='common.Workspace')),
            ],
            options={
                'verbose_name': 'Workspace Filter Setting',
                'verbose_name_plural': 'Workspace Filter Settings',
                'db_table': 'workspace_filter_settings',
                'unique_together': {('workspace', 'enrollment_status_filter')},
            },
        ),
    ]
