# Generated by Django 2.2 on 2024-06-20 11:27

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0039_create_regulatory_compliance_service'),
    ]

    operations = [
        migrations.CreateModel(
            name='KafkaUserIntegrationLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.TextField(blank=True, null=True, verbose_name='Title')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('message_read', models.TextField(blank=True, null=True, verbose_name='Message Read')),
                ('workspace_id', models.UUIDField(blank=True, null=True, verbose_name='Workspace Id')),
                ('batch_name', models.CharField(blank=True, max_length=500, null=True, verbose_name='Batch Name')),
                ('is_error', models.BooleanField(default=False, verbose_name='Is Error')),
                ('created_date', models.DateTimeField(auto_now_add=True, verbose_name='Created Date')),
            ],
            options={
                'verbose_name_plural': 'Kafka User Integration Log',
                'db_table': 'kafka_user_integration_log',
            },
        ),
        migrations.AddField(
            model_name='workspace',
            name='default_federated_identity_provider_alias',
            field=models.CharField(max_length=100, null=True, verbose_name='Default Federated Identity Provider Alias'),
        ),
    ]
