# Generated by Django 2.2 on 2023-08-24 19:31

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0026_auto_20230815_0111'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='userprofileworkspace',
            name='job',
        ),
        migrations.CreateModel(
            name='IdpWorkspace',
            fields=[
                ('idp', models.CharField(max_length=200, primary_key=True, serialize=False, verbose_name='Identity Provider')),
                ('workspace', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='common.Workspace')),
            ],
            options={
                'db_table': 'idp_workspace',
            },
        ),
    ]
