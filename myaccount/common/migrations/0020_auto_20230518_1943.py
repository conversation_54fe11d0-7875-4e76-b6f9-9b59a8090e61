# Generated by Django 2.2 on 2023-05-18 19:43

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0019_auto_20230419_1351'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserIntegrationMessageHash',
            fields=[
                ('created_date', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, verbose_name='Updated Date')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('hash', models.TextField(verbose_name='hash')),
                ('workspace_id', models.UUIDField(verbose_name='workspace_id')),
            ],
            options={
                'verbose_name_plural': 'User Integration Message Hashes',
                'db_table': 'user_integration_message_hash',
            },
        ),
        migrations.Alter<PERSON>ield(
            model_name='workspace',
            name='smtp_auth_pass',
            field=models.CharField(blank=True, max_length=1000, null=True, verbose_name='Smtp Auth User'),
        ),
        migrations.AlterField(
            model_name='workspace',
            name='smtp_auth_user',
            field=models.CharField(blank=True, max_length=400, null=True, verbose_name='Smtp Auth User'),
        ),
    ]
