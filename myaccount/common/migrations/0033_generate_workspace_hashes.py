from django.db import migrations, models
from utils.utils import workspace_hash


def generate_workspace_hashes(apps, schema_editor):
    Workspace = apps.get_model("common", "Workspace")

    workspaces = Workspace.objects.all()

    for workspace in workspaces:
        hash_id = workspace_hash(workspace.id)
        workspace.hash_id = hash_id
        workspace.save()


class Migration(migrations.Migration):
    dependencies = [
        ("common", "0033_auto_20230927_1730"),
    ]

    operations = [
        migrations.RunPython(generate_workspace_hashes),
    ]
