# Generated by Django 2.2 on 2023-03-16 16:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0017_auto_20221111_1312'),
    ]

    operations = [
        migrations.AddField(
            model_name='workspace',
            name='enable_email_notifications',
            field=models.BooleanField(default=True, verbose_name='Enable email notifications'),
        ),
        migrations.AddField(
            model_name='workspace',
            name='smtp_auth_pass',
            field=models.CharField(blank=True, max_length=400, null=True, verbose_name='Smtp Auth User'),
        ),
        migrations.AddField(
            model_name='workspace',
            name='smtp_auth_user',
            field=models.CharField(blank=True, max_length=1000, null=True, verbose_name='Smtp Auth User'),
        ),
        migrations.AddField(
            model_name='workspace',
            name='smtp_host',
            field=models.Char<PERSON>ield(blank=True, max_length=400, null=True, verbose_name='Smtp Host'),
        ),
        migrations.AddField(
            model_name='workspace',
            name='smtp_port',
            field=models.IntegerField(blank=True, null=True, verbose_name='Smtp Port'),
        ),
        migrations.AddField(
            model_name='workspace',
            name='smtp_reject_unauthorized',
            field=models.BooleanField(blank=True, default=True, null=True, verbose_name='Smtp Reject Unauthorized'),
        ),
        migrations.AddField(
            model_name='workspace',
            name='smtp_secure',
            field=models.BooleanField(blank=True, default=False, null=True, verbose_name='Smtp Secure'),
        ),
        migrations.AddField(
            model_name='workspace',
            name='smtp_sender_email',
            field=models.CharField(blank=True, max_length=400, null=True, verbose_name='Smtp Sender Email'),
        ),
        migrations.AddField(
            model_name='workspace',
            name='use_own_smtp',
            field=models.BooleanField(default=False, verbose_name='Use own smtp'),
        ),
    ]
