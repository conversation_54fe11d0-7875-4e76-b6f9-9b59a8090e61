# Generated by Django 2.2 on 2023-08-14 12:30
import uuid

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("common", "0023_auto_20230705_2131"),
    ]

    run_before = [
        ("common", "0025_remove_job_field"),
    ]

    operations = [
        migrations.CreateModel(
            name="Job",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("name", models.CharField(blank=True, max_length=200, null=True, verbose_name="Job")),
                ("created_date", models.DateTimeField(auto_now_add=True, null=True, verbose_name="Created Date")),
                ("updated_date", models.DateTimeField(auto_now=True, null=True, verbose_name="Updated Date")),
                (
                    "workspace",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="common.Workspace",
                        verbose_name="Workspace",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Jobs",
                "db_table": "job",
                "unique_together": {("name", "workspace")},
            },
        ),
        migrations.AddField(
            model_name="userprofileworkspace",
            name="job_position",
            field=models.ForeignKey(
                null=True, on_delete=django.db.models.deletion.SET_NULL, to="common.Job", verbose_name="Job"
            ),
        ),
    ]
