# Generated by Django 2.2 on 2025-06-26 13:52

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0048_workspace_banner_mode'),
    ]

    operations = [
        migrations.CreateModel(
            name='WorkspaceCustomMenuItem',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('icon', models.Char<PERSON>ield(max_length=100)),
                ('url', models.Char<PERSON>ield(max_length=500)),
                ('created_date', models.DateTimeField(auto_now_add=True)),
                ('updated_date', models.DateTimeField(auto_now=True)),
                ('workspace', models.ForeignKey(db_column='workspace_id', on_delete=django.db.models.deletion.CASCADE, related_name='custom_menu_items', to='common.Workspace')),
            ],
            options={
                'db_table': 'workspace_custom_menu_item',
            },
        ),
    ]
