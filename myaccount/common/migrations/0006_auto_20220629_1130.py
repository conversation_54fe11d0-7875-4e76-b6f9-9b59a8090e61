# Generated by Django 2.2 on 2022-06-29 11:30

import uuid

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("common", "0003_auto_20220430_2252"),
    ]

    operations = [
        migrations.CreateModel(
            name="Company",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=200, verbose_name="Name")),
                ("description", models.TextField(blank=True, null=True, verbose_name="Description")),
                ("status", models.BooleanField(default=True, verbose_name="Status")),
                ("address", models.TextField(blank=True, null=True, verbose_name="Address")),
                ("city", models.CharField(blank=True, max_length=200, null=True, verbose_name="City")),
                ("state", models.Char<PERSON>ield(blank=True, max_length=200, null=True, verbose_name="State")),
                ("post_code", models.CharField(blank=True, max_length=200, null=True, verbose_name="Post Code")),
                ("country", models.CharField(blank=True, max_length=200, null=True, verbose_name="Country")),
                ("icon_url", models.TextField(blank=True, null=True, verbose_name="Icon URL")),
                ("created_date", models.DateTimeField(auto_now_add=True, verbose_name="Created Date")),
                ("updated_date", models.DateTimeField(auto_now=True, verbose_name="Updated Date")),
            ],
            options={
                "verbose_name_plural": "companies",
                "db_table": "company",
            },
        ),
        migrations.AddField(
            model_name="workspace",
            name="company",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="common.Company",
                verbose_name="Workspace's company",
            ),
        ),
    ]
