# Generated by Django 2.2 on 2023-08-15 01:11

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0025_remove_job_field'),
    ]

    operations = [
        migrations.CreateModel(
            name='JobFunction',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(blank=True, max_length=200, null=True, verbose_name='Job Function')),
                ('created_date', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated Date')),
                ('job', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='common.Job', verbose_name='Job')),
            ],
            options={
                'verbose_name_plural': 'Job Functions',
                'db_table': 'job_function',
                'unique_together': {('name', 'job')},
            },
        ),
        migrations.AddField(
            model_name='userprofileworkspace',
            name='job_function',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='common.JobFunction', verbose_name='Job Function'),
        ),
    ]
