# Generated by Django 2.2 on 2022-04-16 01:33

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Application',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('status', models.BooleanField(default=True, verbose_name='Status')),
                ('created_date', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated Date')),
            ],
            options={
                'verbose_name_plural': 'applications',
                'db_table': 'application',
            },
        ),
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='Name')),
                ('duns_number', models.CharField(blank=True, max_length=200, null=True, verbose_name='DUNS Number')),
                ('doc_number', models.CharField(blank=True, max_length=200, null=True, verbose_name='DOCS Number')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('status', models.BooleanField(default=True, verbose_name='Status')),
                ('address', models.TextField(blank=True, null=True, verbose_name='Address')),
                ('city', models.CharField(blank=True, max_length=200, null=True, verbose_name='City')),
                ('state', models.CharField(blank=True, max_length=200, null=True, verbose_name='State')),
                ('post_code', models.CharField(blank=True, max_length=200, null=True, verbose_name='Post Code')),
                ('country', models.CharField(blank=True, max_length=200, null=True, verbose_name='Country')),
                ('icon_url', models.TextField(blank=True, null=True, verbose_name='Icon URL')),
                ('icon_svg_url', models.TextField(blank=True, null=True, verbose_name='Logo SVG URL')),
                ('logo_url', models.TextField(blank=True, null=True, verbose_name='Logo URL')),
                ('theme_id', models.TextField(blank=True, null=True, verbose_name='Theme Id')),
                ('theme_dark', models.BooleanField(default=False, verbose_name='Theme Dark Active')),
                ('created_date', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated Date')),
            ],
            options={
                'verbose_name_plural': 'companies',
                'db_table': 'company',
            },
        ),
        migrations.CreateModel(
            name='LanguagePreference',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='Name')),
                ('status', models.BooleanField(default=True, verbose_name='Status')),
                ('created_date', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated Date')),
            ],
            options={
                'verbose_name_plural': 'Language Preferences',
                'db_table': 'language_preference',
            },
        ),
        migrations.CreateModel(
            name='Role',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(blank=True, max_length=200, null=True, verbose_name='Name')),
                ('key', models.CharField(max_length=200, verbose_name='Key')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('status', models.BooleanField(default=True, verbose_name='Status')),
                ('created_date', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated Date')),
                ('application', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='common.Application')),
            ],
            options={
                'verbose_name_plural': 'roles',
                'db_table': 'role',
            },
        ),
        migrations.CreateModel(
            name='Service',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('status', models.BooleanField(default=True, verbose_name='Status')),
                ('created_date', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated Date')),
                ('application', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='common.Application', verbose_name='Application')),
            ],
            options={
                'verbose_name_plural': 'services',
                'db_table': 'service',
            },
        ),
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(blank=True, max_length=200, null=True, verbose_name='Name')),
                ('nickname', models.CharField(blank=True, max_length=200, null=True, verbose_name='Nickname')),
                ('email', models.EmailField(editable=False, max_length=200, unique=True, verbose_name='Email')),
                ('email_verified', models.BooleanField(default=False, verbose_name='Email Verified')),
                ('secondary_email', models.EmailField(blank=True, max_length=200, null=True, verbose_name='Secondary Email')),
                ('phone', models.CharField(blank=True, max_length=15, null=True, validators=[django.core.validators.RegexValidator(message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.", regex='^\\+?1?\\d{9,15}$')], verbose_name='Phone')),
                ('avatar', models.TextField(blank=True, null=True, verbose_name='Avatar')),
                ('gender', models.CharField(blank=True, choices=[('FEMALE', 'FEMALE'), ('MALE', 'MALE')], max_length=200, null=True, verbose_name='Gender')),
                ('job', models.CharField(blank=True, max_length=200, null=True, verbose_name='Job')),
                ('birthday', models.DateField(blank=True, null=True, verbose_name='Birthday')),
                ('address', models.TextField(blank=True, null=True, verbose_name='Address')),
                ('country', models.CharField(blank=True, max_length=20, null=True, verbose_name='Country')),
                ('ein', models.CharField(blank=True, max_length=20, null=True, verbose_name='Employer Identification Number')),
                ('status', models.BooleanField(default=True, verbose_name='Status')),
                ('created_date', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated Date')),
                ('language', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='common.LanguagePreference', verbose_name='Language')),
                ('related_user_leader', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='common.User', verbose_name='Related User Leader')),
            ],
            options={
                'verbose_name_plural': 'Users',
                'db_table': 'user',
            },
        ),
        migrations.CreateModel(
            name='UserRoleCompany',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('status', models.BooleanField(default=True, verbose_name='Status')),
                ('self_sign_up', models.BooleanField(default=False, verbose_name='Self Sign Up')),
                ('created_date', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated Date')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='common.Company')),
                ('role', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='common.Role')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='common.User')),
            ],
            options={
                'verbose_name_plural': 'Users profile company',
                'db_table': 'user_role_company',
                'unique_together': {('user', 'role', 'company')},
            },
        ),
        migrations.CreateModel(
            name='ServiceCompany',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('status', models.BooleanField(default=True, verbose_name='Status')),
                ('custom_url', models.URLField(blank=True, max_length=500, null=True, verbose_name='Custom URL')),
                ('created_date', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated Date')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='common.Company', verbose_name='Company')),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='common.Service', verbose_name='Service')),
            ],
            options={
                'verbose_name_plural': 'Company services',
                'db_table': 'service_company',
                'unique_together': {('service', 'company')},
            },
        ),
    ]
