# Generated by Django 2.2 on 2024-03-01 13:57
from django.conf import settings
from django.db import migrations


def create_gamification_service(apps, schema_editor):
    service_model = apps.get_model("common", "Service")
    application_model = apps.get_model("common", "Application")

    application_model.objects.get_or_create(
        id=settings.KONQUEST_ID, name='Konquest'
    )
    service_model.objects.get_or_create(
        id=settings.GAMIFICATION_SERVICE_ID, name='Gamification',
        application_id=settings.KONQUEST_ID
    )


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0035_workspace_enrollment_goal_duration_days'),
    ]

    operations = [
        migrations.RunPython(create_gamification_service),
    ]
