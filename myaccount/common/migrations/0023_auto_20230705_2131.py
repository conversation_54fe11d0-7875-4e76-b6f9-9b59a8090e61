# Generated by Django 2.2 on 2023-07-05 21:31

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0022_workspace_custom_color'),
    ]

    operations = [
        migrations.AddField(
            model_name='workspace',
            name='allow_create_paid_channel',
            field=models.BooleanField(default=False, verbose_name='Allow Create Paid Channel'),
        ),
        migrations.AddField(
            model_name='workspace',
            name='allow_create_paid_mission',
            field=models.BooleanField(default=False, verbose_name='Allow Create Paid Mission'),
        ),
        migrations.AddField(
            model_name='workspace',
            name='allow_create_public_channel',
            field=models.BooleanField(default=False, verbose_name='Allow Create Public Channel'),
        ),
        migrations.AddField(
            model_name='workspace',
            name='allow_create_public_mission',
            field=models.BooleanField(default=False, verbose_name='Allow Create Public Mission'),
        ),
        migrations.AddField(
            model_name='workspace',
            name='allow_list_paid_channel',
            field=models.BooleanField(default=False, verbose_name='Allow List Paid Channel'),
        ),
        migrations.AddField(
            model_name='workspace',
            name='allow_list_paid_mission',
            field=models.BooleanField(default=False, verbose_name='Allow List Paid Mission'),
        ),
        migrations.AddField(
            model_name='workspace',
            name='allow_list_public_categories',
            field=models.BooleanField(default=True, verbose_name='Allow List Public Categories'),
        ),
        migrations.AddField(
            model_name='workspace',
            name='allow_list_public_channel',
            field=models.BooleanField(default=False, verbose_name='Allow List Public Channel'),
        ),
        migrations.AddField(
            model_name='workspace',
            name='allow_list_public_mission',
            field=models.BooleanField(default=False, verbose_name='Allow List Public Mission'),
        ),
        migrations.AddField(
            model_name='workspace',
            name='min_performance_certificate',
            field=models.FloatField(default=0, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)], verbose_name='Minimum Performance Certificate'),
        ),
        migrations.AddField(
            model_name='workspace',
            name='need_approve_channel',
            field=models.BooleanField(default=True, verbose_name='Need Approve Channel'),
        ),
        migrations.AddField(
            model_name='workspace',
            name='need_approve_mission',
            field=models.BooleanField(default=True, verbose_name='Need Approve Mission'),
        ),
    ]
