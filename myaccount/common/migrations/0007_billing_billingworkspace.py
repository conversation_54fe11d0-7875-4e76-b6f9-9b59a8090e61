# Generated by Django 2.2 on 2022-07-04 11:57

import uuid

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("common", "0006_auto_20220629_1130"),
    ]

    operations = [
        migrations.CreateModel(
            name="Billing",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("monthly_plan", models.IntegerField(default=0, verbose_name="Current monthly plan")),
                ("used", models.IntegerField(default=0, verbose_name="Current monthly plan")),
                ("balance", models.IntegerField(default=0, verbose_name="Current monthly plan")),
                ("start_at", models.DateTimeField(verbose_name="Start Date Billing")),
                ("end_date", models.DateTimeField(verbose_name="End Date Billing")),
                ("created_date", models.DateTimeField(auto_now_add=True, verbose_name="Billing created date")),
                (
                    "application",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to="common.Application", verbose_name="Application"
                    ),
                ),
                (
                    "workspace",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="common.Workspace",
                        verbose_name="Workspace billing",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Billing",
                "db_table": "billing",
            },
        ),
        migrations.CreateModel(
            name="BillingWorkspace",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("current_plan", models.IntegerField(default=0, verbose_name="Current monthly plan")),
                ("created_date", models.DateTimeField(auto_now_add=True, verbose_name="Created Date")),
                ("updated_date", models.DateTimeField(auto_now=True, verbose_name="Updated Date")),
                (
                    "application",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to="common.Application", verbose_name="Application"
                    ),
                ),
                (
                    "workspace",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to="common.Workspace", verbose_name="Workspace"
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Workspace application plan",
                "db_table": "workspace_billing_plan",
                "unique_together": {("application", "workspace")},
            },
        ),
    ]
