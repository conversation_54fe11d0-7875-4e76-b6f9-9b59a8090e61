from celery import shared_task
from config import settings
from utils.task_transaction import task_transaction

from common.models import User


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True)
def verifier_user_email(user_id: str) -> None:
    with task_transaction("verifier_user_email") as container:
        user = User.objects.get(id=user_id)
        user.email_verified = container.debounce_client.check_email(user.email)
        user.save()
