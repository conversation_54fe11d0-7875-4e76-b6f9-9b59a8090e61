from django.contrib import admin

from common.models import Application, Billing, BillingCompanyPlan, Company, Role, Service, User, Workspace


@admin.register(Application)
class ApplicationModelAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "name",
    )
    search_fields = (
        "id",
        "name",
    )


@admin.register(Workspace)
class WorkspaceModelAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "name",
        "company",
    )
    search_fields = (
        "id",
        "name",
        "company__name",
        "company__id",
    )


@admin.register(Company)
class CompanyModelAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "name",
    )


@admin.register(Role)
class RoleModelAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "name",
        "key",
        "application",
    )
    search_fields = (
        "id",
        "name",
        "application__name",
    )


@admin.register(Service)
class ServiceModelAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "name",
        "description",
        "application",
    )
    search_fields = (
        "id",
        "name",
        "application__name",
    )


@admin.register(User)
class UserModelAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "name",
        "nickname",
        "email",
        "phone",
    )
    search_fields = (
        "id",
        "name",
        "nickname",
        "email",
        "phone",
    )


@admin.register(Billing)
class BillingModelAdmin(admin.ModelAdmin):
    list_display = (
        "application",
        "company",
        "monthly_plan",
        "used",
        "balance",
        "start_date",
        "end_date",
    )
    search_fields = (
        "application__name",
        "company__name",
    )
    list_filter = (
        "application__name",
        "company__name",
    )


@admin.register(BillingCompanyPlan)
class BillingCompanyPlanModelAdmin(admin.ModelAdmin):
    list_display = (
        "application",
        "company",
        "current_plan",
    )
    search_fields = (
        "application__name",
        "company__name",
    )
    list_filter = (
        "application__name",
        "company__name",
    )
    list_editable = ("current_plan",)
