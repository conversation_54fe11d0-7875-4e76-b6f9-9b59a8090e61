from django.urls import path

from common.views.application_viewset import (
    ApplicationRolesViewSet,
    ApplicationServicesViewSet,
    ApplicationViewSet,
    ApplicationWorkspaceViewSet,
)
from common.views.role_viewset import RoleViewSet

_READ_ONLY = {"get": "list"}

urlpatterns = [
    path("", ApplicationViewSet.as_view(_READ_ONLY), name="applications-list"),
    path("/<uuid:pk>", ApplicationViewSet.as_view(_READ_ONLY), name="application-detail"),
    path("/<uuid:pk>/workspaces", ApplicationWorkspaceViewSet.as_view(_READ_ONLY), name="application-workspace-detail"),
    path("/<uuid:application_id>/roles", RoleViewSet.as_view(_READ_ONLY), name="roles-list"),
    path("/<uuid:application_id>/roles/<uuid:pk>", RoleViewSet.as_view(_READ_ONLY), name="role-details"),
    path("/roles", ApplicationRolesViewSet.as_view(_READ_ONLY), name="application-roles-details"),
    path("/services", ApplicationServicesViewSet.as_view(_READ_ONLY), name="application-services-details"),
]
