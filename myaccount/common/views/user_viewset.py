from authentication.utils.keeps_authentication import KeepsIsAuthenticatedPermission
from config.pagination_config import StandardResultsSetPagination
from django.db import transaction
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import status, viewsets
from rest_framework.exceptions import NotFound, ValidationError
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter
from rest_framework.response import Response

from common.filters.user_filters import UserFilter
from common.serializers.user_serializer import UserInputSerializer, UserSerializer
from common.services.user_service import UserService


class UserViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    serializer_class = UserSerializer
    pagination_class = StandardResultsSetPagination

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filter_class = UserFilter
    search_fields = ("name", "email")
    ordering_fields = ("name",)
    ordering = ("name",)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._service = UserService()

    @property
    def get_serializer(self, *args, **kwargs):
        return self.get_serializer_class()

    def get_serializer_class(self):
        return UserSerializer if self.request.method == "GET" else UserInputSerializer

    def get_queryset(self):
        return self._service.get_queryset(user_id=self.request.user["sub"])

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        workspace_id = request.user.get("client_id")
        instance = self._service.save(
            data=serializer.validate_data, authenticate_user_id=self.request.user["sub"], workspace_id=workspace_id
        )
        serializer = self.get_serializer(instance=instance)
        headers = self.get_success_headers(serializer.data)

        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    def update(self, request, *args, **kwargs):
        data = request.data
        user_id = kwargs.get("pk")
        user = self.get_queryset().filter(id=user_id).first()
        if not user:
            raise NotFound("user_not_found")
        serializer = self.get_serializer(user, data=data, partial=True)
        if "email" in data:
            data.pop("email")

        try:
            serializer.is_valid(raise_exception=True)
        except ValidationError as error:
            return Response(data=error.__dict__, status=status.HTTP_400_BAD_REQUEST)

        user = self._service.update(user.id, serializer.validated_data)
        response = self.serializer_class(user).data
        return Response(data=response, status=status.HTTP_200_OK)

    def partial_update(self, request, *args, **kwargs):
        return self.update(request, *args, **kwargs)

    @transaction.atomic
    def destroy(self, request, *args, **kwargs):
        self._service.delete(self.kwargs["pk"])
        return Response(status=status.HTTP_204_NO_CONTENT)


class UserSetPasswordViewSet(viewsets.ModelViewSet):
    """
    Reset/Change user's password (Keycloak) informing user_id, new password and if this new pass is
    temporary (need change in the first login).

    ---
        {
            "user_id": "uuid",
            "password": "string alfa-numeric",
            "temporary: true/false
        }
    """

    permission_classes = (KeepsIsAuthenticatedPermission,)

    def __init__(self):
        self._service = UserService()

    def get_queryset(self):
        pass

    def create(self, request, *args, **kwargs):
        user = request.data
        self._service.update_user_password(user["user_id"], user["password"], user["temporary"])
        return Response("Password updated", status=status.HTTP_201_CREATED)


class UserSetPasswordBatchViewSet(viewsets.ModelViewSet):
    """
    Reset/Change user's password (Keycloak) informing a list of users to change (user_id), new password and
    if this new pass is temporary (need change in the first login).

    ---
        {
            "user_id": ["uuid", "uuid", ...],
            "password": "string alfa-numeric",
            "temporary: true/false
        }
    """

    permission_classes = (KeepsIsAuthenticatedPermission,)

    def __init__(self):
        self._service = UserService()

    def get_queryset(self):
        pass

    def create(self, request, *args, **kwargs):
        users = request.data.get("user_id")
        password = request.data.get("password")
        temporary = request.data.get("temporary")

        for user in users:
            self._service.update_user_password(user, password, temporary)

        return Response("Password updated", status=status.HTTP_201_CREATED)
