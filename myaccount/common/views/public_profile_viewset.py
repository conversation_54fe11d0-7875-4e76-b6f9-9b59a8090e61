from authentication.utils.keeps_permissions import ANY_PERMISSION, KeepsAuthenticated
from custom.keeps_exception_handler import Keeps<PERSON>rror
from rest_framework import views
from rest_framework.response import Response

from common.models import User
from common.serializers.user_serializer import PublicProfileSerializer
from common.services.user_service import UserService


class PublicProfileView(views.APIView):
    serializer_class = PublicProfileSerializer
    permission_classes = (KeepsAuthenticated,)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._service = UserService()

    def get(self, request, email):
        instance = self._service.get_user_by_email(email)
        serializer_data = self.serializer_class(instance).data
        return Response(serializer_data)


class PublicProfileByIdView(views.APIView):
    serializer_class = PublicProfileSerializer
    permission_classes = ANY_PERMISSION

    def get(self, request, user_id):
        instance = User.objects.filter(id=user_id).first()
        if not instance:
            raise KeepsError(status_code=404, detail="user not found", i18n="user_not_found")
        serializer_data = self.serializer_class(instance).data
        return Response(serializer_data)
