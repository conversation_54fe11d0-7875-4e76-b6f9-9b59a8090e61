from authentication.utils.keeps_permissions import ANY_PERMISSION
from django_filters.rest_framework import DjangoFilter<PERSON><PERSON><PERSON>
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter

from common.serializers.language_preference_serializer import LanguagePreferenceSerializer
from common.services.language_preference_service import LanguagePreferenceService


class LanguagePreferenceViewSet(viewsets.ReadOnlyModelViewSet):
    """
    A viewset that provides the standard actions
    """

    serializer_class = LanguagePreferenceSerializer

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filter_fields = ("name", "status")
    search_fields = ("name",)
    ordering_fields = ("name",)
    ordering = ("name",)

    permission_classes = ANY_PERMISSION

    def __init__(self):
        self._service = LanguagePreferenceService()

    def get_queryset(self):
        return self._service.get_queryset()
