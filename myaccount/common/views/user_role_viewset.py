from authentication.utils.keeps_authentication import KeepsIsAuthenticatedPermission
from config.pagination_config import StandardResultsSetPagination
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON><PERSON><PERSON>, SearchFilter

from common.filters.user_role_filters import UserRoleFilter
from common.models import UserRoleWorkspace
from common.serializers.user_serializer import UserRoleWorkspaceListFullSerializer


class UserRoleViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    serializer_class = UserRoleWorkspaceListFullSerializer
    pagination_class = StandardResultsSetPagination

    permission_classes = (KeepsIsAuthenticatedPermission,)

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filter_class = UserRoleFilter
    search_fields = ("user__name", "user__email", "user__status")
    ordering_fields = ("user__name",)
    ordering = ("user__name",)

    def get_queryset(self):
        workspace_id = self.request.META.get("HTTP_X_CLIENT")
        return UserRoleWorkspace.objects.filter(workspace_id=workspace_id)
