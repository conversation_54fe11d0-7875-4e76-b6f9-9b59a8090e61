import uuid
from copy import deepcopy

from authentication.utils.keeps_permissions import ANY_PERMISSION
from di import Container
from django.core.files.uploadedfile import InMemoryUploadedFile
from django_injector import inject
from rest_framework import status, viewsets
from rest_framework.response import Response
from utils.image.editor import Editor
from utils.utils import create_file_name

from common.services.user_service import UserService


class WorkspaceIconSVGViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    permission_classes = ANY_PERMISSION

    @inject
    def __init__(self, container: Container):
        self._service = UserService()
        self._aws_s3_client = container.aws_s3_client

    def create(self, request, *args, **kwargs):
        file = request.data.get("file")
        file_name = "{}.{}".format(create_file_name(deepcopy(file)), "svg")
        response = self._aws_s3_client.send_file(file, file_name, "workspace-svg", "image/svg+xml")
        return Response(response, status=status.HTTP_200_OK)


class WorkspaceLogoViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    permission_classes = ANY_PERMISSION

    @inject
    def __init__(self, container: Container):
        self._service = UserService()
        self._aws_s3_client = container.aws_s3_client
        self.editor = Editor()

    def create(self, request, *args, **kwargs):
        file_instance: InMemoryUploadedFile = request.data.get("file")
        file_path = self.editor.cut(file_instance.file.read(), 500, 400)
        with open(file_path, "rb") as buffer:
            file_name = f"{uuid.uuid4()}.png"
            response = self._aws_s3_client.send_file(buffer, file_name, "workspace-logo", "image/png")
        return Response(response, status=status.HTTP_200_OK)


class WorkspaceIconViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    permission_classes = ANY_PERMISSION

    @inject
    def __init__(self, container: Container):
        self._service = UserService()
        self._aws_s3_client = container.aws_s3_client
        self.editor = Editor()

    def create(self, request, *args, **kwargs):
        file = request.data.get("file")
        file_path = self.editor.cut(file.file.read(), 200, 200)
        with open(file_path, "rb") as buffer:
            file_name = f"{uuid.uuid4()}.png"
            response = self._aws_s3_client.send_file(buffer, file_name, "workspace-icon", "image/png")
        return Response(response, status=status.HTTP_200_OK)


class UserAvatarViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    permission_classes = ANY_PERMISSION

    @inject
    def __init__(self, container: Container):
        self._service = UserService()
        self._allowed_images = {
            "image/jpeg": "jpg",
            "image/png": "png",
            "image/gif": "gif",
            "image/bmp": "bmp",
            "image/tiff": "tif",
        }
        self._aws_s3_client = container.aws_s3_client
        self.editor = Editor()

    def create(self, request, *args, **kwargs):
        file = request.data.get("file")
        extension = self._allowed_images[file.content_type]
        file_path = self.editor.cut(file.file.read(), 200, 200)
        with open(file_path, "rb") as buffer:
            file_name = f"{uuid.uuid4()}.{extension}"
            response = self._aws_s3_client.send_file(buffer, file_name, "user-avatar", "image/png")
        return Response(response, status=status.HTTP_200_OK)
