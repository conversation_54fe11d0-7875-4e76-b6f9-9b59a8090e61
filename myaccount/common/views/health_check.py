from authentication.utils.keeps_authentication import IsNotAuthenticated
from rest_framework import status, views
from rest_framework.response import Response


class HealthCheckViewSet(views.APIView):
    permission_classes = (IsNotAuthenticated,)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    @staticmethod
    def get(request):
        return Response("server ok", status=status.HTTP_200_OK)
