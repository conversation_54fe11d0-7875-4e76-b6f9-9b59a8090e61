from config.pagination_config import StandardResultsSetPagination
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter
from rest_framework.viewsets import ReadOnlyModelViewSet

from common.models import UserProfileWorkspace
from common.serializers.manager_serializer import ManagerSerializer


class ManagerValuesViewSet(ReadOnlyModelViewSet):
    serializer_class = ManagerSerializer
    pagination_class = StandardResultsSetPagination
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filter_fields = ("manager",)
    search_fields = ("manager",)
    ordering_fields = ("manager",)
    ordering = ("manager",)

    def get_queryset(self):
        workspace_id = self.request.META.get("HTTP_X_CLIENT")
        queryset = UserProfileWorkspace.objects.filter(workspace_id=workspace_id, manager__isnull=False).exclude(
            manager=""
        )
        unique_values = queryset.values_list("manager", flat=True).distinct()
        return unique_values
