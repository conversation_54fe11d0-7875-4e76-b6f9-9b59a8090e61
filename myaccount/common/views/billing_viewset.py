from authentication.utils.keeps_permissions import ANY_PERMISSION
from django_injector import inject
from rest_framework import status, views
from rest_framework.response import Response

from common.services.billing_konquest_service import BillingKonquestService
from common.services.billing_smartzap_service import BillingSmartzapService


class BillingView(views.APIView):
    permission_classes = ANY_PERMISSION

    @inject
    def __init__(self):
        super().__init__()
        self._billing_konquest = BillingKonquestService()
        self._billing_smartzap = BillingSmartzapService()

    def get(self, request, *args, **kwargs):
        start_date = request.query_params.get("start_date", None)
        end_date = request.query_params.get("end_date", None)
        self._billing_konquest.generate(start_date, end_date)
        self._billing_smartzap.generate(start_date, end_date)

        return Response(status.HTTP_200_OK)
