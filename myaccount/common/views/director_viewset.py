from config.pagination_config import StandardResultsSetPagination
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import OrderingFilter, SearchFilter
from rest_framework.viewsets import ReadOnlyModelViewSet

from common.models import UserProfileWorkspace
from common.serializers.director_serializer import DirectorSerializer


class DirectorValuesViewSet(ReadOnlyModelViewSet):
    serializer_class = DirectorSerializer
    pagination_class = StandardResultsSetPagination
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filter_fields = ("director",)
    search_fields = ("director",)
    ordering_fields = ("director",)
    ordering = ("director",)

    def get_queryset(self):
        workspace_id = self.request.META.get("HTTP_X_CLIENT")
        queryset = UserProfileWorkspace.objects.filter(workspace_id=workspace_id, director__isnull=False).exclude(
            director=""
        )
        unique_values = queryset.values_list("director", flat=True).distinct()
        return unique_values
