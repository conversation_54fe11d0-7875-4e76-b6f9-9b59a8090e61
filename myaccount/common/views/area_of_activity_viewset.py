from config.pagination_config import StandardResultsSetPagination
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter
from rest_framework.viewsets import ReadOnlyModelViewSet

from common.models import UserProfileWorkspace
from common.serializers.area_of_activity_serializer import AreaOfActivitySerializer


class AreaOfActivityViewSet(ReadOnlyModelViewSet):
    serializer_class = AreaOfActivitySerializer
    pagination_class = StandardResultsSetPagination
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filter_fields = ("area_of_activity",)
    search_fields = ("area_of_activity",)
    ordering_fields = ("area_of_activity",)
    ordering = ("area_of_activity",)

    def get_queryset(self):
        workspace_id = self.request.META.get("HTTP_X_CLIENT")
        queryset = UserProfileWorkspace.objects.filter(
            workspace_id=workspace_id, area_of_activity__isnull=False
        ).exclude(area_of_activity="")
        unique_values = queryset.values_list("area_of_activity", flat=True).distinct()
        return unique_values
