from authentication.utils.keeps_permissions import ANY_PERMISSION
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter

from common.serializers.service_serializer import ServiceSerializer
from common.services.service_service import ServiceService


class ServiceViewSet(viewsets.ReadOnlyModelViewSet):
    """
    A viewset that provides the standard actions
    """

    serializer_class = ServiceSerializer

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filter_fields = ("name", "application")
    search_fields = ("name", "description")
    ordering_fields = ("name",)
    ordering = ("name",)

    permission_classes = ANY_PERMISSION

    def __init__(self):
        self._service = ServiceService()

    def get_queryset(self):
        return self._service.get_queryset()
