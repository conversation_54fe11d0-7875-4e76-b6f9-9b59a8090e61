# -*- coding: utf-8 -*-

from authentication.utils.keeps_permissions import ANY_PERMISSION
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter

from common.serializers.workspace_serializer import WorkspaceSerializer
from common.services.user_service import UserService


class UserWorkspaceViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    serializer_class = WorkspaceSerializer

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    permission_classes = ANY_PERMISSION
    filter_fields = ("name", "status")
    search_fields = ("name",)
    ordering_fields = ("created_date",)
    ordering = ("created_date",)

    def __init__(self):
        self._service = UserService()

    def get_queryset(self):
        return self._service.get_workspaces_queryset(self.request.user["sub"], self.kwargs["user_id"])
