from authentication.utils.keeps_permissions import ANY_PERMISSION, KeepsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter

from common.models import Role, Service, ServiceWorkspace, UserRoleWorkspace, Workspace
from common.serializers.application_serializer import (
    ApplicationRoleSerializer,
    ApplicationSerializer,
    ApplicationServiceSerializer,
)
from common.serializers.workspace_serializer import WorkspaceApplicationSerializer
from common.services.application_service import ApplicationService
from common.services.base_service import BaseService


class ApplicationViewSet(viewsets.ReadOnlyModelViewSet):
    """
    A viewset that provides the standard actions
    """

    serializer_class = ApplicationSerializer

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filter_fields = ("name",)
    search_fields = ("name", "description")
    ordering_fields = ("name",)
    ordering = ("name",)

    permission_classes = ANY_PERMISSION

    def __init__(self):
        self._service = ApplicationService()

    def get_queryset(self):
        return self._service.get_queryset()


class ApplicationRolesViewSet(viewsets.ReadOnlyModelViewSet):
    """
    A viewset that provides the standard actions
    """

    serializer_class = ApplicationRoleSerializer

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filter_fields = ("name",)
    search_fields = ("name", "description")
    ordering_fields = ("name",)
    ordering = ("name",)

    permission_classes = ANY_PERMISSION

    def __init__(self):
        self._service = ApplicationService()

    def get_queryset(self):
        workspace_uuid = self.request.user.get("client_id", None)
        if workspace_uuid:
            return self._service.get_application_service_by_workspace(workspace_uuid)

        return self._service.get_queryset()


class ApplicationServicesViewSet(viewsets.ReadOnlyModelViewSet):
    """
    A viewset that provides the standard actions
    """

    serializer_class = ApplicationServiceSerializer

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filter_fields = ("name",)
    search_fields = ("name", "description")
    ordering_fields = ("name",)
    ordering = ("name",)

    permission_classes = ANY_PERMISSION

    def __init__(self):
        self._service = ApplicationService()

    def get_queryset(self):
        return self._service.get_queryset()


class ApplicationWorkspaceViewSet(viewsets.ReadOnlyModelViewSet):
    """
    List all workspaces allowed for logged user that have application ID.
    """

    serializer_class = WorkspaceApplicationSerializer
    permission_classes = (KeepsAuthenticated,)

    def __init__(self):
        self._service = BaseService()

    def get_queryset(self):
        user_id = self.request.user["sub"]
        application_id = str(self.kwargs["pk"])

        application_roles_filter = Role.objects.filter(application_id=application_id, status=True).values_list(
            "id", flat=True
        )
        workspaces_filter = UserRoleWorkspace.objects.filter(
            user_id=user_id, role__in=application_roles_filter
        ).values_list("workspace_id", flat=True)

        services = Service.objects.filter(application_id=application_id).values_list("id")
        service_workspace_filter = ServiceWorkspace.objects.filter(
            workspace_id__in=workspaces_filter, service_id__in=services
        ).values_list("workspace_id", flat=True)

        return Workspace.objects.filter(id__in=service_workspace_filter)
