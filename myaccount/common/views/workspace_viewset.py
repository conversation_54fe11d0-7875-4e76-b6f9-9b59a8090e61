from authentication.utils.keeps_permissions import KeepsAuthenticated
from custom.keeps_exception_handler import KeepsNotFoundCompanyError
from di import Container
from django_filters.rest_framework import DjangoFilterBackend
from django_injector import inject
from rest_framework import status, viewsets
from rest_framework.filters import Ordering<PERSON><PERSON>er, SearchFilter
from rest_framework.permissions import AllowAny
from rest_framework.response import Response

from common.models import Workspace
from common.serializers.workspace_serializer import WorkspaceInputSerializer, WorkspaceSerializer


class WorkspaceViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    serializer_class = WorkspaceInputSerializer

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filter_fields = ("name",)
    search_fields = ("name", "description")
    ordering_fields = ("name",)
    ordering = ("name",)

    permission_classes = (KeepsAuthenticated,)

    @inject
    def __init__(self, container: Container, **kwargs):
        super().__init__(**kwargs)
        self._service = container.workspace_service()

    def get_queryset(self):
        return self._service.get_queryset(self.request.user["sub"])

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        instance = self._service.save(self.request.user["sub"], serializer.validated_data)
        headers = self.get_success_headers(serializer.data)

        return Response(
            WorkspaceSerializer(instance, context=self.get_serializer_context()).data,
            status=status.HTTP_201_CREATED,
            headers=headers,
        )

    def update(self, request, *args, **kwargs):
        workspace_id = kwargs["pk"]
        serializer = self.get_serializer(data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        instance = self._service.update(workspace_id, serializer.validated_data, self.request.user.get("sub"))

        return Response(self.serializer_class(instance).data, status=status.HTTP_201_CREATED)

    def partial_update(self, request, *args, **kwargs):
        return self.update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        response = super().destroy(request, *args, **kwargs)
        return response


class WorkspaceThemeViewSet(viewsets.ReadOnlyModelViewSet):
    """
    A viewset that provides the standard actions
    """

    permission_classes = (KeepsAuthenticated,)

    def list(self, request, *args, **kwargs):
        workspace = Workspace.objects.filter(id=self.kwargs.get("pk")).first()
        if workspace:
            return Response({"theme_id": workspace.theme_id}, status=status.HTTP_200_OK)

        raise KeepsNotFoundCompanyError()


class WorkspaceHashLoginUrlViewset(viewsets.ReadOnlyModelViewSet):
    permission_classes = (AllowAny,)

    def retrieve(self, request, *args, **kwargs):
        hash_id = self.kwargs.get("hash_id")
        workspace = Workspace.objects.filter(hash_id=hash_id).first()
        if not workspace:
            raise KeepsNotFoundCompanyError()
        return Response({"custom_login_url": workspace.custom_login_url}, status=status.HTTP_200_OK)
