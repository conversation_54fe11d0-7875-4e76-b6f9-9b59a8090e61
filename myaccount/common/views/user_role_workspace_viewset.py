from rest_framework import generics, mixins, status
from rest_framework.response import Response

from common.serializers.user_serializer import UserRoleWorkspaceListFullSerializer, UserRoleWorkspaceSerializer
from common.services.user_service import UserService


class UserRoleWorkspaceViewSet(mixins.CreateModelMixin, mixins.DestroyModelMixin, generics.GenericAPIView):
    """
    A viewset that provides the standard actions
    """

    serializer_class = UserRoleWorkspaceSerializer
    serializer_class_list = UserRoleWorkspaceListFullSerializer

    def __init__(self):
        super().__init__()
        self._service = UserService()

    def get_queryset(self):
        return self._service.get_queryset(self.request.user["sub"])

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user_logged = self.request.user["sub"]
        data = serializer.validated_data
        instance = self._service.add_role_application(user_logged, data)
        serializer = self.serializer_class_list(instance=instance)
        headers = self.get_success_headers(serializer.data)

        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    def delete(self, request, *args, **kwargs):
        # Services
        user_logged = self.request.user["sub"]
        self._service.delete_role_application(user_logged, self.kwargs["pk"])

        return Response(status=status.HTTP_204_NO_CONTENT)
