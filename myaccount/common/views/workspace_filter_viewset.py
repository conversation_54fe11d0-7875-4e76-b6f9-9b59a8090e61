from authentication.utils.keeps_permissions import KeepsAuthenticated
from rest_framework import viewsets
from rest_framework.response import Response

from common.models.enrollment_status_filter import EnrollmentStatusFilter
from common.models.workspace_filter_settings import WorkspaceFilterSetting
from common.serializers.workspace_filter_serializer import WorkspaceFilterSettingSerializer


class WorkspaceFilterSettingViewSet(viewsets.ModelViewSet):
    serializer_class = WorkspaceFilterSettingSerializer
    permission_classes = (KeepsAuthenticated,)

    def get_queryset(self):
        workspace_id = self.request.headers.get("x-client")
        return WorkspaceFilterSetting.objects.filter(workspace_id=workspace_id)

    def perform_create(self, serializer):
        serializer.save()

    def list(self, request, *args, **kwargs):
        workspace_id = self.request.headers.get("x-client")
        existing_settings = self.get_queryset().exists()
        if not existing_settings:
            enrollment_status_filters = EnrollmentStatusFilter.objects.all()
            filter_settings = [
                WorkspaceFilterSetting(workspace_id=workspace_id, enrollment_status_filter=filter, is_enabled=True)
                for filter in enrollment_status_filters
            ]
            WorkspaceFilterSetting.objects.bulk_create(filter_settings)
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def partial_update(self, request, *args, **kwargs):
        workspace_id = self.request.headers.get("x-client")
        id = kwargs.get("pk")
        filter_setting = WorkspaceFilterSetting.objects.get(id=id, workspace_id=workspace_id)
        serializer = self.get_serializer(filter_setting, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        return Response(serializer.data)
