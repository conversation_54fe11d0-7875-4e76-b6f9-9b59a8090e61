from authentication.utils.keeps_permissions import ANY_PERMISSION
from django_filters.rest_framework import DjangoFilter<PERSON><PERSON><PERSON>
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON><PERSON>er, SearchFilter

from common.serializers.job_function_serializer import JobFunctionSerializer
from common.services.job_service import JobPositionService


# pylint: disable=protected-access
class JobFunctionViewSet(viewsets.ModelViewSet):  # Altere esta linha
    serializer_class = JobFunctionSerializer

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filter_fields = ("name", "workspace", "created_date", "updated_date")
    search_fields = ("name",)
    ordering_fields = ("name", "created_date", "updated_date")
    ordering = ("name",)

    permission_classes = ANY_PERMISSION

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._service = JobPositionService()

    def get_queryset(self):
        workspace_id = self.request.META.get("HTTP_X_CLIENT")
        return self._service.get_queryset(self.request.user["sub"]).filter(workspace=workspace_id)

    def perform_create(self, serializer):
        workspace_id = self.request.META.get("HTTP_X_CLIENT")
        serializer.save(workspace_id=workspace_id)
