from authentication.utils.keeps_permissions import ANY_PERMISSION
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON><PERSON>er, SearchFilter

from common.serializers.role_serializer import RoleSerializer
from common.services.role_service import RoleService


class RoleViewSet(viewsets.ReadOnlyModelViewSet):
    """
    A viewset that provides the standard actions
    """

    serializer_class = RoleSerializer

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filter_fields = ("name", "key")
    search_fields = ("name", "description", "key")
    ordering_fields = ("name", "key")
    ordering = ("key",)

    permission_classes = ANY_PERMISSION

    def __init__(self):
        self._service = RoleService()

    def get_queryset(self):
        return self._service.get_queryset(self.kwargs["application_id"])
