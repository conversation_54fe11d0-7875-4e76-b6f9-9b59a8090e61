from authentication.utils.keeps_permissions import ANY_PERMISSION
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter

from common.serializers.company_serializer import CompanySerializer
from common.services.company_service import CompanyService


class CompanyViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    serializer_class = CompanySerializer

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filter_fields = ("name",)
    search_fields = ("name", "description")
    ordering_fields = ("name",)
    ordering = ("name",)

    permission_classes = ANY_PERMISSION

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._service = CompanyService()

    def get_queryset(self):
        user_id = self.request.user["sub"]
        return self._service.list_allowed_companies(user_id)
