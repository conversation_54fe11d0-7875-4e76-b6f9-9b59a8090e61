import uuid

from authentication.utils.keeps_authentication import KeepsIsAuthenticatedPermission
from authentication.utils.keeps_permissions import ANY_PERMISSION
from config.pagination_config import StandardResultsSetPagination
from config.settings import TEMP_UPLOAD_FOLDER
from constants import ID, QUERY_PARAM_FIELDS
from custom.keeps_exception_handler import KeepsBadRequestError
from django.core.files.storage import default_storage
from django.shortcuts import get_object_or_404
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import status, viewsets
from rest_framework.exceptions import ValidationError
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from utils.utils import temp_file

from common.filters.user_filters import UserFilter
from common.models import User, UserRoleWorkspace
from common.serializers.user_serializer import (
    PublicProfileSerializer,
    UserIdsSerializer,
    UserInputSerializer,
    UserSerializer,
    WorkspaceUserImportSerializer,
)
from common.serializers.workspace_user_serializer import WorkspaceUserInputSerializer
from common.services.import_user_service import ImportUserService
from common.services.user_service import UserService
from common.services.workspace_user_service import WorkspaceUserService


class WorkspaceUserPublicViewSet(viewsets.ReadOnlyModelViewSet):
    """
    A viewset that provides the list action
    """

    serializer_class = PublicProfileSerializer
    pagination_class = StandardResultsSetPagination
    permission_classes = (KeepsIsAuthenticatedPermission,)

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filter_fields = ("name", "email", "status")
    search_fields = (
        "name",
        "email",
    )
    ordering_fields = (
        "created_date",
        "name",
        "email",
    )
    ordering = (
        "created_date",
        "name",
        "email",
    )

    def __init__(self):
        self._service = WorkspaceUserService()
        self._user_service = UserService()

    def get_queryset(self):
        workspace_id = self.kwargs["workspace_id"]
        users = UserRoleWorkspace.objects.filter(workspace_id=workspace_id).values_list("user", flat=True)
        return User.objects.filter(id__in=users)


class WorkspaceUserViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    pagination_class = StandardResultsSetPagination

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filter_class = UserFilter
    search_fields = (
        "name",
        "email",
    )
    ordering_fields = (
        "created_date",
        "name",
        "email",
    )
    ordering = (
        "created_date",
        "name",
        "email",
    )

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._service = WorkspaceUserService()
        self._user_service = UserService()

    @property
    def serializer_class(self):
        return self.get_serializer_class()

    def get_serializer_class(self):
        if self.request.method == "POST":
            return WorkspaceUserInputSerializer
        if self.request.method == "GET" and (self.request.query_params.get(QUERY_PARAM_FIELDS) == ID):
            return UserIdsSerializer
        if self.request.method == "PUT":
            return UserInputSerializer
        return UserSerializer

    def get_queryset(self):
        self.request.workspace_id = self.kwargs["workspace_id"]
        return self._service.get_users_queryset(self.request.user["sub"], self.kwargs["workspace_id"])

    def create(self, request, *args, **kwargs):
        data = request.data
        workspace_id = self.kwargs["workspace_id"]
        serializer = self.serializer_class(data=data, context={"workspace_id": workspace_id})
        try:
            serializer.is_valid(True)
        except ValidationError as exc:
            raise KeepsBadRequestError(detail=str(exc), i18n="validation_error") from exc
        data = serializer.validated_data
        users = self._service.save_users(data.get("users"), data.get("permissions"), workspace_id)
        users_serializer = UserSerializer(users, many=True).data
        headers = self.get_success_headers(users_serializer)
        return Response(users_serializer, status=status.HTTP_201_CREATED, headers=headers)

    def update(self, request, *args, **kwargs):
        # todo: deletar isso após refatorar o front para apontar para a api user/<uuid:pk> ao atualizar o usuário
        data = request.data
        workspace_id = self.kwargs["workspace_id"]
        user_id = kwargs.get("pk")
        if "email" in data:
            request.data.pop("email")
        if data.get("birthday") == "":
            data["birthday"] = None

        user = get_object_or_404(User, pk=user_id)
        serializer = self.serializer_class(data=data, context={"workspace_id": workspace_id})

        try:
            serializer.is_valid(raise_exception=True)
        except ValidationError as error:
            return Response(data=error.__dict__, status=status.HTTP_400_BAD_REQUEST)

        user = self._user_service.update(user_id, data, workspace_id)
        response = UserSerializer(user).data
        return Response(data=response, status=status.HTTP_200_OK)

    def destroy(self, request, *args, **kwargs):
        users = UserRoleWorkspace.objects.filter(workspace_id=self.kwargs["workspace_id"], user_id=kwargs.get("pk"))
        if users:
            self.perform_destroy(users)
            return Response(status=status.HTTP_204_NO_CONTENT)

        return Response(status=status.HTTP_404_NOT_FOUND)


class WorkspaceUserImportViewSet(viewsets.ViewSet):
    """
    A viewset that provides the standard actions
    """

    serializer_class = WorkspaceUserImportSerializer
    permission_classes = ANY_PERMISSION

    def __init__(self):
        self.user_import_service = ImportUserService()

    # todo: refactor viewset (use serializers)
    def create(self, request, *args, **kwargs):
        """
        Import user to the workspace and set their roles.

        File xls could contain information to connect the user to one Identity provider (last 3 columns). These columns
        is identity_provider_alias, provider_user_id, provider_username and if are filled the field self_signup will be
        set to True.

        The field self_signup control if the user will receive onboard communications and Keycloak will set the password
        to temporary. In case of user have an Identity Provider is not necessary send onboarding e-mail or reset pass.

        :param request: need receive file (form data) and roles (string if roles uuid and separated by comma)
        :return: errors (users with problem), imported (users ok)
        """
        workspace_id = kwargs.get("workspace_id")
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(True)
        data = serializer.validated_data
        file = temp_file(data["file"])

        users = self.user_import_service.parser_users(file, workspace_id)
        imported, errors = self.user_import_service.import_users(
            users, data["roles"][0], workspace_id, data["temporary_password"]
        )

        _imported_serialized = []

        for _user in imported:
            _imported_serialized.append(UserSerializer(_user).data)

        return Response(data={"errors": errors, "imported": _imported_serialized}, status=status.HTTP_200_OK)

    @staticmethod
    def temp_file(data):
        file = data.get("file")
        extension = file.name.split(".")[1]
        filename = f"{str(uuid.uuid4())}.{extension}"

        with open(default_storage.path(filename), "wb+") as destination:
            for chunk in file.chunks():
                destination.write(chunk)

        return f"{TEMP_UPLOAD_FOLDER}/{filename}"


class WorkspaceUserSendInviteViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    permission_classes = ANY_PERMISSION

    def __init__(self):
        self._service = WorkspaceUserService()
        self._user_service = UserService()

    def get_queryset(self):
        return self._service.get_users_queryset(self.request.user["sub"], self.kwargs["workspace_id"])

    def create(self, request, *args, **kwargs):
        if "users_id" not in request.data:
            raise ValueError("users_id key is required")

        users_id = request.data["users_id"]

        if not isinstance(users_id, list):
            raise ValueError("users_id must be list")

        workspace_id = str(kwargs.get("workspace_id"))
        response = self._user_service.send_invite(users_id, self.request.user["sub"], workspace_id)
        return Response(response, status=status.HTTP_200_OK)


class WorkspaceSignUpViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    serializer_class = UserSerializer
    permission_classes = (AllowAny,)

    def __init__(self):
        self._service = UserService()

    def get_queryset(self):
        pass

    def create(self, request, *args, **kwargs):
        user = self._service.self_sign_up(request.data, self.kwargs["workspace_id"])
        users_serializer = self.serializer_class(user).data
        headers = self.get_success_headers(users_serializer)
        return Response(users_serializer, status=status.HTTP_201_CREATED, headers=headers)
