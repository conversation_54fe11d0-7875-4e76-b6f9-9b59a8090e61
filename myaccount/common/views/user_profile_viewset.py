from config.pagination_config import StandardResultsSetPagination
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter

from common.serializers.user_serializer import UserProfileListSerializer, UserProfileSerializer
from common.services.user_service import UserService
from common.services.workspace_user_service import WorkspaceUserService


class UserProfileViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    pagination_class = StandardResultsSetPagination
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._service = WorkspaceUserService()
        self._user_service = UserService()

    @property
    def serializer_class(self):
        return self.get_serializer_class()

    def get_serializer_class(self):
        return UserProfileListSerializer if self.request.method == "GET" else UserProfileSerializer

    def get_queryset(self):
        user_id = self.request.user.get("sub")
        workspace_id = self.kwargs["workspace_id"]
        self._service.check_permission_to_manage_workspace(user_id, workspace_id)
        return self._service.get_user_profiles(user_id, workspace_id)

    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        request.data["workspace"] = self.kwargs["workspace_id"]
        self._service.check_permission_to_manage_workspace(self.request.user.get("sub"), self.kwargs["workspace_id"])
        return super().create(request, *args, **kwargs)

    def update(self, request, *args, **kwargs):
        request.data["workspace"] = self.kwargs["workspace_id"]
        return super().update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)
