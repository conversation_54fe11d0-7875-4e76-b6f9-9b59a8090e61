from authentication.utils.keeps_permissions import KeepsAuthenticated
from rest_framework import status, viewsets
from rest_framework.response import Response

from common.serializers.gamification_serializer import GamificationWorkspaceListSerializer
from common.services.gamification_service import GamificationService


class GamificationWorkspaceViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions to enable and disable gamification and gamification's ranking.
    """

    serializer_class = GamificationWorkspaceListSerializer
    permission_classes = (KeepsAuthenticated,)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._service = GamificationService()

    def get_queryset(self):
        workspace_id = self.request.headers.get("x-client")
        return self._service.get_queryset(workspace_id)

    def update(self, request, *args, **kwargs):
        workspace_id = self.request.headers.get("x-client")
        ranking_id = kwargs.get("ranking_id")
        status_ranking = request.data.get("status")
        message = self._service.edit_ranking(workspace_id, ranking_id, status_ranking)
        return Response({"message": message}, status=status.HTTP_200_OK)
