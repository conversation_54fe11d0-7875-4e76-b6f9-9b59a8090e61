from authentication.utils.keeps_permissions import KeepsAuthenticated
from rest_framework import views
from rest_framework.response import Response

from common.serializers.user_serializer import UserSerializer
from common.services.user_service import UserService


class UserInfoView(views.APIView):
    serializer_class = UserSerializer
    permission_classes = (KeepsAuthenticated,)

    def __init__(self):
        super().__init__()
        self._service = UserService()

    def get(self, request, *args, **kwargs):
        instance = self._service.get_user_by_email(email=self.request.user["email"])
        serializer_data = self.serializer_class(instance).data
        return Response(serializer_data)
