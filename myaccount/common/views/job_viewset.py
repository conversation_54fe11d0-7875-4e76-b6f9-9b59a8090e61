from authentication.utils.keeps_permissions import ANY_PERMISSION
from django_filters.rest_framework import DjangoFilter<PERSON><PERSON>end
from rest_framework import viewsets
from rest_framework.filters import Order<PERSON><PERSON><PERSON><PERSON>, SearchFilter

from common.serializers.job_serializer import JobSerializer
from common.services.job_service import JobService

# pylint: disable=protected-access


class JobViewSet(viewsets.ModelViewSet):
    serializer_class = JobSerializer

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filter_fields = ("name", "workspace", "created_date", "updated_date")
    search_fields = ("name",)
    ordering_fields = ("name", "created_date", "updated_date")
    ordering = ("name",)

    permission_classes = ANY_PERMISSION

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._service = JobService()

    def get_queryset(self):
        workspace_id = self.request.META.get("HTTP_X_CLIENT")
        return self._service.get_queryset(self.request.user["sub"]).filter(workspace=workspace_id)

    def perform_create(self, serializer):
        workspace_id = self.request.META.get("HTTP_X_CLIENT")
        serializer.save(workspace_id=workspace_id)
