from authentication.utils.keeps_permissions import Keeps<PERSON>uthenticated
from di import Container
from django_filters.rest_framework import DjangoFilter<PERSON><PERSON><PERSON>
from django_injector import inject
from rest_framework import status, viewsets
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter
from rest_framework.response import Response

from common.serializers.service_workspace_serializer import (
    ServiceWorkspaceListSerializer,
    ServiceWorkspacePostSerializer,
)


class ServiceWorkspaceViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    serializer_class = ServiceWorkspaceListSerializer
    permission_classes = (KeepsAuthenticated,)

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filter_fields = ("service__application", "service")
    search_fields = ("service__application__name", "service__name")
    ordering_fields = ("service__application__name", "service__name")
    ordering = ("service__name",)

    @inject
    def __init__(self, container: Container, **kwargs):
        super().__init__(**kwargs)
        self._service = container.workspace_service()

    def get_queryset(self):
        return self._service.get_services_workspace_queryset(
            self.request.user.get("sub", None), self.kwargs["workspace_id"]
        )

    def create(self, request, *args, **kwargs):
        workspace_id = request.headers.get("x-client") or self.kwargs["workspace_id"]
        service_id = self.kwargs["service_id"]

        instance = self._service.add_service_in_workspace(workspace_id, service_id)
        serializer = ServiceWorkspacePostSerializer(instance)
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    def destroy(self, request, *args, **kwargs):
        workspace_id = request.headers.get("x-client") or self.kwargs["workspace_id"]
        service_id = self.kwargs["service_id"]

        deleted = self._service.delete_service_from_workspace(workspace_id, service_id)

        if not deleted:
            return Response(status=status.HTTP_404_NOT_FOUND)

        return Response(status=status.HTTP_204_NO_CONTENT)
