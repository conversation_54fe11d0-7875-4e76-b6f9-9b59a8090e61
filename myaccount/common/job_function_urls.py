from django.urls import path

from common.views.job_function_viewset import JobFunctionViewSet

LIST = {"get": "list", "post": "create"}
DETAIL = {"get": "retrieve", "put": "update", "patch": "partial_update", "delete": "destroy"}

urlpatterns = [
    path("", JobFunctionViewSet.as_view(LIST), name="job-function-list"),
    path("/<uuid:pk>", JobFunctionViewSet.as_view(DETAIL), name="job-function-detail"),
]
