import os
import shutil
import uuid

import mock
from django.test import TestCase, override_settings
from model_mommy import mommy

from common.models import Role, User, Workspace
from common.services.import_user_service import ImportUserService
from common.models.language_preference import LanguagePreference

kc_user_mock = "0176b6c7-56be-45b0-a801-d357f754342e"
KEYCLOAK_SAVE_IDENTITY_PROVIDER = "utils.keycloak.KeyCloakUserManager.save_identity_provider"


@override_settings(SUSPEND_SIGNALS=True)
@mock.patch("common.services.user_service.UserService.get_or_create_keycloak_user", return_value=(uuid.uuid4()))
@mock.patch("common.services.user_service.UserService.update_user_password")
class ImportUserServiceTestCase(TestCase):

    def setUp(self):
        self.workspace = mommy.make(Workspace, id=uuid.uuid4())
        self.role_account_admin = mommy.make(Role, id=uuid.uuid4(), key="account_admin")
        self.role = mommy.make(Role, id=uuid.uuid4())
        mommy.make(LanguagePreference, name='pt-br')
        self.user = mommy.make(User, email="<EMAIL>")
        self.service = ImportUserService()

    @mock.patch(KEYCLOAK_SAVE_IDENTITY_PROVIDER)
    def test_parser_user_xls_with_job(self, save_identity_provider: mock.MagicMock, *args):
        self.template_sheet = os.path.dirname(os.path.dirname(__file__)) + "/tests/import_users_with_job.xlsx"
        self.filename = os.path.dirname(os.path.dirname(__file__)) + "/tests/temp.xlsx"
        shutil.copyfile(self.template_sheet, self.filename)

        response = self.service.parser_users(self.filename, self.workspace.id)
        imported, errors = self.service.import_users(response, [self.role.id], self.workspace.id, False)
        self.assertEqual(len(imported), 2)