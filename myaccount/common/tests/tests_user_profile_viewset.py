import uuid
from unittest import mock

from django.test import TestCase
from django.urls import reverse
from mock_targets import CHECK_ROLE
from model_mommy import mommy
from rest_framework.test import APIClient
from common.models.job import Job

from common.models import Role, User, UserProfileWorkspace, UserRoleWorkspace, Workspace


@mock.patch("tasks.platform_service.PlatformIntegrationService.publish", return_value={})
@mock.patch(CHECK_ROLE, return_value=True)
@mock.patch("common.services.user_service.UserService._update_keycloak_user")
class UserProfileViewSetTestCase(TestCase):
    fixtures = ["application", "services", "language_preference", "roles"]

    def setUp(self):
        self.client = APIClient()
        self.user = mommy.make(User, id=uuid.uuid4())
        self.workspace = mommy.make(Workspace, id=uuid.uuid4())
        self.account_user_role = Role.objects.get(id="3b16b975-0297-4edf-950b-e3700b0d0d01")
        mommy.make(UserRoleWorkspace, user=self.user, workspace=self.workspace, role=self.account_user_role)

        self.headers = {"HTTP_X_CLIENT_ID": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})

    def test_detail_user_profile(self, integration, check_role, update_keycloak_user):
        profile = mommy.make(UserProfileWorkspace, user=self.user, workspace=self.workspace)
        self.url = reverse("workspace-users-profile", args=[str(self.workspace.id), str(profile.id)])

        response = self.client.get(self.url, **self.headers, format="json")
        data = response.json()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(str(profile.id), data["id"])

    def test_not_allowed_when_detail_stranger_workspace_user_profile(
        self, integration, check_role, update_keycloak_user
    ):
        workspace = mommy.make(Workspace, id=uuid.uuid4())
        profile = mommy.make(UserProfileWorkspace, user=self.user, workspace=workspace)
        self.url = reverse("workspace-users-profile", args=[str(workspace.id), str(profile.id)])

        response = self.client.get(self.url, **self.headers, format="json")

        self.assertEqual(response.status_code, 403)

    def test_update_user_profiles(self, integration, check_role, update_keycloak_user):
        profile = mommy.make(UserProfileWorkspace, user=self.user, workspace=self.workspace)
        self.url = reverse("workspace-users-profile", args=[str(self.workspace.id), str(profile.id)])
        payload = {"manager": "New Manager", "job": "NEW JOB"}

        response = self.client.patch(self.url, data=payload, **self.headers, format="json")
        data = response.json()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(payload["manager"], data["manager"])
        self.assertEqual(payload["job"], data["job"])

    def test_update_user_profile_with_job_uuid(self, integration, check_role, update_keycloak_user):
        profile = mommy.make(UserProfileWorkspace, user=self.user, workspace=self.workspace)
        self.url = reverse("workspace-users-profile", args=[str(self.workspace.id), str(profile.id)])
        job = mommy.make(Job, workspace=self.workspace, name="NEW JOB")
        payload = {"job": str(job.id)}

        response = self.client.patch(self.url, data=payload, **self.headers, format="json")
        data = response.json()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(job.name, data["job"])

    def test_create_user_profiles(self, integration, check_role, update_keycloak_user):
        self.url = reverse("workspace-users-profiles", args=[str(self.workspace.id)])
        payload = {"user": self.user.id, "workspace": self.workspace.id, "manager": "New Manager"}

        response = self.client.post(self.url, data=payload, **self.headers, format="json")
        data = response.json()

        self.assertEqual(response.status_code, 201)
        self.assertEqual(payload["manager"], data["manager"])
