import uuid
from datetime import datetime
from unittest import mock

from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from common.models import Role, User, UserRoleWorkspace, Workspace
from mock_targets import CHECK_ROLE


@mock.patch("tasks.platform_service.PlatformIntegrationService.publish", return_value={})
@mock.patch(CHECK_ROLE, return_value=True)
@mock.patch("common.services.user_service.UserService._update_keycloak_user")
class UserViewSetTestCase(TestCase):
    fixtures = ["application", "services", "language_preference", "roles"]

    def setUp(self):
        self.client = APIClient()
        self.user = mommy.make(User, id=uuid.uuid4())
        self.workspace = mommy.make(Workspace, id=uuid.uuid4())
        mommy.make(UserRoleWorkspace, user=self.user, workspace=self.workspace)
        self.headers = {"HTTP_X_CLIENT_ID": str(self.workspace.id)}
        self.myaccount_user_role = Role.objects.get(id="3b16b975-0297-4edf-950b-e3700b0d0d01")
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})
        self.url = reverse("user-list")

    @mock.patch("tasks.notifications.notify_user.delay")
    @mock.patch("tasks.platform_service.PlatformIntegrationService.publish")
    def test_konquest_user_filter(self, mock_notify_user, mock_publish, integration, check_role, update_keycloak_user):
        konquest_user_role = Role.objects.get(id="a6d23aea-807e-4374-964e-c725b817742d")
        mommy.make(UserRoleWorkspace, user=self.user, workspace=self.workspace, role=self.myaccount_user_role)
        user_konquest = mommy.make(User)
        mommy.make(
            UserRoleWorkspace,
            user=user_konquest,
            workspace=self.workspace,
            role=konquest_user_role,
        )
        response = self.client.get(
            f"{self.url}?application_id={konquest_user_role.application_id}", **self.headers, format="json"
        )
        response_user_ids = [user["id"] for user in response.json().get("results")]

        self.assertEqual(response.status_code, 200)
        self.assertIn(str(user_konquest.id), response_user_ids)
        self.assertNotIn(str(self.user.id), response_user_ids)

    def test_user_update_with_blank_birthday(self, integration, check_role, update_keycloak_user):
        url = reverse("user-detail", args=[str(self.user.id)])
        data = {"birthday": ""}

        response = self.client.patch(url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["birthday"], None)

    def test_user_update_email(self, integration, check_role, update_keycloak_user):
        url = reverse("user-detail", args=[str(self.user.id)])
        new_email = "<EMAIL>"
        data = {"email": new_email}

        response = self.client.patch(url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 200)
        self.assertNotEqual(response.data["email"], new_email)

    def test_update_related_user_leader(self, integration, check_role, update_keycloak_user):
        url = reverse("user-detail", args=[str(self.user.id)])
        leader = mommy.make(User, email="<EMAIL>")
        data = {"related_user_leader": leader.id}

        response = self.client.patch(url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 200)
        self.assertNotEqual(response.data["related_user_leader"]["id"], leader)

    def test_update_related_user_leader_using_email(self, integration, check_role, update_keycloak_user):
        url = reverse("user-detail", args=[str(self.user.id)])
        leader = mommy.make(User, email="<EMAIL>")
        data = {"related_user_leader_email": leader.email}

        response = self.client.patch(url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 200)
        self.assertNotEqual(response.data["related_user_leader"]["id"], leader)

    def test_user_update_with_invalid_birthday(self, integration, check_role, update_keycloak_user):
        url = reverse("user-detail", args=[str(self.user.id)])
        data = {"birthday": datetime.today()}

        response = self.client.patch(url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 400)
