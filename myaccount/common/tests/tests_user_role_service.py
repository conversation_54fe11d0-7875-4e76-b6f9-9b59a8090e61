import uuid
from django.test import TestCase
from model_mommy import mommy
import mock
from common.models import User, Workspace, UserRoleWorkspace, Role
from common.services.user_service import UserService
from custom.keeps_exception_handler import KeepsNotAllowedError


@mock.patch("config.celery.app.send_task", return_value={})
class UserRoleWorkspaceViewSetTestCase(TestCase):
    fixtures = ["application", "services", "language_preference", "roles"]

    def setUp(self):
        self.user_logged = mommy.make(User, id=uuid.uuid4())
        self.user_to_add_role = mommy.make(User, id=uuid.uuid4())
        self.workspace = mommy.make(Workspace, id=uuid.uuid4())
        self.user_service = UserService()

        self.keeps_admin_role = Role.objects.get(key="keeps_admin")
        self.workspace_admin_role = Role.objects.get(key="company_admin")
        self.user_role = Role.objects.get(key="account_admin")
        self.konquest_admin_role_id = "297a88de-c34b-4661-be8a-7090fa9a89e5"

        self.data = {"role": self.user_role, "workspace": self.workspace, "user": self.user_to_add_role}

    def test_check_permission_to_add_roles_when_keeps_admin(self, mock_notify):
        """
        Check if user logged can add roles to others

        Roles and Rules:
        If user logged has 'keeps_admin' role key, can add roles for all users and companies
        """

        mommy.make(UserRoleWorkspace, user=self.user_logged, workspace=self.workspace, role=self.keeps_admin_role)

        response = self.user_service.add_role_application(self.user_logged.id, self.data)
        self.assertEqual(response.role, self.user_role)
        self.assertEqual(response.workspace, self.workspace)
        self.assertEqual(response.user, self.user_to_add_role)

    def test_check_permission_to_add_roles_when_company_admin(self, mock_notify):
        """
        Check if user logged can add roles to others

        Roles and Rules:
        If user logged has 'company_admin' role key, can add roles for all user in the selected company
            - if user logged has 'account_admin' role key, can't add roles (include for self)
        """

        mommy.make(UserRoleWorkspace, user=self.user_logged, workspace=self.workspace, role=self.workspace_admin_role)

        response = self.user_service.add_role_application(self.user_logged.id, self.data)
        self.assertEqual(response.role, self.user_role)
        self.assertEqual(response.workspace, self.workspace)
        self.assertEqual(response.user, self.user_to_add_role)

    def test_check_permission_to_add_roles_when_account_admin(self, mock_notify):
        """
        Check if user logged can add roles to others

        Roles and Rules:
            - if user logged has 'account_admin' role key, can't add roles (include for self)
        """

        mommy.make(UserRoleWorkspace, user=self.user_logged, workspace=self.workspace, role=self.user_role)
        self.assertRaises(
            KeepsNotAllowedError, lambda: self.user_service.add_role_application(self.user_logged.id, self.data)
        )

    @mock.patch.object(UserService, "generate_random_password")
    @mock.patch.object(UserService, "update_user_password")
    @mock.patch.object(UserService, "_notify_user")
    @mock.patch.object(UserService, "check_allowed_add_roles")
    def test_should_generate_a_new_password_when_set_the_first_user_role(
        self, check_allowed_add_roles, notify_user, update_user_password, generate_random_password, mock_notify
    ):
        new_password = "142324"
        check_allowed_add_roles.return_value = True
        generate_random_password.return_value = new_password

        role = Role.objects.get(id=self.konquest_admin_role_id)

        user_role_workspace = self.user_service.add_role_application(
            self.user_logged.id, {"role": role, "workspace": self.workspace, "user": self.user_to_add_role}
        )

        update_user_password.assert_called_with(self.user_to_add_role.id, new_password, True)
        notify_user.assert_called_with(user_role_workspace, new_password)
