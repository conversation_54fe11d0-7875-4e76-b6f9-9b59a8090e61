import tempfile
import uuid
from unittest import mock

from django.test import TestCase, override_settings
from django.urls import reverse
from model_mommy import mommy
from PIL import Image
from rest_framework.test import APIClient
from common.models import Service, ServiceWorkspace, User, UserRoleWorkspace, Workspace, Role
from mock_targets import CHECK_ROLE

SEND_FILE_OBJ = "utils.aws.S3Client.send_file"

@override_settings(SUSPEND_SIGNALS=True)
@mock.patch("common.services.user_service.UserService.get_or_create_keycloak_user", return_value=(uuid.uuid4()))
@mock.patch("common.services.user_service.UserService.update_user_password")
@mock.patch("tasks.notifications.notify_user.delay", return_value={})
@mock.patch("tasks.platform_service.PlatformIntegrationService.publish", return_value={})
@mock.patch(CHECK_ROLE, return_value=True)
class WorkspaceViewSetTestCase(TestCase):
    fixtures = ["application", "services", "language_preference", "roles"]

    def setUp(self):
        self.client = APIClient()
        self.user_keeps = mommy.make(User, id=uuid.uuid4(), name="User Keeps")
        self.workspace_keeps = mommy.make(Workspace, id=uuid.uuid4(), name="Keeps")
        with mock.patch("tasks.notifications.notify_user.delay", return_value={}):
            mommy.make(
                UserRoleWorkspace,
                user=self.user_keeps,
                workspace=self.workspace_keeps,
                role_id="3b16b975-0297-4edf-950b-e3700b0d0d01",
            )

    def test_workspace_list(self, mock_return, mock_platform, mock_notify, *args):
        """ """
        self.client.force_authenticate(user={"sub": str(self.user_keeps.id)})

        url = reverse("workspaces-list")
        response = self.client.get(url, format="json").json()

        self.assertEqual(len(response), 1)

    def test_workspace_detail(self, mock_return, mock_platform, mock_notify, *args):
        """ """
        self.client.force_authenticate(user={"sub": str(self.user_keeps.id)})

        url = reverse("workspace-detail", args=[str(self.workspace_keeps.id)])
        response = self.client.get(url, format="json").json()

        self.assertEqual(response["name"], self.workspace_keeps.name)
        self.assertEqual(response["logout_url"], self.workspace_keeps.logout_url)
        self.assertEqual(response["custom_color"], self.workspace_keeps.custom_color)

    def test_workspace_create(self, mock_return, mock_platform, mock_notify, *args):
        """ """
        self.client.force_authenticate(user={"sub": str(self.user_keeps.id)})
        data = {"name": "New workspace"}

        url = reverse("workspaces-list")

        response = self.client.post(url, data=data, format="json").json()

        mock_platform.assert_not_called()
        self.assertEqual(response["name"], data["name"])
        self.assertIsNotNone(response["company"])

    def test_workspace_create_format_hash_id(self, mock_return, mock_platform, mock_notify, *args):
        """ """
        self.client.force_authenticate(user={"sub": str(self.user_keeps.id)})
        data = {"name": "New workspace", "hash_id": "MY New workspace.//"}

        url = reverse("workspaces-list")

        response = self.client.post(url, data=data, format="json").json()
        response2 = self.client.post(url, data=data, format="json").json()
        response3 = self.client.post(url, data=data, format="json").json()
        self.assertEqual(response["hash_id"], "my-new-workspace")
        self.assertEqual(response2["hash_id"], "my-new-workspace-lxp-1")
        self.assertEqual(response3["hash_id"], "my-new-workspace-lxp-2")

    def test_workspace_update(self, mock_return, mock_platform, mock_notify, *args):
        """ """
        self.role = mommy.make(Role, key="company_admin")
        mommy.make(UserRoleWorkspace, user=self.user_keeps, role=self.role, workspace=self.workspace_keeps)

        self.client.force_authenticate(user={"sub": str(self.user_keeps.id)})
        data = {"name": "Update workspace"}

        url = reverse("workspace-detail", args=[str(self.workspace_keeps.id)])
        response = self.client.patch(url, data=data, format="json").json()

        mock_platform.assert_not_called()
        self.assertEqual(response["name"], data["name"])


    @mock.patch('common.rest_clients.konquest_client.KonquestClient.enable_alura_integration', return_value=mock.MagicMock(status_code=200))
    def test_workspace_enable_alura_integration(self, mock_return, mock_platform, mock_notify, mock_enable_alura_integration, *args):
        """ """
        self.role = mommy.make(Role, key="company_admin")
        mommy.make(UserRoleWorkspace, user=self.user_keeps, role=self.role, workspace=self.workspace_keeps)

        self.client.force_authenticate(user={"sub": str(self.user_keeps.id)})
        data = {"alura_integration_active": True}

        url = reverse("workspace-detail", args=[str(self.workspace_keeps.id)])
        response = self.client.put(url, data=data, format="json").json()

        mock_platform.assert_not_called()
        self.assertTrue(User.objects.filter(email="<EMAIL>").exists())

    def test_workspace_update_with_service_enabled(self, mock_return, mock_platform, mock_notify, *args):
        """ """
        self.role = mommy.make(Role, key="company_admin")

        self.client.force_authenticate(user={"sub": str(self.user_keeps.id)})
        service = Service.objects.get(id="0d3752f0-15d7-402a-8628-04ed47bcbf41")
        workspace = mommy.make(Workspace, id=uuid.uuid4(), name="Keeps", icon_url="http://icon", logo_url="http://logo")
        mommy.make(UserRoleWorkspace, user=self.user_keeps, role=self.role, workspace=workspace)
        mommy.make(
            UserRoleWorkspace, user=self.user_keeps, workspace=workspace, role_id="3b16b975-0297-4edf-950b-e3700b0d0d01"
        )
        mommy.make(ServiceWorkspace, workspace=workspace, service=service)

        data = {
            "id": str(workspace.id),
            "name": workspace.name,
            "icon_url": workspace.icon_url,
            "logo_url": workspace.logo_url,
        }

        url = reverse("workspace-detail", args=[str(workspace.id)])
        response = self.client.patch(url, data=data, format="json").json()

        self.assertEqual(response["name"], data["name"])

    @mock.patch(SEND_FILE_OBJ)
    def test_create_workspace_logo(self, send_file, mock_return, mock_platform, mock_notify, *args):
        """ """
        self.client.force_authenticate(user={"sub": str(self.user_keeps.id)})
        image = self.create_image(".jpg")
        send_file.return_value = {"url": "https://url.com"}
        data = {"file": image}

        url = reverse("logo-create", args=[str(self.workspace_keeps.id)])
        response = self.client.post(url, data=data, format="multipart")

        self.assertEqual(response.status_code, 200)

    @staticmethod
    def create_image(file):
        image = Image.new("RGB", (100, 100))
        tmp_file = tempfile.NamedTemporaryFile(suffix=f"{file}")
        image.save(tmp_file)
        tmp_file.seek(0)

        return tmp_file

    def test_workspace_update_custom_color(self, mock_return, mock_platform, mock_notify, *args):
        """ """
        self.role = mommy.make(Role, key="company_admin")
        mommy.make(UserRoleWorkspace, user=self.user_keeps, role=self.role, workspace=self.workspace_keeps)

        self.client.force_authenticate(user={"sub": str(self.user_keeps.id)})
        data = {"custom_color": "#937db4"}

        url = reverse("workspace-detail", args=[str(self.workspace_keeps.id)])
        response = self.client.patch(url, data=data, format="json").json()

        mock_platform.assert_not_called()
        self.assertEqual(response["custom_color"], data["custom_color"])

    def test_workspace_update_enrollment_goal_duration_days(self, mock_return, mock_platform, mock_notify, *args):
        """ """
        self.role = mommy.make(Role, key="company_admin")
        mommy.make(UserRoleWorkspace, user=self.user_keeps, role=self.role, workspace=self.workspace_keeps)

        self.client.force_authenticate(user={"sub": str(self.user_keeps.id)})
        data = {"enrollment_goal_duration_days": 10}

        url = reverse("workspace-detail", args=[str(self.workspace_keeps.id)])
        response = self.client.patch(url, data=data, format="json").json()

        mock_platform.assert_not_called()
        self.assertEqual(response["enrollment_goal_duration_days"], data["enrollment_goal_duration_days"])
