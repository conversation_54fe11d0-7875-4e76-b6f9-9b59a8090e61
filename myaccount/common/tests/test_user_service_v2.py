import uuid

import mock
from django.test import TestCase
from model_mommy import mommy

from common.models import User, Workspace
from common.services.user_service import UserService
from common.models import Role


SET_USER_PASSWORD = "utils.keycloak.KeyCloakUserManager.set_user_password"
CREATE_KEYCLOAK_USER = "common.services.user_service.UserService._create_keycloak_user"
GET_KEYCLOAK_USER = "common.services.user_service.UserService._get_keycloak_user_id"
INTEGRATION_PUBLISH = "tasks.platform_service.PlatformIntegrationService.publish"
GET_OR_CREATE_KEYCLOAK_USER = "common.services.user_service.UserService.get_or_create_keycloak_user"
ADD_DEFAULT_ROLE = "common.services.user_service.UserService.add_default_role"


class TestUserServiceV2(TestCase):
    def setUp(self) -> None:
        self.workspace = mommy.make(Workspace)
        self.service = UserService()
        self.user_role = mommy.make(Role, key="account_admin")

    @mock.patch(SET_USER_PASSWORD)
    @mock.patch(GET_OR_CREATE_KEYCLOAK_USER)
    @mock.patch(ADD_DEFAULT_ROLE)
    def test_create_new_user(
        self, add_default_role: mock.MagicMock, get_or_create_kc_user: mock.MagicMock, set_user_password: mock.MagicMock
    ):
        user_data = {"email": "<EMAIL>", "name": "New User"}
        get_or_create_kc_user.return_value = uuid.uuid4()

        user, password = self.service.save_with_validated_data(user_data, workspace_id=self.workspace.id)

        self.assertIsNotNone(password)
        self.assertEqual(user.email, user_data["email"])
        self.assertEqual(user.name, user_data["name"])
        self.assertEqual(user.name, user_data["name"])
        get_or_create_kc_user.assert_called_with(user)
        add_default_role.assert_called_with(user_id=user.id, workspace_id=self.workspace.id, self_sign_up=False)
        set_user_password.assert_called_with(user_id=user.id, password=password, temporary=True)

    @mock.patch(SET_USER_PASSWORD)
    @mock.patch(GET_OR_CREATE_KEYCLOAK_USER)
    @mock.patch(ADD_DEFAULT_ROLE)
    def test_create_new_user_with_no_temporary_password(
        self, add_default_role: mock.MagicMock, get_or_create_kc_user: mock.MagicMock, set_user_password: mock.MagicMock
    ):
        user_data = {"email": "<EMAIL>", "name": "New User"}
        get_or_create_kc_user.return_value = uuid.uuid4()

        user, password = self.service.save_with_validated_data(
            user_data, workspace_id=self.workspace.id, temporary_password=False
        )

        self.assertIsNotNone(password)
        set_user_password.assert_called_with(user_id=user.id, password=password, temporary=False)

    @mock.patch(SET_USER_PASSWORD)
    @mock.patch(GET_OR_CREATE_KEYCLOAK_USER)
    @mock.patch(ADD_DEFAULT_ROLE)
    def test_create_new_user_with_password_no_temporary(
        self, add_default_role: mock.MagicMock, get_or_create_kc_user: mock.MagicMock, set_user_password: mock.MagicMock
    ):
        user_data = {"email": "<EMAIL>", "name": "New User"}
        expected_password = "123456"
        get_or_create_kc_user.return_value = uuid.uuid4()

        user, password = self.service.save_with_validated_data(
            user_data, workspace_id=self.workspace.id, temporary_password=False, password=expected_password
        )

        self.assertEqual(password, expected_password)
        set_user_password.assert_called_with(user_id=user.id, password=expected_password, temporary=False)

    def test_update_user(self):
        email = "<EMAIL>"
        older_user = mommy.make(User, email=email)
        user_data = {"email": email, "name": "New User"}
        updated_user, password = self.service.save_with_validated_data(
            user_data, workspace_id=self.workspace.id, temporary_password=False
        )

        self.assertIsNone(password)
        self.assertEqual(updated_user.id, older_user.id)
        self.assertEqual(updated_user.name, user_data["name"])

    @mock.patch.object(User, "update")
    def test_should_call_model_update_method(self, update: mock.MagicMock):
        email = "<EMAIL>"
        mommy.make(User, email=email)
        user_data = {"email": email, "name": "New User"}
        self.service.save_with_validated_data(user_data, workspace_id=self.workspace.id, temporary_password=False)

        update.assert_called_with(**user_data)
