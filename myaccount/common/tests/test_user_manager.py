from unittest import TestCase

import mock
import pytest

from custom.keeps_exception_handler import KeepsInvalidIdentityProvider
from utils.keycloak import KeyCloakUserManager

GET_USER_SOCIAL_LOGINS = "keycloak.KeycloakAdmin.get_user_social_logins"
ADD_USER_SOCIAL_LOGIN = "keycloak.KeycloakAdmin.add_user_social_login"
DELETE_USER_SOCIAL_LOGIN = "keycloak.KeycloakAdmin.delete_user_social_login"
IDENTITY_PROVIDER_EXISTS = "utils.keycloak.KeyCloakUserManager._identity_provider_exists"


class TestUserManger(TestCase):
    def setUp(self) -> None:
        self.service = KeyCloakUserManager()

    @mock.patch(GET_USER_SOCIAL_LOGINS)
    @mock.patch(ADD_USER_SOCIAL_LOGIN)
    @mock.patch(IDENTITY_PROVIDER_EXISTS, return_value=True)
    def test_save_new_identity_provider(
        self,
        identity_provider_exists: mock.MagicMock,
        add_user_social_login: mock.MagicMock,
        get_user_social_logins: mock.MagicMock,
    ):
        user_id = "1"
        identity_provider_alias = "alias-unique"
        provider_user_id = "user_id"
        provider_username = "user name"
        get_user_social_logins.return_value = []

        self.service.save_identity_provider(user_id, identity_provider_alias, provider_user_id, provider_username)

        get_user_social_logins.assert_called_with(user_id)
        add_user_social_login.assert_called_with(user_id, identity_provider_alias, provider_user_id, provider_username)

    @mock.patch(GET_USER_SOCIAL_LOGINS)
    @mock.patch(ADD_USER_SOCIAL_LOGIN)
    @mock.patch(DELETE_USER_SOCIAL_LOGIN)
    @mock.patch(IDENTITY_PROVIDER_EXISTS, return_value=True)
    def test_update_identity_provider(
        self,
        identity_provider_exists: mock.MagicMock,
        delete_user_social_login: mock.MagicMock,
        add_user_social_login: mock.MagicMock,
        get_user_social_logins: mock.MagicMock,
    ):
        user_id = "1"
        identity_provider_alias = "alias-unique"
        provider_user_id = "user_id"
        provider_username = "user name"
        get_user_social_logins.return_value = [
            {"identityProvider": identity_provider_alias, "userId": user_id, "userName": provider_username}
        ]

        self.service.save_identity_provider(user_id, identity_provider_alias, provider_user_id, provider_username)

        get_user_social_logins.assert_called_with(user_id)
        delete_user_social_login.assert_called_with(user_id, identity_provider_alias)
        add_user_social_login.assert_called_with(user_id, identity_provider_alias, provider_user_id, provider_username)

    @mock.patch(GET_USER_SOCIAL_LOGINS)
    @mock.patch(ADD_USER_SOCIAL_LOGIN)
    @mock.patch(DELETE_USER_SOCIAL_LOGIN)
    @mock.patch(IDENTITY_PROVIDER_EXISTS, return_value=True)
    def test_add_identity_provider(
        self,
        identity_provider_exists: mock.MagicMock,
        delete_user_social_login: mock.MagicMock,
        add_user_social_login: mock.MagicMock,
        get_user_social_logins: mock.MagicMock,
    ):
        user_id = "1"
        identity_provider_alias = "alias-unique"
        another_identity_provider_alias = "alias-unique-2"
        provider_user_id = "user_id"
        provider_username = "user name"
        get_user_social_logins.return_value = [
            {"identityProvider": identity_provider_alias, "userId": user_id, "userName": provider_username}
        ]

        self.service.save_identity_provider(
            user_id, another_identity_provider_alias, provider_user_id, provider_username
        )

        get_user_social_logins.assert_called_with(user_id)
        delete_user_social_login.assert_not_called()
        add_user_social_login.assert_called_with(
            user_id, another_identity_provider_alias, provider_user_id, provider_username
        )

    @mock.patch(IDENTITY_PROVIDER_EXISTS)
    def test_invalid_identity_provider_alias_error(self, identity_provider_exists: mock.MagicMock):
        user_id = "1"
        another_identity_provider_alias = "alias-unique-2"
        provider_user_id = "user_id"
        provider_username = "user name"
        identity_provider_exists.return_value = False

        with pytest.raises(KeepsInvalidIdentityProvider):
            self.service.save_identity_provider(
                user_id, another_identity_provider_alias, provider_user_id, provider_username
            )
