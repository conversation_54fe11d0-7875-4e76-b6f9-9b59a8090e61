import pytest
from django.test import TestCase
from mock import mock
from model_mommy import mommy

from common.models import Workspace, User
from common.services.workspace_service import WorkspaceService
from custom.keeps_exception_handler import KeepsNoPermissionToEditWorkspace


class HashService:
    @staticmethod
    def encrypt(password):
        return password


class TestWorkspaceService(TestCase):
    def setUp(self) -> None:
        self._workspace = mommy.make(Workspace)
        self._user = mommy.make(User)
        self._service = WorkspaceService(HashService())

    @mock.patch.object(HashService, "encrypt")
    @mock.patch.object(WorkspaceService, "_has_admin_permission")
    def test_should_update_workspace(self, has_admin_permission: mock.MagicMock, encrypt: mock.MagicMock):
        has_admin_permission.return_value = True
        encrypt.return_value = "passwordenc"
        password = "password"
        data = {"smtp_auth_pass": password}

        workspace = self._service.update(self._workspace.id, data, self._user.id)

        encrypt.assert_called_with(password)
        self.assertEqual(workspace.smtp_auth_pass, "passwordenc")

    @mock.patch.object(HashService, "encrypt")
    @mock.patch.object(WorkspaceService, "_has_admin_permission")
    def test_should_raise_no_permission_to_edit_workspace_exception(
        self, has_admin_permission: mock.MagicMock, encrypt: mock.MagicMock
    ):
        has_admin_permission.return_value = False
        encrypt.return_value = "passwordenc"
        password = "password"
        data = {"smtp_auth_pass": password}

        with pytest.raises(KeepsNoPermissionToEditWorkspace):
            self._service.update(self._workspace.id, data, self._user.id)

        encrypt.assert_not_called()

    @mock.patch.object(HashService, "encrypt")
    @mock.patch.object(WorkspaceService, "_has_admin_permission")
    def test_should_not_call_hash_password_already_hashed(
        self, has_admin_permission: mock.MagicMock, encrypt: mock.MagicMock
    ):
        has_admin_permission.return_value = False
        data = {"name": "Nome"}

        with pytest.raises(KeepsNoPermissionToEditWorkspace):
            self._service.update(self._workspace.id, data, self._user.id)

        encrypt.assert_not_called()
