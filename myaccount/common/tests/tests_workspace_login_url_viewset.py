import uuid
from unittest import mock

from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from common.models import User, UserRoleWorkspace, Workspace


class WorkspaceViewSetTestCase(TestCase):
    fixtures = ["application", "services", "language_preference", "roles"]

    def setUp(self):
        self.client = APIClient()
        self.user_keeps = mommy.make(User, id=uuid.uuid4(), name="User Keeps")

    def test_workspace_login_url_detail_with_url(self):
        workspace_keeps = mommy.make(
            Workspace, id=uuid.uuid4(), name="Keeps", hash_id="Keeps", custom_login_url="keeps.test.com"
        )
        with mock.patch("tasks.notifications.notify_user.delay", return_value={}):
            mommy.make(
                UserRoleWorkspace,
                user=self.user_keeps,
                workspace=workspace_keeps,
                role_id="3b16b975-0297-4edf-950b-e3700b0d0d01",
            )
        self.client.force_authenticate(user={"sub": str(self.user_keeps.id)})

        url = reverse("workspace-hash-login-url", args=[str(workspace_keeps.hash_id)])
        response = self.client.get(url, format="json").json()
        self.assertEqual(response["custom_login_url"], "keeps.test.com")

    def test_workspace_login_url_detail_not_url(self):
        workspace_keeps = mommy.make(Workspace, id=uuid.uuid4(), name="Keeps", hash_id="Keeps")
        with mock.patch("tasks.notifications.notify_user.delay", return_value={}):
            mommy.make(
                UserRoleWorkspace,
                user=self.user_keeps,
                workspace=workspace_keeps,
                role_id="3b16b975-0297-4edf-950b-e3700b0d0d01",
            )
        self.client.force_authenticate(user={"sub": str(self.user_keeps.id)})

        url = reverse("workspace-hash-login-url", args=[str(workspace_keeps.hash_id)])
        response = self.client.get(url, format="json").json()
        self.assertEqual(response["custom_login_url"], None)
