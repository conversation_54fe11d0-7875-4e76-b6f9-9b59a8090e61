from unittest import mock

from django.test import TestCase
from model_mommy import mommy

from common.models import User
from common.tasks.user_tasks import verifier_user_email

CHECK_EMAIL = "utils.DeBounceClient.check_email"


class UserTasksTest(TestCase):
    @mock.patch(CHECK_EMAIL)
    def test_verifier_user_email(self, check_email: mock.MagicMock):
        self.user = mommy.make(User, name="User Keeps", email="<EMAIL>")
        check_email.return_value = True
        verifier_user_email(self.user.id)
        self.user.refresh_from_db()
        self.assertTrue(self.user.email_verified)
