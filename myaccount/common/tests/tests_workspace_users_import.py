import os
from django.core.files.uploadedfile import SimpleUploadedFile
from django.test import TestCase
from django.urls import reverse
from mock import mock
from model_mommy import mommy
from rest_framework.test import APIClient

from common.models import LanguagePreference, User, Workspace
from mock_targets import CHECK_ROLE
import datetime


@mock.patch("common.services.import_user_service.ImportUserService.import_users")
@mock.patch(CHECK_ROLE, return_value=True)
class WorkspaceUserImportViewSetTestCase(TestCase):
    fixtures = ["application", "services", "roles", "language_preference"]

    def setUp(self) -> None:
        self.konquest_admin_role_id = "297a88de-c34b-4661-be8a-7090fa9a89e5"
        self.account_admin_role_id = "3b16b975-0297-4edf-950b-e3700b0d0d01"
        self.admin_user = mommy.make(User)
        self.workspace = mommy.make(Workspace)
        self.client = APIClient()
        self.headers = {"HTTP_X_CLIENT_ID": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.admin_user.id), "client_id": str(self.workspace.id)})
        self.url = reverse("user-import", args=[self.workspace.id])

    def test_import_new_users(self, check_role, import_users: mock.MagicMock) -> None:
        xlsx_path = os.path.join(os.path.dirname(__file__), "import_users_sample.xlsx")
        import_users.return_value = [], []
        with open(xlsx_path, "rb") as file:
            data = {
                "file": SimpleUploadedFile(file.name, file.read()),
                "roles": f"{self.konquest_admin_role_id},{self.account_admin_role_id}",
            }
            response = self.client.post(self.url, **self.headers, data=data, format="multipart")

        expected_parsed_users = [
            {
                "email": "<EMAIL>",
                "name": "User OK",
                "identity_provider": {
                    "alias": "adfs-idp-tivit",
                },
                "profile": {
                    "director": "last",
                    "manager": "last",
                    "area_of_activity": "last",
                },
                "email_verified": True,
                "secondary_email": "<EMAIL>",
                "phone": "*************",
                "gender": "MALE",
                "birthday": datetime.date(2023, 9, 19),
                "address": "Florianópolis",
                "country": "Brasil",
                "ein": "1234",
                "cpf": "***********",
                "admission_date": datetime.date(2023, 9, 19),
                "ethnicity": "branco",
                "marital_status": "Solteiro",
                "education": "Ensino Médio",
                "hierarchical_level": "Nenhum",
                "contract_type": "PJ",
                "language": LanguagePreference.objects.get(id="ea636f50-fdc4-49b0-b2de-9e5905de456b"),
                "related_user_leader": None,
            },
            {
                "name": "User No Email",
                "email_verified": False,
                "phone": "*************",
                "address": "Florianópolis",
                "language": LanguagePreference.objects.get(id="0319c298-c4cf-4c48-82b2-0fd4808a3d07"),
            },
            {
                "email": "<EMAIL>",
                "name": "user same email",
                "email_verified": False,
                "phone": "**********",
                "address": "Florianópolis",
                "language": LanguagePreference.objects.get(id="8b58683a-0e0b-4c5c-8fbc-5f07d043a365"),
            },
        ]

        expected_roles = [self.konquest_admin_role_id, self.account_admin_role_id]
        call_args, call_kwargs = import_users.call_args
        self.assertEqual(len(call_args[0]), len(expected_parsed_users))
        for i, user_dict in enumerate(call_args[0]):
            filtered_expected = {k: v for k, v in expected_parsed_users[i].items() if k in user_dict}
            for key, value in filtered_expected.items():
                self.assertEqual(user_dict[key], value)
        self.assertEqual(call_args[1], expected_roles)
        self.assertEqual(call_args[2], self.workspace.id)
        self.assertEqual(call_args[3], False)
        self.assertEqual(response.status_code, 200)
