import uuid
from unittest import mock

from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from common.models import Application, Role, Service, ServiceWorkspace, User, UserRoleWorkspace, Workspace
from mock_targets import CHECK_ROLE


@mock.patch(CHECK_ROLE, return_value=True)
@mock.patch("tasks.platform_service.PlatformIntegrationService.publish", return_value={})
@mock.patch("tasks.notifications.notify_user.delay", return_value={})
class ApplicationViewsetTestCase(TestCase):
    fixtures = ["application", "services", "language_preference", "roles"]

    def setUp(self):
        self.client = APIClient()

        self.user_keeps = mommy.make(User, id=uuid.uuid4(), name="User Keeps")
        self.user_bayer = mommy.make(User, id=uuid.uuid4(), name="User Bayer")

        self.workspace_keeps = mommy.make(Workspace, id=uuid.uuid4(), name="Keeps")
        self.workspace_bayer = mommy.make(Workspace, id=uuid.uuid4(), name="Bayer")

        self.app_konquest = Application.objects.get(id="0abf08ea-d252-4d7c-ab45-ab3f9135c288")

        self.myaccount_user_role = Role.objects.get(id="3b16b975-0297-4edf-950b-e3700b0d0d01")
        self.konquest_user_role = Role.objects.get(id="a6d23aea-807e-4374-964e-c725b817742d")

        self.service_konquest = Service.objects.get(id="0d3752f0-15d7-402a-8628-04ed47bcbf41")

    def test_application_workspace_users_case_1(self, mock_check_role, mock_platform, mock_notify):
        """ """
        mommy.make(ServiceWorkspace, workspace=self.workspace_keeps, service=self.service_konquest)
        mommy.make(ServiceWorkspace, workspace=self.workspace_bayer, service=self.service_konquest)

        mommy.make(
            UserRoleWorkspace, user=self.user_keeps, workspace=self.workspace_keeps, role=self.konquest_user_role
        )
        mommy.make(
            UserRoleWorkspace, user=self.user_keeps, workspace=self.workspace_bayer, role=self.konquest_user_role
        )

        self.client.force_authenticate(user={"sub": str(self.user_keeps.id)})

        url = reverse("application-workspace-detail", args=[str(self.app_konquest.id)])
        response = self.client.get(url, format="json").json()

        self.assertEqual(len(response), 2)

    def test_application_workspace_users_case_2(self, mock_check_role, mock_platform, mock_notify):
        """ """
        mommy.make(ServiceWorkspace, workspace=self.workspace_bayer, service=self.service_konquest)

        mommy.make(
            UserRoleWorkspace, user=self.user_keeps, workspace=self.workspace_bayer, role=self.konquest_user_role
        )

        self.client.force_authenticate(user={"sub": str(self.user_keeps.id)})

        url = reverse("application-workspace-detail", args=[str(self.app_konquest.id)])
        response = self.client.get(url, format="json").json()

        self.assertEqual(len(response), 1)
        self.assertEqual(response[0]["name"], "Bayer")

    def test_application_workspace_users_case_4(self, mock_check_role, mock_platform, mock_notify):
        """ """
        mommy.make(ServiceWorkspace, workspace=self.workspace_keeps, service=self.service_konquest)
        mommy.make(
            UserRoleWorkspace, user=self.user_keeps, workspace=self.workspace_keeps, role=self.konquest_user_role
        )

        self.client.force_authenticate(user={"sub": str(self.user_keeps.id)})

        url = reverse("application-workspace-detail", args=[str(self.app_konquest.id)])
        response = self.client.get(url, format="json").json()

        self.assertEqual(len(response), 1)

    def test_application_roles_filter_by_workspace(self, mock_check_role, mock_platform, mock_notify):
        """ """
        self.client.force_authenticate(user={"sub": str(self.user_keeps.id), "client_id": str(self.workspace_keeps.id)})
        url = reverse("application-roles-details")

        mommy.make(ServiceWorkspace, workspace=self.workspace_keeps, service=self.service_konquest)
        response = self.client.get(url, format="json").json()
        self.assertEqual(len(response), 2)
