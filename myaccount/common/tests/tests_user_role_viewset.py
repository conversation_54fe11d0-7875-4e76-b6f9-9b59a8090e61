from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from model_mommy import mommy
from unittest import mock
import uuid
from common.models import User, Workspace

KEYCLOAK_UPDATE_USER = "utils.keycloak.KeyCloakUserManager.update_user"


class UserRolesListTestCase(TestCase):
    """
    Test list users by role
    """

    def setUp(self):
        self.client = APIClient()
        with mock.patch(KEYCLOAK_UPDATE_USER, return_value={}):
            self.user = mommy.make(User, id=uuid.uuid4())
            self.workspace = mommy.make(Workspace, id=uuid.uuid4())
        self.url = reverse("user-role-list")
        self.headers = {"HTTP_X_CLIENT_ID": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})

    def test_list_users_admin_role(self):
        request = self.client.get(self.url, **self.headers, format="json")
        response = request.json()
        self.assertEqual(request.status_code, 200)
        self.assertIn("results", response)
