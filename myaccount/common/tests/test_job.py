import uuid
from unittest import mock

from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from common.models import Role, User, UserRoleWorkspace, Workspace, Job, UserProfileWorkspace
from mock_targets import CHECK_ROLE


@mock.patch(CHECK_ROLE, return_value=True)
class JobViewSetTestCase(TestCase):
    fixtures = ["application", "services", "language_preference", "roles"]

    def setUp(self):
        self.client = APIClient()
        self.workspace = mommy.make(Workspace, id=uuid.uuid4())
        self.job = mommy.make(Job, workspace=self.workspace, name="JOB1")
        self.user = mommy.make(User, id=uuid.uuid4(), email="<EMAIL>")
        self.user.job = self.job.name

        profile = mommy.make(UserProfileWorkspace, user=self.user, workspace=self.workspace)
        profile.job = self.job.name

        self.role1 = mommy.make(Role)
        self.user_role_workspace1 = mommy.make(
            UserRoleWorkspace, user=self.user, workspace=self.workspace, role=self.role1
        )

        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})
        self.url_list = reverse("job-list")
        self.url_detail = reverse("job-detail", args=[str(self.job.id)])

    def test_list_jobs(self, *args):
        response = self.client.get(self.url_list, **self.headers)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]["name"], self.job.name)

    def test_retrieve_job(self, *args):
        response = self.client.get(self.url_detail, **self.headers)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["name"], self.job.name)

    def test_create_job(self, *args):
        data = {"name": "JOB2"}
        response = self.client.post(self.url_list, data=data, **self.headers)
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data["name"], data["name"])

    def test_update_job(self, *args):
        data = {"name": "JOB2"}
        response = self.client.put(self.url_detail, data=data, **self.headers)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["name"], data["name"])
