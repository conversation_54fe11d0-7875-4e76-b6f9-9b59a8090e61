import os
import shutil
import uuid

import mock
import pytest
from django.test import TestCase, override_settings
from model_mommy import mommy
from rest_framework.exceptions import ValidationError

from common.models import Role, User, UserRoleWorkspace, Workspace
from common.services.import_user_service import ImportUserService
from custom.keeps_exception_handler import KeepsBadRequestError

kc_user_mock = "0176b6c7-56be-45b0-a801-d357f754342e"
KEYCLOAK_SAVE_IDENTITY_PROVIDER = "utils.keycloak.KeyCloakUserManager.save_identity_provider"


@override_settings(SUSPEND_SIGNALS=True)
@mock.patch("common.services.user_service.UserService.get_or_create_keycloak_user", return_value=(uuid.uuid4()))
@mock.patch("common.services.user_service.UserService.update_user_password")
class ImportUserServiceTestCase(TestCase):
    fixtures = ["language_preference"]

    def setUp(self):
        self.workspace = mommy.make(Workspace, id=uuid.uuid4())
        self.role_account_admin = mommy.make(Role, id=uuid.uuid4(), key="account_admin")
        self.role = mommy.make(Role, id=uuid.uuid4())
        self.user = mommy.make(User, email="<EMAIL>")
        self.service = ImportUserService()

    def test_parser_user_xls(self, *args):
        """ """
        self.template_sheet = os.path.dirname(os.path.dirname(__file__)) + "/tests/import_users_sample.xlsx"
        self.filename = os.path.dirname(os.path.dirname(__file__)) + "/tests/temp.xlsx"
        shutil.copyfile(self.template_sheet, self.filename)

        response = self.service.parser_users(self.filename, self.workspace.id)
        self.assertEqual(len(response), 3)
        self.assertEqual(response[0]["name"], "User OK")
        self.assertEqual(response[0]["email"], "<EMAIL>")
        self.assertEqual(response[0]["phone"], "*************")
        self.assertEqual(str(response[0]["language"].id), "ea636f50-fdc4-49b0-b2de-9e5905de456b")
        self.assertEqual(response[0]["address"], "Florianópolis")
        self.assertEqual(response[0]["secondary_email"], "<EMAIL>")
        self.assertEqual(response[0]["country"], "Brasil")
        self.assertEqual(response[0]["ein"], "1234")
        self.assertEqual(response[0]["cpf"], "64284577093")
        self.assertEqual(response[0]["ein"], "1234")
        self.assertEqual(response[0]["related_user_leader"].email, "<EMAIL>")
        self.assertEqual(response[0]["identity_provider"]["alias"], "adfs-idp-tivit")
        self.assertEqual(response[0]['admission_date'].strftime('%Y-%m-%d'), '2023-09-19')
        self.assertEqual(response[0]["gender"], "MALE")
        self.assertEqual(response[0]["marital_status"], "Solteiro")
        self.assertEqual(response[0]["education"], "Ensino Médio")
        self.assertEqual(response[0]["hierarchical_level"], "Nenhum")
        self.assertEqual(response[0]["contract_type"], "PJ")
        self.assertEqual(response[0]["birthday"].strftime('%Y-%m-%d'), '2023-09-19')

    def test_parser_user_xls_without_number(self, *args):
        """ """
        self.template_sheet = (
            os.path.dirname(os.path.dirname(__file__)) + "/tests/import_users_sample_without_phone.xlsx"
        )
        self.filename = os.path.dirname(os.path.dirname(__file__)) + "/tests/temp.xlsx"
        shutil.copyfile(self.template_sheet, self.filename)

        response = self.service.parser_users(self.filename, self.workspace.id)
        self.assertEqual(len(response), 1)
        self.assertEqual(response[0]["name"], "User OK")
        self.assertEqual(response[0]["email"], "<EMAIL>")

    def test_invalid_user_email(self, *args):
        """ """
        self.template_sheet = os.path.dirname(os.path.dirname(__file__)) + "/tests/import_users_with_invalid_email.xlsx"
        self.filename = os.path.dirname(os.path.dirname(__file__)) + "/tests/temp.xlsx"
        shutil.copyfile(self.template_sheet, self.filename)

        with pytest.raises(ValidationError) as exc_info:
            self.service.parser_users(self.filename, self.workspace.id)

        assert exc_info.value.args[0] == {"email": ["Enter a valid email address."]}

    def test_parser_user_xls_error(self, *args):
        """ """
        self.template_sheet = os.path.dirname(os.path.dirname(__file__)) + "/tests/import_users_sample_error.xlsx"
        self.filename = os.path.dirname(os.path.dirname(__file__)) + "/tests/temp.xlsx"
        shutil.copyfile(self.template_sheet, self.filename)

        with self.assertRaises(KeepsBadRequestError):
            self.service.parser_users(self.filename, self.workspace.id)

    @mock.patch("common.services.user_service.UserService.save_with_validated_data")
    def test_import_existing_user_from_an_xlsx(self, save_with_validated_data: mock.MagicMock, *args):
        """ """
        save_with_validated_data.return_value = self.user, "password"
        mommy.make(UserRoleWorkspace, user=self.user, workspace=self.workspace, role=self.role)

        self.template_sheet = os.path.dirname(os.path.dirname(__file__)) + "/tests/import_users_sample.xlsx"
        self.filename = os.path.dirname(os.path.dirname(__file__)) + "/tests/temp.xlsx"
        shutil.copyfile(self.template_sheet, self.filename)

        users = [{}]

        imported, errors = self.service.import_users(users, [self.role.id], self.workspace.id, False)

        self.assertEqual(imported[0], self.user)
        save_with_validated_data.assert_called()

    def test_parser_user_xls_with_pt_pt_language(self, *args):
        """ """
        self.template_sheet = (
            os.path.dirname(os.path.dirname(__file__)) + "/tests/import_users_with_pt_pt_language.xlsx"
        )
        self.filename = os.path.dirname(os.path.dirname(__file__)) + "/tests/temp.xlsx"
        shutil.copyfile(self.template_sheet, self.filename)
        response = self.service.parser_users(self.filename, self.workspace.id)
        self.assertEqual(len(response), 1)
        self.assertEqual(response[0]["name"], "Teste")
        self.assertEqual(response[0]["email"], "<EMAIL>")
        self.assertEqual(str(response[0]["language"]), "pt-pt")
