import uuid
from unittest import mock

from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from common.models import Service, ServiceWorkspace, User, UserRoleWorkspace, Workspace
from mock_targets import CHECK_ROLE


@mock.patch(CHECK_ROLE, return_value=True)
@mock.patch("tasks.platform_service.PlatformIntegrationService.publish", return_value={})
@mock.patch("tasks.notifications.notify_user", return_value={})
class WorkspaceServiceViewSetTestCase(TestCase):
    fixtures = ["application", "services", "language_preference", "roles"]

    def setUp(self):
        self.client = APIClient()
        self.user_keeps = mommy.make(User, id=uuid.uuid4(), name="User Keeps")
        self.workspace_keeps = mommy.make(Workspace, id=uuid.uuid4(), name="Keeps")

        with mock.patch("tasks.notifications.notify_user.delay", return_value={}):
            mommy.make(
                UserRoleWorkspace,
                user=self.user_keeps,
                workspace=self.workspace_keeps,
                role_id="3b16b975-0297-4edf-950b-e3700b0d0d01",
            )

    def test_workspace_list_services(self, check_role, mock_platform, mock_notify):
        """ """
        self.client.force_authenticate(user={"sub": str(self.user_keeps.id)})

        url = reverse("services-workspaces-list", args=[str(self.workspace_keeps.id)])
        response = self.client.get(url, format="json").json()

        self.assertEqual(len(response), 0)

    def test_workspace_add_konquest_service(self, check_role, mock_platform, mock_notify):
        """ """
        self.client.force_authenticate(user={"sub": str(self.user_keeps.id)})
        service = Service.objects.get(id="0d3752f0-15d7-402a-8628-04ed47bcbf41")
        url = reverse("service-workspace-detail", args=[str(self.workspace_keeps.id), str(service.id)])

        response = self.client.post(url, format="json").json()
        self.assertEqual(response["service"]["id"], str(service.id))
        self.assertEqual(response["workspace"]["id"], str(self.workspace_keeps.id))

    def test_workspace_add_smartzap_service(self, check_role, mock_platform, mock_notify):
        """ """
        self.client.force_authenticate(user={"sub": str(self.user_keeps.id)})
        service = Service.objects.get(id="f9743ebc-c159-4dec-9600-cf1f9f0537b3")
        url = reverse("service-workspace-detail", args=[str(self.workspace_keeps.id), str(service.id)])

        response = self.client.post(url, format="json").json()
        self.assertEqual(response["service"]["id"], str(service.id))
        self.assertEqual(response["workspace"]["id"], str(self.workspace_keeps.id))

    def test_workspace_remove_konquest_service(self, check_role, mock_platform, mock_notify):
        """ """
        self.client.force_authenticate(user={"sub": str(self.user_keeps.id)})
        service = Service.objects.get(id="0d3752f0-15d7-402a-8628-04ed47bcbf41")
        mommy.make(ServiceWorkspace, service=service, workspace=self.workspace_keeps)

        url = reverse("service-workspace-detail", args=[str(self.workspace_keeps.id), str(service.id)])

        response = self.client.delete(url)
        self.assertEqual(response.status_code, 204)

    def test_workspace_remove_smartzap_service(self, check_role, mock_platform, mock_notify):
        """ """
        self.client.force_authenticate(user={"sub": str(self.user_keeps.id)})
        service = Service.objects.get(id="f9743ebc-c159-4dec-9600-cf1f9f0537b3")
        mommy.make(ServiceWorkspace, service=service, workspace=self.workspace_keeps)
        url = reverse("service-workspace-detail", args=[str(self.workspace_keeps.id), str(service.id)])

        response = self.client.delete(url)
        self.assertEqual(response.status_code, 204)
