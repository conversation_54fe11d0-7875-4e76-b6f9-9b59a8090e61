import uuid
from unittest import mock

from django.test import TestCase, override_settings
from model_mommy import mommy

from common.models import BillingCompanyPlan, Company, ServiceWorkspace, Workspace


@mock.patch("tasks.notifications.notify_user.delay", return_value={})
@mock.patch("tasks.platform_service.PlatformIntegrationService.publish", return_value={})
class WorkspaceBillingServiceSignalTestCase(TestCase):
    fixtures = ["application", "services"]

    def setUp(self):
        self.konquest_id = "0abf08ea-d252-4d7c-ab45-ab3f9135c288"
        self.mission_service_id = "0d3752f0-15d7-402a-8628-04ed47bcbf41"
        self.pulse_service_id = "f19a1f71-82fb-46df-ab88-bdd3700da124"
        self.company = mommy.make(Company)
        self.keeps_workspace = mommy.make(Workspace, id=uuid.uuid4, name="Keeps", company=self.company)
        self.keeps_workspace_with_service = mommy.make(Workspace, id=uuid.uuid4, name="Keeps", company=self.company)
        self.service_keeps = mommy.make(
            ServiceWorkspace, service_id=self.pulse_service_id, workspace_id=self.keeps_workspace_with_service.id
        )
        self.service_keeps_2 = mommy.make(
            ServiceWorkspace, service_id=self.mission_service_id, workspace_id=self.keeps_workspace_with_service.id
        )

    @override_settings(SUSPEND_SIGNALS=False)
    def test_create_service_billing_workspace(
        self,
        mock_return,
        mock_platform,
    ):
        mommy.make(ServiceWorkspace, workspace_id=self.keeps_workspace.id, service_id=self.pulse_service_id)
        billing_workspace = BillingCompanyPlan.objects.filter(
            application_id=self.konquest_id, company_id=self.keeps_workspace.company_id
        ).exists()

        self.assertTrue(billing_workspace)

    def test_delete_all_services_billing_workspace(self, mock_return, mock_platform):
        self.service_keeps.delete()
        self.service_keeps_2.delete()
        billing_workspace_konquest = BillingCompanyPlan.objects.filter(
            application_id=self.konquest_id, company_id=self.keeps_workspace_with_service.company_id
        ).exists()

        self.assertFalse(billing_workspace_konquest)
