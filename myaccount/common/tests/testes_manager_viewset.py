import uuid
from unittest import mock

from django.test import TestCase
from django.urls import reverse
from mock_targets import CHECK_ROLE
from model_mommy import mommy
from rest_framework.test import APIClient

from common.models import UserProfileWorkspace, Workspace

@mock.patch("tasks.platform_service.PlatformIntegrationService.publish.delay", return_value={})
@mock.patch(CHECK_ROLE, return_value=True)
@mock.patch("common.services.user_service.UserService._update_keycloak_user")
class ManagerViewSetTestCase(TestCase):
    fixtures = ["application", "services", "language_preference", "roles"]

    def setUp(self):
        self.client = APIClient()
        self.workspace = mommy.make(Workspace, id=uuid.uuid4())
        self.workspace2 = mommy.make(Workspace, id=uuid.uuid4())
        mommy.make(UserProfileWorkspace, manager="test1", workspace=self.workspace)
        mommy.make(UserProfileWorkspace, manager="test2", workspace=self.workspace)
        mommy.make(UserProfileWorkspace, manager="test3", workspace=self.workspace2)
        self.url = reverse("managers-values")

        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.workspace.id), "client_id": str(self.workspace.id)})
    
    def test_list_manager(self, integration, check_role, update_keycloak_user):
        response = self.client.get(self.url, **self.headers, format="json")
        data = response.json()['results']

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(data), 2)
        self.assertEqual(data[0], "test1")
        self.assertEqual(data[1], "test2")