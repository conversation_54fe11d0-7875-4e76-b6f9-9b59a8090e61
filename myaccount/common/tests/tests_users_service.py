import uuid
from unittest import mock

from django.test import TestCase
from model_mommy import mommy

from common.models import Role, User, UserRoleWorkspace, Workspace
from common.services.user_service import UserService
from custom.keeps_exception_handler import KeepsNotAllowedError

kc_user_mock = "0176b6c7-56be-45b0-a801-d357f754342e"
kc_user_create_mock = "0176b6c7-56be-45b0-a801-d357f754342a"

# kc_user_mock = {
#     'id': '0176b6c7-56be-45b0-a801-d357f754342e',
#     'createdTimestamp': 1590608529858,
#     'username': '<EMAIL>',
#     'enabled': True,
#     'totp': False,
#     'emailVerified': True,
#     'email': '<EMAIL>',
#     'disableableCredentialTypes': [
#
#     ],
#     'requiredActions': [
#
#     ],
#     'notBefore': 0,
#     'access': {
#         'manageGroupMembership': True,
#         'view': True,
#         'mapRoles': True,
#         'impersonate': False,
#         'manage': True
#     }
# }
#
# kc_user_create_mock = {
#     'id': '0176b6c7-56be-45b0-a801-d357f754342a',
#     'createdTimestamp': 1590608529858,
#     'username': '<EMAIL>',
#     'enabled': True,
#     'totp': False,
#     'emailVerified': True,
#     'email': '<EMAIL>',
#     'disableableCredentialTypes': [
#
#     ],
#     'requiredActions': [
#
#     ],
#     'notBefore': 0,
#     'access': {
#         'manageGroupMembership': True,
#         'view': True,
#         'mapRoles': True,
#         'impersonate': False,
#         'manage': True
#     }
# }


@mock.patch("tasks.notifications.notify_user.delay", return_value={})
@mock.patch("common.services.user_service.UserService._create_keycloak_user", return_value=None)
@mock.patch("common.services.user_service.UserService._get_keycloak_user_id", return_value=kc_user_mock)
@mock.patch("tasks.platform_service.PlatformIntegrationService.publish", return_value={})
@mock.patch("authentication.utils.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
class UserServiceTestCase(TestCase):
    fixtures = ["application", "services", "language_preference", "roles"]

    def setUp(self):
        self.user = mommy.make(User, id=uuid.uuid4(), email="<EMAIL>")
        self.workspace = mommy.make(Workspace, id=uuid.uuid4())
        self.myaccount_user_role = Role.objects.get(id="3b16b975-0297-4edf-950b-e3700b0d0d01")
        self.user_service = UserService()

        with mock.patch("tasks.notifications.notify_user.delay", return_value={}):
            self.user_workspace_role = mommy.make(
                UserRoleWorkspace, user=self.user, workspace=self.workspace, role=self.myaccount_user_role
            )

    def test_workspace_error_to_create_user(
        self, mock_return, mock_platform, mock_kc_get_user, mock_kc_create_user, mock_notify
    ):
        """
        Permission error.
        """
        data = {"users": [{}]}
        with self.assertRaises(KeepsNotAllowedError):
            self.user_service.save(data["users"][0], None, None, None)

    def test_workspace_create_user_total_new(
        self, mock_return, mock_platform, mock_kc_get_user, mock_kc_create_user, mock_notify
    ):
        """
        Create a new user that don't have any data (Keycloak or MyAccount)
        """

        # simulate KC call, when first return None (don't have KC user) then return a
        # kc_user_create_mock (after call kc_create_user)
        mock_kc_get_user.side_effect = [None, kc_user_create_mock]

        data = {
            "users": [
                {
                    "name": "Test",
                    "nickname": "AutoTest",
                    "email": "<EMAIL>",
                    "secondary_email": "",
                    "phone": "*************",
                    "gender": "MALE",
                    "birthday": None,
                    "address": "Acate",
                }
            ],
            "permissions": [str(self.myaccount_user_role.id)],
        }
        result = self.user_service.save(
            data["users"][0], authenticate_user_id=self.user.id, workspace_id=self.workspace.id
        )
        self.assertNotEqual(result.id, "0176b6c7-56be-45b0-a801-d357f754342e")
        self.assertEqual(result.name, data["users"][0]["name"])
        self.assertEqual(result.name, data["users"][0]["name"])
        self.assertEqual(result.nickname, data["users"][0]["nickname"])
        self.assertEqual(result.email, data["users"][0]["email"])
        self.assertEqual(result.phone, data["users"][0]["phone"])
        self.assertEqual(result.gender, data["users"][0]["gender"])
        self.assertEqual(result.address, data["users"][0]["address"])

    def test_workspace_create_user_new_for_workspace(
        self, mock_return, mock_platform, mock_kc_get_user, mock_kc_create_user, mock_notify
    ):
        """
        Create a new user that already have data into keycloak
        """
        data = {
            "users": [
                {
                    "name": "Test",
                    "nickname": "AutoTest",
                    "email": "<EMAIL>",
                    "secondary_email": "",
                    "phone": "*************",
                    "gender": "MALE",
                    "birthday": None,
                    "address": "Acate",
                }
            ],
            "permissions": [str(self.myaccount_user_role.id)],
        }
        result = self.user_service.save(
            data["users"][0], authenticate_user_id=self.user.id, workspace_id=self.workspace.id
        )
        self.assertEqual(result.id, "0176b6c7-56be-45b0-a801-d357f754342e")
        self.assertEqual(result.nickname, data["users"][0]["nickname"])
        self.assertEqual(result.email, data["users"][0]["email"])
        self.assertEqual(result.phone, data["users"][0]["phone"])
        self.assertEqual(result.gender, data["users"][0]["gender"])
        self.assertEqual(result.address, data["users"][0]["address"])

    def test_workspace_create_new_user_already_exist(
        self, mock_return, mock_platform, mock_kc_get_user, mock_kc_create_user, mock_notify
    ):
        """
        Create a new user that already have data in keycloak and my account
        """
        user = mommy.make(User, id="0176b6c7-56be-45b0-a801-d357f754342e", email="<EMAIL>")

        data = {
            "users": [
                {
                    "name": "Test",
                    "nickname": "AutoTest",
                    "email": "<EMAIL>",
                    "secondary_email": "",
                    "phone": "*************",
                    "gender": "MALE",
                    "birthday": None,
                    "status": False,
                    "address": "Acate",
                }
            ],
            "permissions": [str(self.myaccount_user_role.id)],
        }
        result = self.user_service.save(
            data["users"][0], authenticate_user_id=self.user.id, workspace_id=self.workspace.id
        )
        self.assertEqual(str(result.id), str(user.id))
        self.assertEqual(result.nickname, data["users"][0]["nickname"])
        self.assertEqual(result.email, data["users"][0]["email"])
        self.assertEqual(result.phone, data["users"][0]["phone"])
        self.assertEqual(result.gender, data["users"][0]["gender"])
        self.assertEqual(result.address, data["users"][0]["address"])
        self.assertEqual(result.status, data["users"][0]["status"])

    def test_workspace_create_new_user_already_exist_with_status_false(
        self, mock_return, mock_platform, mock_kc_get_user, mock_kc_create_user, mock_notify
    ):
        """
        Create a new user that already have data in keycloak and my account
        """
        user = mommy.make(User, id="0176b6c7-56be-45b0-a801-d357f754342e", email="<EMAIL>")
        payload_data = {"name": "Test", "email": "<EMAIL>", "status": False}
        updated_user, password = self.user_service.save_with_validated_data(
            payload_data, workspace_id=self.workspace.id
        )
        self.assertEqual(str(updated_user.id), user.id)
        self.assertEqual(updated_user.name, payload_data["name"])
        self.assertEqual(updated_user.email, payload_data["email"])
        self.assertEqual(updated_user.status, payload_data["status"])

    def test_workspace_create_user_with_adfs(
        self, mock_return, mock_platform, mock_kc_get_user, mock_kc_create_user, mock_notify
    ):
        """
        Create a new user that don't have any data (Keycloak or MyAccount)
        """

        # simulate KC call, when first return None (don't have KC user) then return a
        # kc_user_create_mock (after call kc_create_user)
        mock_kc_get_user.side_effect = [None, kc_user_create_mock]

        data = {
            "users": [{"name": "Test", "email": "<EMAIL>"}],
            "permissions": [str(self.myaccount_user_role.id)],
        }

        adfs_config = {"alias": "adfs-idp-test", "provider_user_id": "email@email", "provider_username": "email@email"}

        with mock.patch(
            "utils.keycloak.user_manager.KeyCloakUserManager.save_identity_provider", return_value={}
        ) as mk:
            result = self.user_service.save(
                data["users"][0],
                authenticate_user_id=self.user.id,
                workspace_id=self.workspace.id,
                identity_provider=adfs_config,
            )

            self.assertNotEqual(result.id, "0176b6c7-56be-45b0-a801-d357f754342e")
            self.assertEqual(result.name, data["users"][0]["name"])
            self.assertEqual(result.email, data["users"][0]["email"])
            mk.assert_called()

    def test_workspace_create_user_with_leader(
        self, mock_return, mock_platform, mock_kc_get_user, mock_kc_create_user, mock_notify
    ):
        """
        Create a new user that don't have any data (Keycloak or MyAccount)
        """

        # simulate KC call, when first return None (don't have KC user) then return a
        # kc_user_create_mock (after call kc_create_user)
        mock_kc_get_user.side_effect = [None, kc_user_create_mock]

        data = {
            "users": [{"name": "Test", "email": "<EMAIL>", "related_user_leader": self.user.email}],
            "permissions": [str(self.myaccount_user_role.id)],
        }
        result = self.user_service.save(
            data["users"][0], authenticate_user_id=self.user.id, workspace_id=self.workspace.id
        )
        self.assertNotEqual(result.id, "0176b6c7-56be-45b0-a801-d357f754342e")
        self.assertEqual(result.name, data["users"][0]["name"])
        self.assertEqual(result.email, data["users"][0]["email"])
        self.assertEqual(result.related_user_leader.email, self.user.email)
