from django.test import TestCase, override_settings
from model_mommy import mommy
import mock
from common.models import UserRoleWorkspace, User, Role, Workspace
from common.filters.user_role_filters import UserRoleFilter

KEYCLOAK_UPDATE_USER = "utils.keycloak.KeyCloakUserManager.update_user"
VERIFIER_USER_EMAIL = "common.tasks.user_tasks.verifier_user_email.delay"


@override_settings(SUSPEND_SIGNALS=False)
@mock.patch(KEYCLOAK_UPDATE_USER)
class UserRoleFilterTestCase(TestCase):
    @override_settings(SUSPEND_SIGNALS=True)
    def setUp(self):
        self.user1 = mommy.make(User)
        self.user2 = mommy.make(User)

        self.role1 = mommy.make(Role)
        self.role2 = mommy.make(Role)

        self.workspace1 = mommy.make(Workspace)
        self.workspace2 = mommy.make(Workspace)

        self.user_role_workspace1 = mommy.make(
            UserRoleWorkspace, user=self.user1, workspace=self.workspace1, role=self.role1
        )
        self.user_role_workspace2 = mommy.make(
            UserRoleWorkspace, user=self.user2, workspace=self.workspace2, role=self.role2
        )

    def test_filter_by_role(self, keycloak_update_user: mock.MagicMock):
        # Get initial queryset
        qs = UserRoleWorkspace.objects.all()

        # Apply filter
        f = UserRoleFilter({"role_id": f"{self.role1.id},{self.role2.id}"}, queryset=qs)
        results = f.qs
        f2 = UserRoleFilter({"role__id": self.role1.id}, queryset=qs)
        results2 = f2.qs

        # Check if filter was applied correctly
        self.assertIn(self.user_role_workspace1, results)
        self.assertIn(self.user_role_workspace2, results)
        self.assertEqual(len(results), 2)
        self.assertEqual(len(results2), 1)
