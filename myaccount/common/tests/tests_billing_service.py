import uuid
from unittest import mock

from django.test import TestCase
from model_mommy import mommy

from common.models import Company, Workspace
from common.services.billing_konquest_service import BillingKonquestService
from common.services.billing_service import BillingGeneral
from common.services.billing_smartzap_service import BillingSmartzapService

GET_USED = "common.selectors.billing_selector.Selector.get_used_plan"
GET_WORKSPACES = "common.selectors.billing_selector.Selector.get_companies_with_application"
GET_KONQUEST_ACTIVE_USERS = "common.selectors.billing_selector.Selector.konquest_count_active_user_by_company"
GET_SMARTZAP_MESSAGES_COUNT = "common.selectors.billing_selector.Selector.smartzap_count_messages"
GET_WORKSPACES_KONQUEST = "common.services.billing_konquest_service.generate"
GET_FREE_USE = "common.services.billing_service.BillingGeneral.get_current_plan_for_company"


@mock.patch(GET_WORKSPACES)
@mock.patch(GET_FREE_USE)
@mock.patch(GET_USED)
@mock.patch(GET_SMARTZAP_MESSAGES_COUNT)
@mock.patch(GET_KONQUEST_ACTIVE_USERS)
class BillingServiceTestCase(TestCase):
    fixtures = ["application"]

    def setUp(self):
        self.billing = BillingGeneral("2022-01-01", "2022-01-31")
        self.konquest_id = "0abf08ea-d252-4d7c-ab45-ab3f9135c288"
        self.billing_konquest = BillingKonquestService()
        self.billing_smartzap = BillingSmartzapService()
        self.company = mommy.make(Company)
        self.workspace = mommy.make(Workspace, id=uuid.uuid4(), company=self.company)
        self.workspace_id = [self.workspace.id, self.workspace.name]

    def test_create_konquest_billing(
        self, mock_konquest, mock_smartzap, mock_used, mc, mock_get_workspaces
    ):
        mock_used.return_value = 35
        mc.return_value = 150
        billing = self.billing.execute([self.company], self.konquest_id)

        self.assertEqual(billing[0].get("billing").balance, 115)
