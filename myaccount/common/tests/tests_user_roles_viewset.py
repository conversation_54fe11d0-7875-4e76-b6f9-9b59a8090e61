import uuid
from unittest import mock

from django.test import TestCase
from django.urls import reverse
from mock_targets import CHECK_ROLE
from model_mommy import mommy
from rest_framework.test import APIClient

from common.models import Role, User, UserRoleWorkspace, Workspace
from common.services.user_service import UserService


@mock.patch("config.celery.app.send_task", return_value={})
@mock.patch("tasks.platform_integration_task.integration_task_publish.delay", return_value={})
@mock.patch(CHECK_ROLE, return_value=True)
class UserRoleWorkspaceViewsetTestCase(TestCase):
    """
    Create all test cases to add roles for users

    Doc: https://docs.google.com/spreadsheets/d/1nbBYvmd_e5nyyDkIyHIOvrv1PPTYWQzmtD2G_uFubb0
    """

    fixtures = ["application", "services", "language_preference", "roles"]

    def setUp(self):
        self.client = APIClient()
        self.user = mommy.make(User, id=uuid.uuid4())
        self.user_account_admin = mommy.make(User, id=uuid.uuid4())
        self.user_konquest = mommy.make(User, id=uuid.uuid4())
        self.workspace = mommy.make(Workspace, id=uuid.uuid4())
        self.myaccount_user_role = Role.objects.get(id="3b16b975-0297-4edf-950b-e3700b0d0d01")
        self.myaccount_keeps_admin_role = Role.objects.get(id="e67234f4-957b-483d-badc-2fbcd6cd4173")

        with mock.patch("tasks.notifications.notify_user.delay", return_value={}):
            self.user_workspace_role = mommy.make(
                UserRoleWorkspace, user=self.user, workspace=self.workspace, role=self.myaccount_user_role
            )

            self.user_workspace_role = mommy.make(
                UserRoleWorkspace, user=self.user, workspace=self.workspace, role=self.myaccount_keeps_admin_role
            )

            self.user_workspace_role = mommy.make(
                UserRoleWorkspace, user=self.user_account_admin, workspace=self.workspace, role=self.myaccount_user_role
            )
            with mock.patch("tasks.platform_service.PlatformIntegrationService.publish", return_value={}):
                self.user_konquest_role = mommy.make(
                    UserRoleWorkspace,
                    user=self.user_konquest,
                    workspace=self.workspace,
                    role=Role.objects.get(id="a6d23aea-807e-4374-964e-c725b817742d"),
                )

        self.headers = {"HTTP_X_CLIENT_ID": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})
        self.url = reverse("user-role-detail")

    def test_add_user_role_myacc(self, mock_return, mock_platform, mock_notify):
        """
        CASE 1: Myacc (role account_admin)
            - Notification Type: NOT
            - Password: NOT
        """
        mommy.make(User, id=uuid.uuid4())
        mock_platform.assert_not_called()
        mock_notify.assert_not_called()

    def test_add_user_role_myacc_admin(self, mock_return, mock_platform, mock_notify):
        """
        CASE 2: Myacc (role account_admin) and add new MyAcc role (workspace_admin)
            - Notification Type: NOT
            - Password: NOT
        """
        data = {
            "user": str(self.user_account_admin.id),
            "role": "77e3a833-94b5-4c37-891d-988513eabb67",
            "workspace": str(self.workspace.id),
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)
        mock_platform.assert_not_called()
        mock_notify.assert_not_called()

    @mock.patch.object(UserService, "update_user_password")
    def test_add_user_role_konquest_admin(self, update_user_password, mock_return, mock_platform, mock_notify):
        """
        CASE 3: Myacc (role account_admin) and add new Konquest role (admin)
            - Notification Type: ONBOARDING
            - Password: SEND
        """
        data = {
            "user": str(self.user_account_admin.id),
            "role": "297a88de-c34b-4661-be8a-7090fa9a89e5",
            "workspace": str(self.workspace.id),
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)
        mock_notify.assert_called_once()

    @mock.patch.object(UserService, "update_user_password")
    def test_add_user_role_konquest_content(self, update_user_password, mock_return, mock_platform, mock_notify):
        """
        CASE 3: Myacc (role account_admin) and add new Konquest role (content)
            - Notification Type: ONBOARDING
            - Password: SEND
        """
        data = {
            "user": str(self.user_account_admin.id),
            "role": "97f4a026-f727-4e23-bdf9-971fec7ce20e",
            "workspace": str(self.workspace.id),
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)
        mock_notify.assert_called_once()

    @mock.patch.object(UserService, "update_user_password")
    def test_add_user_role_konquest_super_admin(self, update_user_password, mock_return, mock_platform, mock_notify):
        """
        CASE 3: Myacc (role account_admin) and add new Konquest role (content)
            - Notification Type: ONBOARDING
            - Password: SEND
        """
        data = {
            "user": str(self.user_account_admin.id),
            "role": "c2a0da89-311d-4e4f-bf7b-c49d7c15f2b6",
            "workspace": str(self.workspace.id),
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)
        mock_notify.assert_called_once()

    @mock.patch.object(UserService, "update_user_password")
    def test_add_user_role_konquest_user(self, update_user_password, mock_return, mock_platform, mock_notify):
        """
        CASE 3: Myacc (role account_admin) and add new Konquest role (content)
            - Notification Type: ONBOARDING
            - Password: SEND
        """
        data = {
            "user": str(self.user_account_admin.id),
            "role": "a6d23aea-807e-4374-964e-c725b817742d",
            "workspace": str(self.workspace.id),
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)
        mock_notify.assert_called_once()

    def test_add_user_role_second_role_for_konquest(self, mock_return, mock_platform, mock_notify):
        """
        CASE 4: Myacc (role account_admin), Konquest (user) and add new Konquest role (content)
            - Notification Type: NOT
            - Password: NOT
        """
        data = {
            "user": str(self.user_konquest.id),
            "role": "97f4a026-f727-4e23-bdf9-971fec7ce20e",
            "workspace": str(self.workspace.id),
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        self.assertEqual(response.status_code, 201)
        mock_notify.assert_not_called()

    @mock.patch.object(UserService, "update_user_password")
    def test_add_user_role_smartzap_admin(self, update_user_password, mock_return, mock_platform, mock_notify):
        """
        CASE 5: Myacc (role account_admin) and add new Smartzap role (admin)
            - Notification Type: ONBOARDING
            - Password: SEND
        """
        data = {
            "user": str(self.user_account_admin.id),
            "role": "3d010792-7119-4e14-bea3-5258a31f1ddc",
            "workspace": str(self.workspace.id),
        }

        data_integration = {
            "id": str(self.user_account_admin.id),
            "name": self.user_account_admin.name,
            "email": self.user_account_admin.email,
            "avatar": self.user_account_admin.avatar,
            "phone": self.user_account_admin.phone,
            "country": self.user_account_admin.country,
            "status": self.user_account_admin.status,
            "ein": self.user_account_admin.ein,
            "workspace_id": str(self.workspace.id),
            "language": self.user_account_admin.language.name if self.user_account_admin.language else None,
            "related_user_leader_id": (
                str(self.user_account_admin.related_user_leader.id)
                if self.user_account_admin.related_user_leader
                else None
            ),
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)
        mock_notify.assert_called_once()

    def test_add_user_role_second_role_smartzap(self, mock_return, mock_platform, mock_notify):
        """
        CASE 8: Myacc (role account_admin), Konquest (user) and add new Smartzap role (admin)
            - Notification Type: INVITE
            - Password: KEEP
        """
        data = {
            "user": str(self.user_konquest.id),
            "role": "3d010792-7119-4e14-bea3-5258a31f1ddc",
            "workspace": str(self.workspace.id),
        }

        data_integration = {
            "id": str(self.user_konquest.id),
            "name": self.user_konquest.name,
            "email": self.user_konquest.email,
            "avatar": self.user_konquest.avatar,
            "phone": self.user_konquest.phone,
            "country": self.user_account_admin.country,
            "status": self.user_account_admin.status,
            "ein": self.user_account_admin.ein,
            "workspace_id": str(self.workspace.id),
            "language": self.user_account_admin.language.name if self.user_account_admin.language else None,
            "related_user_leader_id": (
                str(self.user_account_admin.related_user_leader.id)
                if self.user_account_admin.related_user_leader
                else None
            ),
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)
        mock_notify.assert_called_once()

    def test_add_user_role_konquest_admin_with_self_sing_up(self, mock_return, mock_platform, mock_notify):
        """
        CASE 10: Myacc (role account_admin) and add new Konquest role (admin) with self_sing_up
            - Notification Type: NOT
            - Password: NOT
        """
        data = {
            "user": str(self.user_account_admin.id),
            "role": "297a88de-c34b-4661-be8a-7090fa9a89e5",
            "workspace": str(self.workspace.id),
            "self_sign_up": True,
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)
        mock_notify.assert_not_called()

    def test_workspace_delete_user_role_smartzap_admin(self, mock_return, mock_platform, mock_notify):
        role = mommy.make(
            UserRoleWorkspace, user=self.user, workspace=self.workspace, role_id="3d010792-7119-4e14-bea3-5258a31f1ddc"
        )
        data_integration = {"id": str(self.user.id), "email": self.user.email, "workspace_id": str(self.workspace.id)}

        response = self.client.delete(self.url + f"/{str(role.id)}", **self.headers, format="json")
        self.assertEqual(response.status_code, 204)
