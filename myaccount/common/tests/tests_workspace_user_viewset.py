import uuid
from unittest import mock

from constants import QUERY_PARAM_FIELDS
from django.test import TestCase
from django.urls import reverse
from mock_targets import CHECK_ROLE
from model_mommy import mommy
from rest_framework.test import APIClient
from config.settings import MYACCOUNT_DEFAULT_ROLE, KONQUEST_DEFAULT_ROLE, SMARTZAP_DEFAULT_ROLE, ANALYTICS_DEFAULT_ROLE

from common.models import Job, Role, User, UserRoleWorkspace, Workspace
from common.models.service_workspace import ServiceWorkspace

kc_user_mock = "0176b6c7-56be-45b0-a801-d357f754342e"
KEYCLOAK_UPDATE_USER = "utils.keycloak.KeyCloakUserManager.update_user"

@mock.patch("config.celery.app.send_task", return_value={})
@mock.patch("common.services.user_service.UserService._create_keycloak_user", return_value=None)
@mock.patch("common.services.user_service.UserService._get_keycloak_user_id", return_value=kc_user_mock)
@mock.patch("tasks.platform_integration_task.integration_task_publish.delay", return_value={})
@mock.patch(CHECK_ROLE, return_value=True)
class WorkspaceUserViewSetTestCase(TestCase):
    fixtures = ["application", "services", "language_preference", "roles"]

    def setUp(self):
        self.client = APIClient()
        self.user = mommy.make(User, id=uuid.uuid4(), email="<EMAIL>")
        self.workspace = mommy.make(Workspace, id=uuid.uuid4())
        self.myaccount_user_role = Role.objects.get(id="3b16b975-0297-4edf-950b-e3700b0d0d01")

        with mock.patch("tasks.notifications.notify_user.delay", return_value={}):
            self.user_workspace_role = mommy.make(
                UserRoleWorkspace, user=self.user, workspace=self.workspace, role=self.myaccount_user_role
            )

        self.headers = {"HTTP_X_CLIENT_ID": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})
        self.url = reverse("workspace-users-list", args=[str(self.workspace.id)])

    def test_workspace_users(self, check_role, mock_platform, mock_kc_get_user, mock_kc_create_user, mock_notify):
        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)
        self.assertEqual(response["results"][0]["roles"][0]["role"]["key"], "account_admin")
        self.assertEqual(response["results"][0]["roles"][0]["role"]["application"]["name"], "My Account")

    def test_workspace_users_role_respect_filter(
        self, check_role, mock_platform, mock_kc_get_user, mock_kc_create_user, mock_notify
    ):
        self.user2 = mommy.make(User, id=uuid.uuid4(), email="<EMAIL>")
        self.workspace2 = mommy.make(Workspace, id=uuid.uuid4())
        self.myaccount_user_role2 = Role.objects.get(id="c2a0da89-311d-4e4f-bf7b-c49d7c15f2b6")
        with mock.patch("tasks.notifications.notify_user.delay", return_value={}):
            mommy.make(UserRoleWorkspace, user=self.user2, workspace=self.workspace2, role=self.myaccount_user_role)
            mommy.make(UserRoleWorkspace, user=self.user2, workspace=self.workspace, role=self.myaccount_user_role2)
        response = self.client.get(
            f"{self.url}?role_id=3b16b975-0297-4edf-950b-e3700b0d0d01", **self.headers, format="json"
        ).json()
        self.assertEqual(len(response["results"]), 1)

    def test_list_users_ids(self, check_role, mock_platform, mock_kc_get_user, mock_kc_create_user, mock_notify):
        response = self.client.get(f"{self.url}?{QUERY_PARAM_FIELDS}=id", **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)
        self.assertEqual(response["results"][0], {"id": str(self.user.id)})

    @mock.patch(KEYCLOAK_UPDATE_USER)
    def test_workspace_create_user(
        self, keycloak_update_user, check_role, mock_platform, mock_kc_get_user, mock_kc_create_user, mock_notify
    ):
        name = "Test"
        email = "<EMAIL>"
        data = {
            "users": [
                {
                    "name": name,
                    "nickname": "AutoTest",
                    "email": email,
                    "secondary_email": "",
                    "phone": "*************",
                    "gender": "MALE",
                    "address": "Acate",
                    "cpf": "***********",
                    "admission_date": "2023-09-19",
                    "marital_status": "SOLTEIRO",
                    "education": "ENSINO MÉDIO",
                    "hierarchical_level": "NENHUM",
                    "contract_type": "PJ",
                    "birthday": "2023-09-19",
                    "profile": {
                        "job": "TESTER",
                        "director": "Director",
                        "manager": "Manager",
                        "area_of_activity": "Development",
                    },
                }
            ],
            "permissions": [str(self.myaccount_user_role.id)],
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        self.assertEqual(response.status_code, 201)
        response_data = response.json()[0]
        payload_user = data["users"][0]
        payload_user["email"] = payload_user["email"].lower()
        payload_profile = data["users"][0].pop("profile")

        for field in payload_user:
            self.assertEqual(response_data[field], payload_user[field])
        for field in payload_profile:
            self.assertEqual(response_data["profiles"][0][field], payload_profile[field])
        self.assertEqual(response_data["roles"][0]["role"]["key"], "account_admin")
        self.assertEqual(response_data["roles"][0]["role"]["application"]["name"], "My Account")
        keycloak_update_user.assert_called_with(
            kc_user_mock, {"firstName": name, "username": email, "email": email, "attributes": {"locale": ["pt-BR"]}}
        )
        mock_platform.assert_not_called()

    @mock.patch(KEYCLOAK_UPDATE_USER)
    def test_workspace_create_user_with_older_job_field(
        self, keycloak_update_user, check_role, mock_platform, mock_kc_get_user, mock_kc_create_user, mock_notify
    ):
        data = {
            "users": [
                {
                    "name": "Test",
                    "email": "<EMAIL>",
                    "job": "TESTER",
                }
            ],
            "permissions": [str(self.myaccount_user_role.id)],
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)
        response_data = response.json()[0]
        payload_user = data["users"][0]
        payload_user["email"] = payload_user["email"].lower()

        self.assertEqual(response_data["profiles"][0]["job"], "TESTER")

    def test_workspace_create_user_already_exist(
        self, check_role, mock_platform, mock_kc_get_user, mock_kc_create_user, mock_notify
    ):
        workspace = mommy.make(Workspace, id=uuid.uuid4(), name="First workspace Add")
        user_exist = mommy.make(User, id=kc_user_mock, email="<EMAIL>")
        mommy.make(UserRoleWorkspace, user=user_exist, workspace=self.workspace, role=self.myaccount_user_role)
        mommy.make(UserRoleWorkspace, user=self.user, workspace=workspace, role=self.myaccount_user_role)
        mommy.make(Job, name="CEP", workspace=workspace)

        data = {
            "users": [
                {
                    "name": "New Test Name",
                    "nickname": "AutoTest",
                    "email": "<EMAIL>",
                    "secondary_email": "",
                    "phone": "*************",
                    "gender": "MALE",
                    "job": "CEP",
                    "birthday": None,
                    "address": "Acate",
                }
            ],
            "permissions": [],
        }
        self.url = reverse("workspace-users-list", args=[str(workspace.id)])

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_data = response.json()
        self.assertEqual(response.status_code, 201)

        self.assertEqual(response_data[0]["name"], data["users"][0]["name"].strip())
        self.assertEqual(response_data[0]["email"], data["users"][0]["email"].lower())
        self.assertEqual(response_data[0]["nickname"], data["users"][0]["nickname"])
        self.assertEqual(response_data[0]["job"], data["users"][0]["job"])
        self.assertEqual(response_data[0]["roles"][0]["role"]["key"], "account_admin")
        mock_platform.assert_not_called()

    @mock.patch(KEYCLOAK_UPDATE_USER)
    def test_workspace_create_user_without_role(
        self, update_user, check_role, mock_platform, mock_kc_get_user, mock_kc_create_user, mock_notify
    ):
        mommy.make(Job, name="CEO", workspace=self.workspace)
        data = {
            "users": [
                {
                    "name": "Test",
                    "nickname": "AutoTest",
                    "email": "<EMAIL>",
                    "secondary_email": "",
                    "phone": "*************",
                    "gender": "MALE",
                    "job": "CEO",
                    "birthday": None,
                    "address": "Acate",
                    "language_id": "ea636f50-fdc4-49b0-b2de-9e5905de456b",
                }
            ],
            "permissions": [],
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_data = response.json()
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response_data[0]["name"], data["users"][0]["name"])
        self.assertEqual(response_data[0]["email"], data["users"][0]["email"])
        self.assertEqual(response_data[0]["nickname"], data["users"][0]["nickname"])
        self.assertEqual(response_data[0]["job"], data["users"][0]["job"])
        self.assertEqual(response_data[0]["roles"][0]["role"]["key"], "account_admin")
        self.assertEqual(response_data[0]["roles"][0]["role"]["application"]["name"], "My Account")
        mock_platform.assert_not_called()

    @mock.patch("common.services.user_service.UserService._update_keycloak_user", return_value=None)
    def test_workspace_update_user(
        self, check_role, mock_platform, mock_kc_get_user, mock_kc_create_user, mock_notify, mock_update_kc_user
    ):
        data = {
            "name": "new name",
        }

        response = self.client.patch(self.url + f"/{self.user.id}", **self.headers, data=data, format="json")
        response_json = response.json()
        self.assertEqual(response_json["name"], data["name"])
        self.assertEqual(response.status_code, 200)
        mock_platform.assert_not_called()

    @mock.patch(KEYCLOAK_UPDATE_USER)
    def test_workspace_update_user_when_data_contains_empty_character(
        self, check_role, mock_platform, mock_kc_get_user, mock_kc_create_user, mock_notify, mock_update_kc_user
    ):
        data = {
            "id": self.user.id,
            "name": "Test Test",
            "nickname": "Test",
            "email": self.user.email,
            "secondary_email": "",
            "phone": "",
            "address": "",
            "language_id": "ea636f50-fdc4-49b0-b2de-9e5905de456b",
            "job": "NOT EXIST",
            "gender": None,
        }

        response = self.client.put(self.url + f"/{self.user.id}", **self.headers, data=data, format="json")
        response_json = response.json()
        self.assertEqual(response_json["name"], data["name"])
        self.assertEqual(response.status_code, 200)
        mock_platform.assert_not_called()

    def test_workspace_delete_user(self, check_role, mock_platform, mock_kc_get_user, mock_kc_create_user, mock_notify):
        response = self.client.delete(self.url + f"/{self.user.id}", **self.headers, format="json")
        self.assertEqual(response.status_code, 204)
        mock_platform.assert_not_called()

    def test_workspace_try_delete_user(
        self, check_role, mock_platform, mock_kc_get_user, mock_kc_create_user, mock_notify
    ):
        response = self.client.delete(self.url + f"/{uuid.uuid4()}", **self.headers, format="json")
        self.assertEqual(response.status_code, 404)
        mock_platform.assert_not_called()

    def test_workspace_delete_user_with_role_smartzap(
        self, check_role, mock_platform, mock_kc_get_user, mock_kc_create_user, mock_notify
    ):
        mommy.make(
            UserRoleWorkspace, user=self.user, workspace=self.workspace, role_id="3d010792-7119-4e14-bea3-5258a31f1ddc"
        )

        response = self.client.delete(self.url + f"/{self.user.id}", **self.headers, format="json")
        self.assertEqual(response.status_code, 204)

    def test_workspace_create_user_set_default_permissions_workspace(
        self, check_role, mock_platform, mock_kc_get_user, mock_kc_create_user, mock_notify
    ):
        user_exist = mommy.make(User, id=kc_user_mock, email="<EMAIL>")
        mommy.make(UserRoleWorkspace, user=user_exist, workspace=self.workspace, role=self.myaccount_user_role)
        workspace = mommy.make(Workspace, id=uuid.uuid4(), name="First workspace Add")
        ServiceWorkspace.objects.create(
            workspace = workspace,
            service_id='525e39e2-8054-45f4-93c5-2f132fa4d73a',
            status = True
        )
        ServiceWorkspace.objects.create(
            workspace = workspace,
            service_id='f9743ebc-c159-4dec-9600-cf1f9f0537b3',
            status = True
        )
        ServiceWorkspace.objects.create(
            workspace = workspace,
            service_id='0d3752f0-15d7-402a-8628-04ed47bcbf41',
            status = True
        )

        mommy.make(Job, name="CEP", workspace=workspace)
        data = {
            "users": [
                {
                    "name": "New Test Name",
                    "nickname": "AutoTest",
                    "email": "<EMAIL>",
                    "secondary_email": "",
                    "phone": "*************",
                    "gender": "MALE",
                    "job": "CEP",
                    "birthday": None,
                    "address": "Acate",
                }
            ],
            "permissions": [],
        }
        self.url = reverse("workspace-users-list", args=[str(workspace.id)])
        response = self.client.post(self.url, **self.headers, data=data, format="json")
        self.assertEqual(response.status_code, 201)
        user_roles_workspace = UserRoleWorkspace.objects.filter(user__email="<EMAIL>", workspace=workspace)
        self.assertEqual(user_roles_workspace.count(), 3)
        roles = [str(x.role.id) for x in user_roles_workspace]
        self.assertIn(MYACCOUNT_DEFAULT_ROLE, roles)
        self.assertIn(KONQUEST_DEFAULT_ROLE, roles)
        self.assertIn(ANALYTICS_DEFAULT_ROLE, roles)
        self.assertEqual(user_roles_workspace.first().user.email, data["users"][0]["email"])

    def test_workspace_create_user_already_exist_in_another_workspace(
        self, check_role, mock_platform, mock_kc_get_user, mock_kc_create_user, mock_notify
    ):
        user_exist = mommy.make(User, id=kc_user_mock, email="<EMAIL>")
        mommy.make(UserRoleWorkspace, user=user_exist, workspace=self.workspace, role=self.myaccount_user_role)

        workspace = mommy.make(Workspace, id=uuid.uuid4(), name="First workspace Add")
        mommy.make(Job, name="CEP", workspace=workspace)
        data = {
            "users": [
                {
                    "name": "New Test Name",
                    "nickname": "AutoTest",
                    "email": "<EMAIL>",
                    "secondary_email": "",
                    "phone": "*************",
                    "gender": "MALE",
                    "job": "CEP",
                    "birthday": None,
                    "address": "Acate",
                }
            ],
            "permissions": [],
        }
        self.url = reverse("workspace-users-list", args=[str(workspace.id)])
        response = self.client.post(self.url, **self.headers, data=data, format="json")
        self.assertEqual(response.status_code, 201)
        user_roles_workspace = UserRoleWorkspace.objects.filter(user__email="<EMAIL>", workspace=workspace)
        self.assertEqual(user_roles_workspace.count(), 1)
        self.assertEqual(user_roles_workspace.first().user.email, data["users"][0]["email"])

    @mock.patch(KEYCLOAK_UPDATE_USER)
    def test_workspace_create_user_job_not_exist(
        self, keycloak_update_user, check_role, mock_platform, mock_kc_get_user, mock_kc_create_user, mock_notify
    ):
        name = "Test"
        email = "<EMAIL>"
        data = {
            "users": [
                {
                    "name": name,
                    "nickname": "AutoTest",
                    "email": email,
                    "secondary_email": "",
                    "phone": "*************",
                    "gender": "MALE",
                    "birthday": None,
                    "address": "Acate",
                    "profile": {
                        "job": "Padeiro",
                        "director": "Director",
                        "manager": "Manager",
                        "area_of_activity": "Development",
                    },
                }
            ],
            "permissions": [str(self.myaccount_user_role.id)],
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        self.assertEqual(response.status_code, 201)

    @mock.patch(KEYCLOAK_UPDATE_USER)
    def test_workspace_create_user_job_with_pk(
        self, keycloak_update_user, check_role, mock_platform, mock_kc_get_user, mock_kc_create_user, mock_notify
    ):
        job = mommy.make(Job, name="PADEIRO", workspace=self.workspace)
        name = "Test"
        email = "<EMAIL>"
        data = {
            "users": [
                {
                    "name": name,
                    "nickname": "AutoTest",
                    "email": email,
                    "secondary_email": "",
                    "phone": "*************",
                    "gender": "MALE",
                    "birthday": None,
                    "address": "Acate",
                    "profile": {
                        "job_position": str(job.id),
                        "director": "Director",
                        "manager": "Manager",
                        "area_of_activity": "Development",
                    },
                }
            ],
            "permissions": [str(self.myaccount_user_role.id)],
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        self.assertEqual(response.status_code, 201)
        response_data = response.json()[0]
        payload_user = data["users"][0]
        payload_user["email"] = payload_user["email"].lower()
        payload_profile = data["users"][0].pop("profile")
        for field in payload_user:
            print(field)
            self.assertEqual(response_data[field], payload_user[field])
        for field in payload_profile:
            print(field)
            print(response_data["profiles"][0][field], payload_profile[field])
            self.assertEqual(response_data["profiles"][0][field], payload_profile[field])
        self.assertEqual(response_data["roles"][0]["role"]["key"], "account_admin")
        self.assertEqual(response_data["roles"][0]["role"]["application"]["name"], "My Account")
        keycloak_update_user.assert_called_with(
            kc_user_mock, {"firstName": name, "username": email, "email": email, "attributes": {"locale": ["pt-BR"]}}
        )
        mock_platform.assert_not_called()

    @mock.patch(KEYCLOAK_UPDATE_USER)
    def test_workspace_create_user_job_blank(
        self, keycloak_update_user, check_role, mock_platform, mock_kc_get_user, mock_kc_create_user, mock_notify
    ):
        name = "Test"
        email = "<EMAIL>"
        data = {
            "users": [
                {
                    "name": name,
                    "nickname": "AutoTest",
                    "email": email,
                    "secondary_email": "",
                    "phone": "*************",
                    "gender": "MALE",
                    "birthday": None,
                    "job": "",
                    "address": "Acate",
                    "profile": {
                        "job_position": "",
                        "director": "Director",
                        "manager": "Manager",
                        "area_of_activity": "Development",
                    },
                }
            ],
            "permissions": [str(self.myaccount_user_role.id)],
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        self.assertEqual(response.status_code, 201)

    @mock.patch(KEYCLOAK_UPDATE_USER)
    def test_workspace_create_user_ein_float(
        self, keycloak_update_user, check_role, mock_platform, mock_kc_get_user, mock_kc_create_user, mock_notify
    ):
        name = "Test"
        email = "<EMAIL>"
        data = {
            "users": [
                {
                    "name": name,
                    "nickname": "AutoTest",
                    "email": email,
                    "secondary_email": "",
                    "phone": "*************",
                    "gender": "MALE",
                    "address": "Acate",
                    "cpf": "***********",
                    "admission_date": "2023-09-19",
                    "marital_status": "SOLTEIRO",
                    "education": "ENSINO MÉDIO",
                    "hierarchical_level": "NENHUM",
                    "contract_type": "PJ",
                    "birthday": "2023-09-19",
                    "ein": "*********.0",
                    "profile": {
                        "job": "TESTER",
                        "director": "Director",
                        "manager": "Manager",
                        "area_of_activity": "Development",
                    },
                }
            ],
            "permissions": [str(self.myaccount_user_role.id)],
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        self.assertEqual(response.status_code, 201)
        response_data = response.json()[0]
        self.assertEqual(response_data.get("ein"), "*********")


    @mock.patch(KEYCLOAK_UPDATE_USER)
    def test_workspace_update_user_ein_float(
        self, keycloak_update_user, check_role, mock_platform, mock_kc_get_user, mock_kc_create_user, mock_notify
    ):
        data = {
            "id": self.user.id,
            "ein": "*********.0",
        }

        response = self.client.put(self.url + f"/{self.user.id}", **self.headers, data=data, format="json")
        response_data = response.json()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response_data.get("ein"), "*********")