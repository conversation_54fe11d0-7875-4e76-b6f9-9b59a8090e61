from unittest import mock

from django.test import TestCase, override_settings
from model_mommy import mommy
from rest_framework.test import APIClient

from common.models import User

KEYCLOAK_UPDATE_USER = "utils.keycloak.KeyCloakUserManager.update_user"
VERIFIER_USER_EMAIL = "common.tasks.user_tasks.verifier_user_email.delay"


@override_settings(SUSPEND_SIGNALS=False)
@mock.patch(KEYCLOAK_UPDATE_USER)
class UserSignalTestCase(TestCase):
    @override_settings(SUSPEND_SIGNALS=True)
    def setUp(self):
        self.user_keeps = mommy.make(User, name="User Keeps")
        self.client = APIClient()

    def test_control_keycloak_user_enabled(self, keycloak_update_user: mock.MagicMock):
        self.user_keeps.status = False
        self.user_keeps.save()
        keycloak_update_user.assert_called_with(self.user_keeps.id, {"enabled": False})
