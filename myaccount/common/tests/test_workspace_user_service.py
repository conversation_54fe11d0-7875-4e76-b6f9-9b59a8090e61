import uuid
from unittest import mock
from django.test import RequestFactory, TestCase, override_settings
from model_mommy import mommy
from common.models import LanguagePreference
from common.services.user_service import UserService
from common.services.workspace_user_service import WorkspaceUserService

@override_settings(SUSPEND_SIGNALS=True)
@mock.patch("common.services.user_service.UserService.get_or_create_keycloak_user", return_value=(uuid.uuid4()))
@mock.patch("common.services.user_service.UserService.update_user_password")
class TestWorkspaceUserService(TestCase):
    def setUp(self):
        self.factory = RequestFactory()
        self.workspace_user_service = WorkspaceUserService()
        self.language_pref = mommy.make(LanguagePreference)
        self.users_data = [
            {
                "email": "<EMAIL>",
                "name": "User 1",
                "nickname": "user1",
                "gender": "FEMALE",
                "language": self.language_pref,
            },
            {
                "email": "<EMAIL>",
                "name": "User 2",
                "nickname": "user2",
                "gender": "MALE",
                "language": self.language_pref,
            },
        ]
        self.permissions = [
            uuid.uuid4(),
            uuid.uuid4(),
        ]
        self.workspace_id = uuid.uuid4()

    @mock.patch.object(UserService, "add_roles")
    @mock.patch.object(UserService, "add_default_role")
    def test_save_users(self, add_roles: mock.MagicMock, save_role: mock.MagicMock, *args):
        saved_users = self.workspace_user_service.save_users(self.users_data, self.permissions, self.workspace_id)
        self.assertEqual(len(saved_users), len(self.users_data))

        for idx, user in enumerate(saved_users):
            self.assertTrue(user.created)
            self.assertEqual(user.email, self.users_data[idx]["email"])
            self.assertEqual(user.name, self.users_data[idx]["name"])
            self.assertEqual(user.nickname, self.users_data[idx]["nickname"])
            self.assertEqual(user.gender, self.users_data[idx]["gender"])
            self.assertEqual(user.language, self.users_data[idx]["language"])
