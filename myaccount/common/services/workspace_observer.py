import logging

from django.db.models.signals import post_delete, post_save
from utils.signals import receiver as keeps_receiver

from common import models
from common.models import BillingCompanyPlan, ServiceWorkspace

LOGGER = logging.getLogger(__name__)


@keeps_receiver(post_save, sender=models.ServiceWorkspace)
def create_service_billing_workspace(sender, instance: models.ServiceWorkspace, created, **kwargs):
    if created:
        BillingCompanyPlan.objects.get_or_create(
            application_id=instance.service.application_id, company_id=instance.workspace.company_id
        )


@keeps_receiver(post_delete, sender=models.ServiceWorkspace)
def delete_service_billing_workspace(sender, instance: models.ServiceWorkspace, **kwargs):
    if not deleted_all_company_services_for_application(instance):
        return None
    billing_plan = BillingCompanyPlan.objects.filter(
        application_id=instance.service.application_id, company_id=instance.workspace.company_id
    )
    billing_plan.delete()


def deleted_all_company_services_for_application(instance: ServiceWorkspace) -> int:
    count_company_application_service = ServiceWorkspace.objects.filter(
        workspace__company_id=instance.workspace.company_id,
        service__application_id=instance.service.application_id,
    ).count()
    return count_company_application_service == 0
