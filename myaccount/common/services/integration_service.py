from abc import ABC, abstractmethod

import requests
from custom.keeps_exception_handler import KeepsBadRequestError

from common.models import Role
from common.models.workspace import Workspace
from common.rest_clients.konquest_client import KonquestClient
from common.services.user_service import UserService


class IntegrationStrategy(ABC):
    @abstractmethod
    def active(self, workspace):
        ...


class IntegrationAlura(IntegrationStrategy):
    def __init__(self) -> None:
        self.user_service = UserService()
        self.konquest_client = KonquestClient()
        super().__init__()

    def active(self, workspace: Workspace, active: bool):
        if active:
            self._enable_integration(workspace)
        else:
            self._disable_integration(workspace)

    def _enable_integration(self, workspace: Workspace):
        KONQUEST_ADMIN_ROLE = Role.objects.get(id="297a88de-c34b-4661-be8a-7090fa9a89e5")
        user_data = {
            "email": "<EMAIL>",
            "name": "Alura Integration",
            "is_user_integration": True,
        }
        user, _ = self.user_service.save_with_validated_data(
            data=user_data,
            workspace_id=workspace.id,
        )

        self.user_service._add_role_by_integration({"role": KONQUEST_ADMIN_ROLE, "user": user, "workspace": workspace})
        request = self.konquest_client.enable_alura_integration(str(workspace.id))
        if request.status_code != 200:
            self._raise_exception(request)

    def _disable_integration(self, workspace: Workspace):
        request = self.konquest_client.disable_alura_integration(str(workspace.id))
        if request.status_code != 200:
            self._raise_exception(request)

    def _raise_exception(self, request: requests.Response):
        message_json = request.json()
        raise KeepsBadRequestError(detail=message_json.get("detail"), i18n=message_json.get("i18n"))


INTEGRATION_STRATEGIES = {"alura_integration_active": IntegrationAlura()}
