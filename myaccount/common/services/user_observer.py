import logging

from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from utils.email.clean_email import clean_email
from utils.keycloak import KeyCloakUserManager
from utils.signals import receiver as keeps_receiver

from common import models

LOGGER = logging.getLogger(__name__)


@keeps_receiver(post_save, sender=models.User)
def control_keycloak_user_enabled(sender, instance: models.User, **kwargs):
    if instance._state.adding:
        return
    keycloak_managed = KeyCloakUserManager()
    keycloak_managed.update_user(instance.id, {"enabled": instance.status})


@receiver(pre_save, sender=models.User)
def clean_user_email(sender, instance: models.User, **kwargs):
    if instance.email:
        instance.email = clean_email(instance.email)
