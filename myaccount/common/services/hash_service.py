import base64

from constants import UTF_8
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad


class HashService:
    def __init__(self, secret_key: str):
        self._secret_key_length = len(secret_key)
        self._secret_key = secret_key

    def encrypt(self, password: str):
        raw = pad(password.encode(), self._secret_key_length)
        cipher = AES.new(self._secret_key.encode(UTF_8), AES.MODE_ECB)
        return base64.b64encode(cipher.encrypt(raw)).decode()
