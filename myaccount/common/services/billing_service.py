import calendar
import uuid
from datetime import date

from config.settings import SMARTZAP_ID
from django.db.models import QuerySet

from common.models import Billing, BillingCompanyPlan, Company, ServiceWorkspace, Workspace
from common.selectors.billing_selector import Selector


class BillingGeneral:
    def __init__(self, start_date=None, end_date=None):
        self.start_date = start_date
        self.end_date = end_date
        self._selector = Selector()

    @staticmethod
    def format_billing_date_range(start_date: str, end_date: str):
        if start_date is not None and end_date is not None:
            return start_date, end_date
        first_day_current_month = date.today().replace(day=1)
        day = calendar.monthrange(first_day_current_month.year, first_day_current_month.month)[1]
        last_day_current_month = date.today().replace(day=day)

        return first_day_current_month, last_day_current_month

    def execute(self, companies: QuerySet, application_id: str) -> list:
        billings = []
        start_date, end_date = self.format_billing_date_range(self.start_date, self.end_date)
        for company in companies:
            billing = self._calc_company_billing(application_id, end_date, start_date, company)
            billings.append({"billing": billing})
        return billings

    def _calc_company_billing(self, application_id: str, end_date: str, start_date: str, company: Company) -> Billing:
        workspaces = self._get_company_workspaces(company, application_id)
        workspace_ids = [workspace.id for workspace in workspaces]
        used = self._selector.get_used_plan(application_id, workspace_ids, start_date, end_date)
        current_plan = self.get_current_plan_for_company(company.id, application_id)
        billing = Billing()
        billing.company = company
        billing.application_id = application_id
        billing.monthly_plan = current_plan
        billing.used = used
        billing.start_date = start_date
        billing.end_date = end_date
        billing.balance = self._calc_balance(billing)
        billing.save()
        return billing

    @staticmethod
    def _get_company_workspaces(company: Company, application_id: str):
        workspace_ids = ServiceWorkspace.objects.filter(
            workspace__company_id=company.id, service__application_id=application_id
        ).values_list("workspace_id", flat=True)
        return Workspace.objects.filter(id__in=workspace_ids)

    def _calc_balance(self, billing: Billing) -> int:
        if str(billing.application_id) == SMARTZAP_ID:
            return self._calc_smartzap_balance(billing)
        monthly_plan = billing.monthly_plan
        used = billing.used
        return monthly_plan - used

    @staticmethod
    def _calc_smartzap_balance(billing: Billing):
        past_billings = (
            Billing.objects.filter(application_id=SMARTZAP_ID, company=billing.company)
            .exclude(id=billing.id)
            .order_by("-end_date")[:2]
        )
        cumulative_balance = sum(billing.balance for billing in past_billings)
        return cumulative_balance

    @staticmethod
    def get_current_plan_for_company(company_id: uuid, application_id: str) -> int:
        billing, _ = BillingCompanyPlan.objects.get_or_create(company_id=company_id, application_id=application_id)
        return billing.current_plan
