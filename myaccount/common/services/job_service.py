from common.models.job import Job, JobFunction
from common.services.base_service import BaseService

# pylint: disable=R0903


class JobService(BaseService):
    def get_queryset(self, user_id):
        allowed_workspace_ids = list(self.get_allowed_workspaces(user_id))
        return Job.objects.filter(workspace_id__in=allowed_workspace_ids)


class JobPositionService(BaseService):
    def get_queryset(self, user_id):
        allowed_workspace_ids = list(self.get_allowed_workspaces(user_id))
        return JobFunction.objects.filter(workspace_id__in=allowed_workspace_ids)
