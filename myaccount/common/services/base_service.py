from common.models import Workspace
from common.models.user_role_workspace import UserRoleWorkspace

# pylint: disable=R0903


class BaseService:
    @staticmethod
    def get_allowed_workspaces(user_id):
        workspaces_allowed = UserRoleWorkspace.objects.filter(user_id=user_id).values_list("workspace_id", flat=True)
        return workspaces_allowed

    @staticmethod
    def get_workspaces(workspace_id):
        workspace_workspaces = Workspace.objects.filter(workspace_id=workspace_id).values_list("id", flat=True)
        return workspace_workspaces
