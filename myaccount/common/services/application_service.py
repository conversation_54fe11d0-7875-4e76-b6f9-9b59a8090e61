from config.settings import MY<PERSON><PERSON>UNT_ID
from django.db.models import Q

from common.models import Service
from common.models.application import Application
from common.models.service_workspace import ServiceWorkspace


class ApplicationService:
    @staticmethod
    def get_queryset():
        return Application.objects.all()

    @staticmethod
    def get_application_service_by_workspace(workspace_id):
        """
        Select roles for each workspace service active and my account roles.
        """
        _services = ServiceWorkspace.objects.filter(workspace_id=workspace_id).values_list("service_id", flat=True)
        _applications = Service.objects.filter(id__in=_services).values_list("application_id", flat=True)
        return Application.objects.filter(Q(id__in=_applications) | Q(id=MYACCOUNT_ID))
