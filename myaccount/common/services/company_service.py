from common.models import Company, Workspace
from common.services.base_service import BaseService
from common.services.user_service import UserService


class CompanyService(BaseService):
    def __init__(self):
        self._user_service = UserService()

    def list_allowed_companies(self, user_id):
        workspace_ids = self.get_allowed_workspaces(user_id)
        company_ids = Workspace.objects.filter(id__in=workspace_ids).values_list("company_id", flat=True)
        return Company.objects.filter(id__in=company_ids)
