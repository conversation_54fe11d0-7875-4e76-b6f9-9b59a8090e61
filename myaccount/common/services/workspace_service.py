import re

from custom.keeps_exception_handler import KeepsBadRequestError, KeepsNoPermissionToEditWorkspace
from django.conf import settings
from django.db import transaction
from django.db.models import Q
from django.utils.text import slugify
from utils.utils import workspace_hash

from common.models import Company, Role, Service, ServiceWorkspace, User, UserRoleWorkspace, Workspace
from common.services.base_service import BaseService
from common.services.gamification_service import GamificationService
from common.services.hash_service import HashService
from common.services.integration_service import INTEGRATION_STRATEGIES
from common.services.user_service import UserService


class WorkspaceService(BaseService):
    def __init__(self, hash_service: HashService):
        self._user_service = UserService()
        self._hash_service = hash_service
        self._gamification_service = GamificationService()

    def get_queryset(self, user_id):
        return Workspace.objects.filter(id__in=self.get_allowed_workspaces(user_id))

    def get_services_queryset(self, user_id, workspace_id):
        # Return only services where authentication user has relation
        services = ServiceWorkspace.objects.filter(
            workspace_id=workspace_id, workspace_id__in=self.get_allowed_workspaces(user_id)
        ).values_list("service", flat=True)

        return Service.objects.filter(id__in=services)

    def get_services_workspace_queryset(self, user_id, workspace_id):
        """
        Retorna apenas os serviços onde o usuário autenticado tem relação.

        TODO: Ajuste temporário para permitir filtrar os dados apenas com o ID da workspace.
        """

        filters = {"workspace_id": workspace_id}

        if user_id is not None:
            filters["workspace_id__in"] = self.get_allowed_workspaces(user_id)

        return ServiceWorkspace.objects.filter(**filters)

    @transaction.atomic
    def save(self, user_id, data: dict):
        """
        Create workspace and add basic rules to user logged:
        - workspace_admin: To the user to manage workspace profile and configurations
        - account_admin: To the user to manage your profile and configurations

        :param user_id: user logged
        :param data: company data
        :return: UserCompany instance
        """
        if not User.objects.filter(id=user_id).exists():
            raise KeepsBadRequestError("user_not_exist", "Check if user exist")

        workspace = Workspace(**data)
        if not workspace.company_id:
            company = self.create_default_company(workspace.name)
            workspace.company_id = company.id
        if not workspace.hash_id:
            workspace.hash_id = workspace_hash(workspace.id)
        else:
            workspace = self._format_workspace_hash_id(workspace)
        if workspace.smtp_auth_pass:
            workspace.smtp_auth_pass = self._hash_service.encrypt(workspace.smtp_auth_pass)
        workspace.save()

        self._create_admin_user(user_id, workspace)

        return workspace

    @staticmethod
    def _format_workspace_hash_id(workspace):
        workspace.hash_id = slugify(workspace.hash_id).lower()
        workspace.hash_id = re.sub(r"[^a-z0-9\-]+", "", workspace.hash_id)
        existing_workspaces = Workspace.objects.filter(Q(hash_id=workspace.hash_id) & ~Q(id=workspace.id))
        if existing_workspaces.exists():
            suffix = "-lxp-1"
            i = 1
            while Workspace.objects.filter(hash_id=workspace.hash_id + suffix).exists():
                i += 1
                suffix = f"-lxp-{i}"
            workspace.hash_id = workspace.hash_id + suffix
        return workspace

    @transaction.atomic
    def update(self, workspace_id: str, data: dict, request_user_id: str) -> Workspace:
        workspace = Workspace.objects.get(id=workspace_id)
        if not self._has_admin_permission(request_user_id, workspace_id):
            raise KeepsNoPermissionToEditWorkspace()
        workspace.update(**data)
        if workspace.hash_id:
            workspace = self._format_workspace_hash_id(workspace)
        if "smtp_auth_pass" in data:
            workspace.smtp_auth_pass = self._hash_service.encrypt(workspace.smtp_auth_pass)
        self._enable_instegrations(workspace, data)
        workspace.save()
        return workspace

    def _enable_instegrations(self, workspace: Workspace, data: dict):
        for key, value in data.items():
            if key in INTEGRATION_STRATEGIES.keys():
                INTEGRATION_STRATEGIES[key].active(workspace, value)

    @staticmethod
    def _has_admin_permission(user_id: str, workspace_id: str):
        return UserRoleWorkspace.objects.filter(
            user_id=user_id, workspace_id=workspace_id, role__key="company_admin"
        ).exists()

    @staticmethod
    def _create_admin_user(user_id, workspace):
        user_company_role = UserRoleWorkspace()
        user_company_role.user_id = user_id
        user_company_role.workspace_id = workspace.id
        user_company_role.role = Role.objects.get(key="company_admin")
        user_company_role.save()
        user_account_role = UserRoleWorkspace()
        user_account_role.user_id = user_id
        user_account_role.workspace_id = workspace.id
        user_account_role.role = Role.objects.get(key="account_admin")
        user_account_role.save()

    @staticmethod
    def create_default_company(workspace_name: str) -> Company:
        company = Company(name=workspace_name)
        company.save()
        return company

    @transaction.atomic
    def add_service_in_workspace(self, workspace_id, service_id):
        """
        Add service to the workspace.
        """
        service_workspace = self._create_service_workspace(workspace_id, service_id)

        if self._is_gamification_service(service_id):
            self._gamification_service.create_gamification_rankings(workspace_id)

        return service_workspace

    @transaction.atomic
    def delete_service_from_workspace(self, workspace_id: str, service_id: str) -> bool:
        """
        Delete the service from the workspace. Returns True if deleted, False if not found.
        """
        instance = self._get_service_workspace(workspace_id, service_id)

        if not instance:
            return False

        if self._is_gamification_service(service_id):
            self._gamification_service.remove_gamification_rankings(workspace_id)

        instance.delete()
        return True

    def _create_service_workspace(self, workspace_id, service_id):
        """
        Helper method to create and save a ServiceWorkspace instance.
        """
        service_workspace = ServiceWorkspace()
        service_workspace.service_id = service_id
        service_workspace.workspace_id = workspace_id
        service_workspace.save()
        return service_workspace

    def _get_service_workspace(self, workspace_id, service_id):
        """
        Helper method to get a ServiceWorkspace instance.
        """
        return ServiceWorkspace.objects.filter(workspace_id=workspace_id, service_id=service_id).first()

    def _is_gamification_service(self, service_id):
        """
        Helper method to check if the service is the gamification service.
        """
        return str(service_id) == str(settings.GAMIFICATION_SERVICE_ID)
