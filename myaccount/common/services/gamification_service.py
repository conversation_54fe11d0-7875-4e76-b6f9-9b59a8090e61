from typing import List

from custom.keeps_exception_handler import KeepsError
from django.conf import settings
from django.db import transaction
from django.db.models import IntegerField, OuterRef, Subquery

from common.models.gamification_ranking_workspace import RANKING_CHOICES, GamificationRankingWorkspace
from common.models.user_profile_workspace import UserProfileWorkspace
from common.services.base_service import BaseService


class SQCount(Subquery):
    template = "(SELECT count(*) FROM (%(subquery)s) _count)"
    output_field = IntegerField()


class GamificationService(BaseService):
    def get_queryset(self, workspace_id):
        director = UserProfileWorkspace.objects.filter(
            workspace_id=OuterRef("workspace_id"), director__isnull=False
        ).values("director")
        manager = UserProfileWorkspace.objects.filter(
            workspace_id=OuterRef("workspace_id"), manager__isnull=False
        ).values("manager")
        leader = UserProfileWorkspace.objects.filter(
            workspace_id=OuterRef("workspace_id"), user__related_user_leader_id__isnull=False
        ).values("user__related_user_leader_id")
        activity_area = UserProfileWorkspace.objects.filter(
            workspace_id=OuterRef("workspace_id"), area_of_activity__isnull=False
        ).values("area_of_activity")
        total_users = UserProfileWorkspace.objects.filter(workspace_id=OuterRef("workspace_id")).values("user_id")

        queryset = (
            GamificationRankingWorkspace.objects.filter(workspace_id=workspace_id)
            .annotate(director_count=SQCount(director))
            .annotate(manager_count=SQCount(manager))
            .annotate(leader_count=SQCount(leader))
            .annotate(activity_area_count=SQCount(activity_area))
            .annotate(users_count=SQCount(total_users))
        )

        self._check_ranking_integrity(queryset)
        return queryset

    @transaction.atomic
    def edit_ranking(self, workspace_id, ranking_id, status):
        ranking = self.get_queryset(workspace_id=workspace_id).filter(id=ranking_id).first()
        if not ranking:
            raise KeepsError(status_code=404, detail="ranking not found", i18n="ranking_not_found")
        if status and not ranking.can_enable:
            message = "To activate the {} ranking, at least 70% of users must be linked to a {}".format(
                ranking.ranking, ranking.ranking
            )
            raise KeepsError(status_code=403, detail=message, i18n="ranking_not_allowed_to_enable")
        ranking.disable_ranking(notify=False)
        ranking.status = status
        ranking.save()
        response = "Ranking {} {}!".format(ranking.ranking, ("activated" if status else "deactivated"))
        return response

    @transaction.atomic
    def create_gamification_rankings(self, workspace_id):
        """
        Creates gamification rankings for the workspace.
        """
        for ranking, is_general in self._get_ranking_choices():
            ranking_workspace, created = GamificationRankingWorkspace.objects.get_or_create(
                workspace_id=workspace_id, service_id=settings.GAMIFICATION_SERVICE_ID, ranking=ranking
            )
            if created:
                ranking_workspace.status = is_general
                ranking_workspace.save()

    @transaction.atomic
    def remove_gamification_rankings(self, workspace_id):
        """
        Deletes all gamification rankings for the workspace.
        """
        GamificationRankingWorkspace.objects.filter(
            workspace_id=workspace_id, service_id=settings.GAMIFICATION_SERVICE_ID
        ).delete()

    def _get_ranking_choices(self):
        """
        Returns the ranking choices with an indicator if the ranking is 'general'.
        """
        return [(ranking[0], ranking[0] == "general") for ranking in RANKING_CHOICES]

    def _check_ranking_integrity(self, rankings: List[GamificationRankingWorkspace]):
        """
        Checks the integrity of the rankings and disables them if needed.
        """
        for ranking in rankings:
            if not ranking.can_enable and ranking.status:
                ranking.disable_ranking(notify=True)
