from config import settings

from common.selectors.billing_selector import Selector
from common.services.billing_service import BillingGeneral


class BillingSmartzapService:
    def __init__(self):
        self.application_id = settings.SMARTZAP_ID
        self._selector = Selector()
        self._billing_general = BillingGeneral()

    def generate(self, start_date=None, end_date=None):
        companies = self._selector.get_companies_with_application(self.application_id)
        self._billing_general.start_date = start_date
        self._billing_general.end_date = end_date
        self._billing_general.execute(companies, self.application_id)
