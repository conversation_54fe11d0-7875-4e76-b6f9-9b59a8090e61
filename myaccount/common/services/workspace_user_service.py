from typing import Sequence
from uuid import UUID

from config.settings import ANALYTICS_DEFAULT_ROLE, KONQUEST_DEFAULT_ROLE, MYACCOUNT_DEFAULT_ROLE
from custom.discord_webhook_logger import DiscordWebhookLogger
from custom.keeps_exception_handler import KeepsNotAllowedError
from django.conf import settings
from django.db.models import QuerySet

from common.models import Service, ServiceWorkspace, User, UserProfileWorkspace, UserRoleWorkspace
from common.models.workspace import Workspace
from common.services.base_service import BaseService
from common.services.user_service import UserService


class WorkspaceUserService(BaseService):
    def __init__(self):
        self._user_service = UserService()
        self._logger = DiscordWebhookLogger()

    def get_queryset(self, user_id):
        return Workspace.objects.filter(id__in=self.get_allowed_workspaces(user_id))

    def get_users_queryset(self, user_id, workspace_id):
        # Return only users where authentication user has relation
        users = UserRoleWorkspace.objects.filter(
            workspace_id=workspace_id, workspace_id__in=self.get_allowed_workspaces(user_id)
        ).values_list("user", flat=True)
        return User.objects.filter(id__in=users)

    def get_user_profiles(self, user_id: str, workspace_id: str) -> QuerySet:
        return UserProfileWorkspace.objects.filter(
            workspace_id=workspace_id, workspace_id__in=self.get_allowed_workspaces(user_id)
        )

    def check_permission_to_manage_workspace(self, user_id: str, workspace_id: UUID) -> None:
        allowed_workspace_ids = list(self.get_allowed_workspaces(user_id))
        not_allowed = workspace_id not in allowed_workspace_ids
        if not_allowed:
            raise KeepsNotAllowedError(
                detail="User is not allowed to proceed with this action", i18n="admin_identity_not_identify"
            )

    def get_user_services_queryset(self, user_id, workspace_id):
        # Return only services where authentication user has relation
        services = ServiceWorkspace.objects.filter(
            workspace_id=workspace_id, workspace_id__in=self.get_allowed_workspaces(user_id)
        ).values_list("service", flat=True)

        return Service.objects.filter(id__in=services)

    def save_users(self, users_data: Sequence[dict], permissions: Sequence[UUID], workspace_id: str) -> Sequence[User]:
        _users = []
        for user in users_data:
            try:
                user = self._save_user(user, permissions, workspace_id)
            except Exception as error:
                self._logger.emit_short_message(f"Error to save user {dict(user)}", error)
                user = User(email=user["email"])
            _users.append(user)

        return _users

    def _save_user(self, data: dict, permissions: Sequence[UUID], workspace_id: str) -> User:
        password = None
        identity_provider = None
        self_signup = False
        profile = None
        if "password" in data:
            password = data.pop("password")
            self_signup = True
        if "identity_provider" in data:
            identity_provider = data.pop("identity_provider")
            self_signup = True
        if "profile" in data:
            profile = data.pop("profile")

        user, password = self._user_service.save_with_validated_data(
            data=data,
            profile=profile,
            workspace_id=workspace_id,
            identity_provider=identity_provider,
            password=password,
            self_register=self_signup,
            temporary_password=data.pop("temporary_password", True),
        )
        user.created = True
        if not permissions:
            permissions = []
        else:
            myaccount_default_role = UUID(MYACCOUNT_DEFAULT_ROLE)
            if myaccount_default_role in permissions:
                permissions.remove(myaccount_default_role)

        services_enabled = ServiceWorkspace.objects.filter(status=True, workspace_id=workspace_id)

        service_konquest = services_enabled.filter(service__application_id=settings.KONQUEST_ID)
        if service_konquest.exists():
            permissions.append(UUID(KONQUEST_DEFAULT_ROLE))

        service_analytics = services_enabled.filter(service__application_id=settings.LEARNING_ANALYTICS_ID)
        if service_analytics.exists():
            permissions.append(UUID(ANALYTICS_DEFAULT_ROLE))

        permissions = list(set(permissions))
        self._user_service.add_roles(workspace_id, user.id, permissions, self_signup, password)
        return user
