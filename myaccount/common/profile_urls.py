from django.urls import path

from common.views.area_of_activity_viewset import AreaOfActivityViewSet
from common.views.director_viewset import DirectorValuesViewSet
from common.views.manager_viewset import ManagerValuesViewSet

LIST = {"get": "list"}

urlpatterns = [
    path("/managers", ManagerValuesViewSet.as_view(LIST), name="managers-values"),
    path("/directors", DirectorValuesViewSet.as_view(LIST), name="directors-values"),
    path("/areas-of-activity", AreaOfActivityViewSet.as_view(LIST), name="areas-of-activity-values"),
]
