import uuid
from string import Template
from typing import Sequence

from config import settings
from django.db import connections

from common.models import Company, ServiceWorkspace

# pylint: disable=E1121


class Selector:
    def __init__(self):
        self.base_templates = f"{settings.BASE_DIR}/common/selectors/tpl_sql/"
        self.applications_map = {
            settings.KONQUEST_ID: self.konquest_count_active_user_by_company,
            settings.SMARTZAP_ID: self.smartzap_count_messages,
        }

    def get_used_plan(self, application_id: str, workspace_ids: Sequence[uuid.UUID], start_date, end_date):
        workspace_ids = self._parser_list(workspace_ids)
        try:
            return self.applications_map[application_id](workspace_ids, start_date, end_date)
        except KeyError as exc:
            raise RuntimeError("Application not mapped") from exc

    @staticmethod
    def get_companies_with_application(application_id: str):
        company_ids = ServiceWorkspace.objects.filter(service__application_id=application_id).values_list(
            "workspace__company_id"
        )
        return Company.objects.filter(id__in=company_ids)

    def konquest_count_active_user_by_company(self, workspace_ids, start_date, end_date):
        with connections["konquest"].cursor() as cursor:
            query_file_path = self.base_templates + "query_users_missions_activities.txt"
            with open(query_file_path, encoding="utf-8") as file_data:
                get_users_missions_activities = Template(file_data.read()).substitute(
                    workspace_ids=workspace_ids, start_date=start_date, end_date=end_date
                )
            query_file_path = self.base_templates + "query_users_pulses_activities.txt"
            with open(query_file_path, encoding="utf-8") as file_data:
                get_users_pulses_activities = Template(file_data.read()).substitute(
                    workspace_ids=workspace_ids, start_date=start_date, end_date=end_date
                )
            cursor.execute(get_users_missions_activities)
            missions_users = list(cursor.fetchall())
            cursor.execute(get_users_pulses_activities)
            pulses_users = list(cursor.fetchall())
            total = missions_users + pulses_users

            return len(total)

    def smartzap_count_messages(self, workspace_ids, start_date, end_date):
        with connections["smartzap"].cursor() as cursor:
            query_file_path = self.base_templates + "query_messages_smartzap.txt"
            with open(query_file_path, encoding="utf-8") as file_data:
                query = Template(file_data.read()).substitute(
                    workspace_ids=workspace_ids, start_date=start_date, end_date=end_date
                )
            cursor.execute(query)
            fetched = cursor.fetchone()
            return fetched[0]

    @staticmethod
    def _parser_list(sequence: Sequence) -> str:
        return ",".join(f"'{value}'" for value in sequence)
