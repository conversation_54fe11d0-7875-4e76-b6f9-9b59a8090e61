SELECT DISTINCT(user_id) FROM learn_content_activity
JOIN mission_stage_content ON learn_content_activity.mission_stage_content_id=mission_stage_content.id
JOIN mission_stage ON mission_stage.id=mission_stage_content.stage_id
JOIN mission_workspace ON mission_workspace.mission_id=mission_stage.mission_id
WHERE mission_workspace.workspace_id in ($workspace_ids)
AND learn_content_activity.created_date >= '$start_date'
AND learn_content_activity.created_date <= '$end_date';
