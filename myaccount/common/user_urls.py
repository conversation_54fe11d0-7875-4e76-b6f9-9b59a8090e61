from django.urls import path

from common.views.images_viewset import UserAvatarViewSet
from common.views.public_profile_viewset import PublicProfileByIdView, PublicProfileView
from common.views.user_info_viewset import UserInfoView
from common.views.user_role_workspace_viewset import UserRoleWorkspaceViewSet
from common.views.user_viewset import UserSetPasswordBatchViewSet, UserSetPasswordViewSet, UserViewSet
from common.views.user_workspace_viewset import UserWorkspaceViewSet

_SAVE_ONLY = {"post": "create"}

_READ_ONLY = {"get": "list"}

_LIST = {"get": "list", "post": "create"}

_DETAIL = {"get": "retrieve", "put": "update", "patch": "partial_update", "delete": "destroy"}

_GET_UPDATE = {"get": "retrieve", "put": "update", "patch": "partial_update"}

urlpatterns = [
    path("", UserViewSet.as_view(_READ_ONLY), name="user-list"),
    path("/set-password", UserSetPasswordViewSet.as_view(_SAVE_ONLY), name="user-set-password"),
    path("/set-password/batch", UserSetPasswordBatchViewSet.as_view(_SAVE_ONLY), name="user-set-password-batch"),
    path("/info", UserInfoView.as_view(), name="user-info"),
    path("/<uuid:pk>", UserViewSet.as_view(_GET_UPDATE), name="user-detail"),
    path("/roles", UserRoleWorkspaceViewSet.as_view(), name="user-role-detail"),
    path("/roles/<uuid:pk>", UserRoleWorkspaceViewSet.as_view(), name="user-role-destroy"),
    path("/public/<str:email>", PublicProfileView.as_view(), name="user-public-detail"),
    path("/public-by-id/<uuid:user_id>", PublicProfileByIdView.as_view(), name="user-public-by-id-detail"),
    path("/<uuid:user_id>/workspaces", UserWorkspaceViewSet.as_view(_LIST), name="user-workspaces-list"),
    path("/<uuid:user_id>/workspaces/<uuid:pk>", UserWorkspaceViewSet.as_view(_DETAIL), name="user-workspaces-list"),
    path("/<uuid:user_id>/avatar", UserAvatarViewSet.as_view(_SAVE_ONLY), name="user-avatar"),
]
