[{"model": "common.role", "pk": "297a88de-c34b-4661-be8a-7090fa9a89e5", "fields": {"name": "Konquest Admin ", "key": "admin", "description": "Have access to config the environment", "application": "0abf08ea-d252-4d7c-ab45-ab3f9135c288", "status": true, "created_date": null, "updated_date": null}}, {"model": "common.role", "pk": "5f19d9b6-dc84-4db3-9074-8f8dfbbe51c8", "fields": {"name": "Konquest Instructor", "key": "instructor", "description": "Have access to your missions which will instruct", "application": "0abf08ea-d252-4d7c-ab45-ab3f9135c288", "status": true, "created_date": null, "updated_date": null}}, {"model": "common.role", "pk": "3b16b975-0297-4edf-950b-e3700b0d0d01", "fields": {"name": "My Account Admin", "key": "account_admin", "description": "Have access to config own information", "application": "ad7e5ad2-1552-43ab-a471-710954f0e66a", "status": true, "created_date": null, "updated_date": null}}, {"model": "common.role", "pk": "45f1fc7f-56f4-4208-a347-ee4d84a8f064", "fields": {"name": "Content Curator", "key": "curator", "description": "Have access to approve/reprove contents", "application": "0abf08ea-d252-4d7c-ab45-ab3f9135c288", "status": true, "created_date": null, "updated_date": null}}, {"model": "common.role", "pk": "77e3a833-94b5-4c37-891d-988513eabb67", "fields": {"name": "My Account Company Administrator", "key": "company_admin", "description": "Have access to config information of company", "application": "ad7e5ad2-1552-43ab-a471-710954f0e66a", "status": true, "created_date": null, "updated_date": null}}, {"model": "common.role", "pk": "97f4a026-f727-4e23-bdf9-971fec7ce20e", "fields": {"name": "Konquest Content", "key": "content", "description": "Create missions and pulses", "application": "0abf08ea-d252-4d7c-ab45-ab3f9135c288", "status": true, "created_date": null, "updated_date": null}}, {"model": "common.role", "pk": "a6d23aea-807e-4374-964e-c725b817742d", "fields": {"name": "Konquest User", "key": "user", "description": "Consume missions and pulses", "application": "0abf08ea-d252-4d7c-ab45-ab3f9135c288", "status": true, "created_date": null, "updated_date": null}}, {"model": "common.role", "pk": "0db05f21-6f26-4459-a0b9-7022901d202e", "fields": {"name": "Konquest Reporter", "key": "reporter", "description": "Has user permissions and can generate reports", "application": "0abf08ea-d252-4d7c-ab45-ab3f9135c288", "status": true, "created_date": null, "updated_date": null}}, {"model": "common.role", "pk": "c2a0da89-311d-4e4f-bf7b-c49d7c15f2b6", "fields": {"name": "Konquest Super Admin", "key": "super_admin", "description": "have full access to control", "application": "0abf08ea-d252-4d7c-ab45-ab3f9135c288", "status": true, "created_date": null, "updated_date": null}}, {"model": "common.role", "pk": "e67234f4-957b-483d-badc-2fbcd6cd4173", "fields": {"name": "FULL ADMIN", "key": "keeps_admin", "description": "Have access to all features and applications (only for Keeper's users)", "application": "ad7e5ad2-1552-43ab-a471-710954f0e66a", "status": true, "created_date": null, "updated_date": null}}, {"model": "common.role", "pk": "b995b041-4c9c-47a3-aa1f-d7b1394d0954", "fields": {"name": "Analytics Admin", "key": "basic_analytics_admin", "description": "Have access to Learn Analytics", "application": "c2928f23-a5a6-4f59-94a7-7e409cf1d4f4", "status": true, "created_date": null, "updated_date": null}}, {"model": "common.role", "pk": "4ddf7c3a-13ab-47a2-98fd-ab0b177ef823", "fields": {"name": "User Analytics", "key": "basic_analytics_user", "description": "Have access to own data in the Learn Analytics", "application": "c2928f23-a5a6-4f59-94a7-7e409cf1d4f4", "status": true, "created_date": null, "updated_date": null}}, {"model": "common.role", "pk": "4ddf7c3a-13ab-47a2-98fd-ab0b177ef823", "fields": {"name": "User Analytics", "key": "basic_analytics_user", "description": "Have access to own data in the Learn Analytics", "application": "c2928f23-a5a6-4f59-94a7-7e409cf1d4f4", "status": true, "created_date": null, "updated_date": null}}, {"model": "common.role", "pk": "3d010792-7119-4e14-bea3-5258a31f1ddc", "fields": {"name": "Smartzap Admin", "key": "admin", "description": "Have access admin users and courses", "application": "84d6715e-9b75-436d-ad44-b74c5a7f6729", "status": true, "created_date": null, "updated_date": null}}, {"model": "common.role", "pk": "08404086-5e4e-48c6-91d7-dbeb360c7205", "fields": {"name": "Smartzap User", "key": "user", "description": "Have access to smartzap view when enrolled", "application": "84d6715e-9b75-436d-ad44-b74c5a7f6729", "status": true, "created_date": null, "updated_date": null}}]