# -*- coding: utf-8 -*-

from rest_framework import serializers

from common.models.role import Role
from common.serializers.application_serializer import ApplicationSimpleSerializer


class RoleSimpleSerializer(serializers.ModelSerializer):
    application = ApplicationSimpleSerializer()

    class Meta:
        model = Role
        fields = ("id", "name", "key", "application")


class RoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Role
        fields = "__all__"
