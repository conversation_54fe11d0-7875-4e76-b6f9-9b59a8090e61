# -*- coding: utf-8 -*-

from rest_framework import serializers

from common.models import Company, Role, Service, UserRoleWorkspace, Workspace
from common.serializers.role_serializer import RoleSimpleSerializer


class UserRoleWorkspaceListSerializer(serializers.ModelSerializer):
    role = RoleSimpleSerializer()

    class Meta:
        model = UserRoleWorkspace
        fields = ("id", "role")
        depth = 1


class WorkspaceSimpleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Workspace
        fields = ("id", "name", "icon_url", "logo_url")


class WorkspaceInputSerializer(serializers.ModelSerializer):
    company = serializers.PrimaryKeyRelatedField(required=False, queryset=Company.objects.filter())

    class Meta:
        model = Workspace
        fields = "__all__"


class WorkspaceSerializer(serializers.ModelSerializer):
    roles = serializers.SerializerMethodField()

    def get_roles(self, obj):
        # Check if user_id exist
        if "user_id" in self.context["view"].kwargs:
            user = self.context["view"].kwargs["user_id"]
            roles = obj.userroleworkspace_set.filter(user_id=user)
            return UserRoleWorkspaceListSerializer(roles, many=True).data

        return {}

    class Meta:
        model = Workspace
        exclude = (
            "use_own_smtp",
            "smtp_host",
            "smtp_port",
            "smtp_secure",
            "smtp_auth_user",
            "smtp_auth_pass",
            "smtp_sender_email",
            "smtp_reject_unauthorized",
        )

        depth = 1


class WorkspaceApplicationSerializer(serializers.ModelSerializer):
    roles = serializers.SerializerMethodField()
    services = serializers.SerializerMethodField()

    def get_roles(self, obj):
        roles = []
        application_id = self.context["view"].kwargs["pk"]
        user_id = self.context["request"].user["sub"]
        app_roles = Role.objects.filter(application_id=str(application_id)).values_list("id")
        app_user_roles = obj.userroleworkspace_set.filter(role_id__in=app_roles, user_id=user_id)
        for _ in app_user_roles:
            roles.append({"id": _.role.id, "name": _.role.key})
        return roles

    def get_services(self, obj):
        services = []
        application_id = self.context["view"].kwargs["pk"]
        service_app = Service.objects.filter(application_id=str(application_id)).values_list("id")
        workspace_service = obj.serviceworkspace_set.filter(
            service_id__in=service_app, workspace_id=str(obj.id)
        ).order_by("service__name")
        for _ in workspace_service:
            services.append({"id": _.service.id, "name": _.service.name})
        return services

    class Meta:
        model = Workspace
        exclude = (
            "use_own_smtp",
            "smtp_host",
            "smtp_port",
            "smtp_secure",
            "smtp_auth_user",
            "smtp_auth_pass",
            "smtp_sender_email",
            "smtp_reject_unauthorized",
        )
        depth = 1
