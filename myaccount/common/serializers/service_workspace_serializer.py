from rest_framework import serializers

from common.models import ServiceWorkspace
from common.serializers.service_serializer import ServiceSimpleSerializer
from common.serializers.workspace_serializer import WorkspaceSimpleSerializer


class ServiceWorkspaceListSerializer(serializers.ModelSerializer):
    service = ServiceSimpleSerializer()

    class Meta:
        model = ServiceWorkspace
        fields = ("id", "service", "status")
        depth = 1


class ServiceWorkspacePostSerializer(serializers.ModelSerializer):
    workspace = WorkspaceSimpleSerializer()

    class Meta:
        model = ServiceWorkspace
        fields = "__all__"
        depth = 1
