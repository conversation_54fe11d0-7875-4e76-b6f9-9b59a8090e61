from django.core.exceptions import ObjectDoesNotExist
from rest_framework import serializers
from rest_framework.exceptions import ValidationError

from common.models import LanguagePreference, UserProfileWorkspace
from common.models.user import User
from common.models.user_role_workspace import UserRoleWorkspace
from common.serializers.role_serializer import RoleSimpleSerializer
from common.serializers.workspace_serializer import WorkspaceSimpleSerializer


class UserRoleWorkspaceSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserRoleWorkspace
        fields = ("user", "role", "workspace", "self_sign_up")


class UserRoleWorkspaceListSerializer(serializers.ModelSerializer):
    workspace = WorkspaceSimpleSerializer()
    role = RoleSimpleSerializer()

    class Meta:
        model = UserRoleWorkspace
        fields = ("id", "role", "workspace", "self_sign_up")
        depth = 1


class UserSerializer(serializers.ModelSerializer):
    password = serializers.CharField(required=False, write_only=True)
    roles = serializers.SerializerMethodField()
    profiles = serializers.SerializerMethodField()
    created = serializers.ReadOnlyField()
    job = serializers.SerializerMethodField()
    job_id = serializers.SerializerMethodField()

    @staticmethod
    def get_roles(obj):
        roles = obj.userroleworkspace_set.all()
        return UserRoleWorkspaceListSerializer(roles, many=True).data

    @staticmethod
    def get_profiles(obj):
        profiles = obj.userprofileworkspace_set.all()
        profiles_data = UserProfileShortSerializer(profiles, many=True).data

        for profile, profile_data in zip(profiles, profiles_data):
            profile_data["job"] = profile.job

        return profiles_data

    def get_job(self, obj):
        profile = obj.userprofileworkspace_set.first()
        if profile and profile.job_position:
            return profile.job_position.name
        return None

    def get_job_id(self, obj):
        profile = obj.userprofileworkspace_set.first()
        if profile and profile.job_position:
            return profile.job_position.id
        return None

    class Meta:
        model = User
        fields = "__all__"
        depth = 1


class UserIdsSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ("id",)


class UserProfileShortSerializer(serializers.ModelSerializer):
    job = serializers.CharField(required=False, allow_null=True)

    class Meta:
        model = UserProfileWorkspace
        exclude = (
            "user",
            "workspace",
        )


class UserProfileSerializer(serializers.ModelSerializer):
    job = serializers.CharField(required=False, allow_null=True)

    class Meta:
        model = UserProfileWorkspace
        fields = "__all__"


class UserProfileListSerializer(serializers.ModelSerializer):
    job = serializers.CharField(required=False, allow_null=True)
    workspace = WorkspaceSimpleSerializer()

    class Meta:
        model = UserProfileWorkspace
        fields = "__all__"
        depth = 1


class UserInputSerializer(serializers.ModelSerializer):
    password = serializers.CharField(required=False, write_only=True)
    related_user_leader_email = serializers.EmailField(required=False)
    temporary_password = serializers.BooleanField(required=False, default=True)

    def to_internal_value(self, data: dict) -> dict:
        if data.get("birthday") == "":
            data["birthday"] = None
        data = self._normalize_related_user_leader(data)
        return super().to_internal_value(data)

    @staticmethod
    def _normalize_related_user_leader(initial_data: dict) -> dict:
        related_user_leader_email = initial_data.get("related_user_leader_email")
        related_user_leader = initial_data.get("related_user_leader")
        if not related_user_leader_email:
            return initial_data

        if related_user_leader:
            raise ValidationError("Inform related 'related_user_leader_email' or 'related_user_leader' never both")
        try:
            leader = User.objects.get(email=related_user_leader_email)
        except ObjectDoesNotExist as exc:
            raise ValidationError("Related_user_leader not found") from exc
        initial_data["related_user_leader"] = leader.id
        return initial_data

    class Meta:
        model = User
        fields = "__all__"


class LanguageSimpleSerializer(serializers.ModelSerializer):
    class Meta:
        model = LanguagePreference
        fields = ("id", "name", "status")


class PublicProfileSerializer(serializers.ModelSerializer):
    language = LanguageSimpleSerializer()

    class Meta:
        model = User
        fields = ("id", "name", "email", "phone", "ein", "country", "language", "avatar")


class UserRoleWorkspaceListFullSerializer(serializers.ModelSerializer):
    user = PublicProfileSerializer()
    workspace = WorkspaceSimpleSerializer()
    role = RoleSimpleSerializer()

    class Meta:
        model = UserRoleWorkspace
        fields = ("id", "user", "role", "workspace")
        depth = 1


class WorkspaceUserImportSerializer(serializers.Serializer):
    file = serializers.FileField(required=True)
    roles = serializers.ListField(required=True)
    temporary_password = serializers.BooleanField(default=False)

    def to_internal_value(self, data: dict) -> dict:
        if data.get("roles"):
            data["roles"] = data["roles"].split(",")
        return super().to_internal_value(data)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass
