import re

from rest_framework import serializers
from validate_docbr import CPF


def validate_cpf(value: str):
    """
    This method removes non-numeric characters, ensures the CPF has 11 digits,
    and validates its correctness. If the CPF is invalid, it raises a validation error.
    """
    value = str(value)

    # Remove non-numeric characters
    cpf = re.sub(r"\D", "", value)

    # Ensure CPF has 11 digits, adding leading zeros if necessary
    cpf = cpf.zfill(11)

    cpf_obj = CPF()

    if cpf_obj.validate(cpf):
        return cpf

    raise serializers.ValidationError("Invalid CPF")
