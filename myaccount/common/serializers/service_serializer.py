# -*- coding: utf-8 -*-

from rest_framework import serializers

from common.models.service import Service
from common.serializers.application_serializer import ApplicationSimpleSerializer


class ServiceSerializer(serializers.ModelSerializer):
    application = ApplicationSimpleSerializer()

    class Meta:
        model = Service
        fields = "__all__"
        depth = 1


class ServiceSimpleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Service
        fields = ("id", "name")
        depth = 1
