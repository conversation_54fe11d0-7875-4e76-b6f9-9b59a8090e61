import re

from django.core.exceptions import ObjectDoesNotExist
from rest_framework import serializers

from common.models import Job, User
from common.serializers.user_serializer import UserProfileShortSerializer, UserSerializer
from common.serializers.validations.cpf import validate_cpf


class WorkspaceUserSerializer(serializers.Serializer):
    users = serializers.ListField(child=UserSerializer())
    permissions = serializers.ListField(child=serializers.UUIDField())

    def update(self, instance, validated_data):
        super().update(instance, validated_data)

    def create(self, validated_data):
        super().create(validated_data)


class IdentityProviderSerializer(serializers.Serializer):
    alias = serializers.CharField(allow_blank=True, required=True)

    def update(self, instance, validated_data):
        super().update(instance, validated_data)

    def create(self, validated_data):
        super().create(validated_data)


class UserInputSerializer(serializers.ModelSerializer):
    password = serializers.CharField(required=False)
    email = serializers.EmailField()
    name = serializers.CharField()
    cpf = serializers.CharField(validators=[validate_cpf], required=False, allow_blank=True, allow_null=True)
    identity_provider = IdentityProviderSerializer(required=False)
    profile = UserProfileShortSerializer(required=False)
    job = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    temporary_password = serializers.BooleanField(required=False, default=True)

    def to_internal_value(self, data):
        if data.get("birthday") == "":
            data["birthday"] = None
        if data.get("related_user_leader"):
            data["related_user_leader"] = self._normalize_related_user_leader(data["related_user_leader"])
        data = self._normalize_foreign_keys(data)
        return super().to_internal_value(data)

    @staticmethod
    def _normalize_foreign_keys(data):
        foreign_key = ["related_user_leader_id", "language_id"]
        for key in foreign_key:
            if key in data:
                new_key = key.replace("_id", "")
                data[new_key] = data.pop(key)
        return data

    def is_valid(self, raise_exception=False):
        identity_provider = self.initial_data.get("identity_provider")
        if identity_provider:
            serializer = IdentityProviderSerializer(data=identity_provider)
            serializer.is_valid(raise_exception)
        super().is_valid(True)

    @staticmethod
    def _normalize_related_user_leader(related_user_leader):
        if "@" not in related_user_leader:
            return related_user_leader
        leader = User.objects.filter(email=related_user_leader).first()
        if leader:
            return leader.id
        return None

    @staticmethod
    def normalize_name_email(attrs):
        if "name" in attrs:
            attrs["name"] = attrs["name"].strip()
        if "email" in attrs:
            attrs["email"] = attrs["email"].lower().strip()

    @staticmethod
    def set_job_in_profile(attrs):
        if "job" in attrs:
            if "profile" not in attrs:
                attrs["profile"] = {}
            attrs["profile"].setdefault("job", attrs["job"])
            attrs.pop("job")
        return attrs

    @staticmethod
    def get_job_from_db(attrs, workspace_id):
        job_name = attrs["profile"].pop("job", None)
        if not job_name:
            return attrs
        try:
            job, _ = Job.objects.get_or_create(name=job_name.strip().upper(), workspace_id=workspace_id)
            attrs["profile"]["job_position"] = job
        except ObjectDoesNotExist:
            attrs["profile"]["job"] = None
        return attrs

    def validate(self, attrs):
        self.normalize_name_email(attrs)
        workspace_id = self.context.get("workspace_id")

        if "cpf" in attrs and attrs["cpf"]:
            attrs["cpf"] = re.sub(r"\D", "", attrs["cpf"])

        if workspace_id:
            return self.job_check(attrs)
        if "job" in attrs:
            attrs.pop("job")
        return attrs

    def job_check(self, attrs):
        attrs = self.set_job_in_profile(attrs)
        return attrs

    class Meta:
        model = User
        fields = "__all__"


class WorkspaceUserInputSerializer(WorkspaceUserSerializer):
    users = serializers.ListField(child=UserInputSerializer())
    permissions = serializers.ListField(child=serializers.UUIDField())


class WorkspaceUserImportOutput(serializers.Serializer):
    errors = serializers.ListField(child=UserInputSerializer())
    imported = serializers.ListField(child=UserInputSerializer())

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass
