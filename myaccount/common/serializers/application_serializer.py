# -*- coding: utf-8 -*-

from rest_framework import serializers

from common.models import Role, Service

from ..models.application import Application


class RoleSimpleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Role
        fields = ("id", "name", "key", "application")


class ServiceSimpleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Service
        fields = ("id", "name")
        depth = 1


class ApplicationSimpleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Application
        fields = ("id", "name")


class ApplicationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Application
        fields = "__all__"
        depth = 1


class ApplicationRoleSerializer(serializers.ModelSerializer):
    roles = serializers.SerializerMethodField()

    def get_roles(self, obj):
        roles = obj.role_set.all()
        return RoleSimpleSerializer(roles, many=True).data

    class Meta:
        model = Application
        fields = "__all__"
        depth = 1


class ApplicationServiceSerializer(serializers.ModelSerializer):
    services = serializers.SerializerMethodField()

    def get_services(self, obj):
        roles = obj.service_set.all()
        return ServiceSimpleSerializer(roles, many=True).data

    class Meta:
        model = Application
        fields = "__all__"
        depth = 1
