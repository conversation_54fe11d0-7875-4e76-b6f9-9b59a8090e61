from rest_framework import serializers

from common.models import Company, Workspace
from common.serializers.workspace_serializer import WorkspaceSimpleSerializer


class CompanySimpleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Workspace
        fields = (
            "id",
            "name",
            "icon_url",
            "company_id",
        )


class CompanySerializer(serializers.ModelSerializer):
    workspace = WorkspaceSimpleSerializer()

    class Meta:
        model = Company
        fields = "__all__"
        depth = 1
