import uuid

from django.db import models

from common.models.workspace import Workspace


# Relaciona IDP com Workspace, usado pelo serviço de User Registration
class IdpWorkspace(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    idp = models.CharField(verbose_name="Identity Provider", max_length=200, null=False, blank=False)
    workspace = models.ForeignKey(Workspace, on_delete=models.CASCADE)

    class Meta:
        app_label = "common"
        db_table = "idp_workspace"
        unique_together = ("idp", "workspace")
