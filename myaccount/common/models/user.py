import uuid

from django.core.exceptions import ValidationError
from django.core.validators import RegexValidator
from django.db import models

from common.models import LanguagePreference

GENDER_OPTIONS = (("FEMALE", "FEMALE"), ("<PERSON>LE", "MALE"), ("<PERSON><PERSON><PERSON>", "OTHER"))


class User(models.Model):
    # Name and nickname
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(verbose_name="Name", max_length=200, null=True, blank=True)
    nickname = models.CharField(verbose_name="Nickname", max_length=200, null=True, blank=True)

    # Contact
    email = models.EmailField(
        verbose_name="Em<PERSON>", max_length=200, null=False, blank=False, unique=True, editable=False
    )
    email_verified = models.BooleanField(verbose_name="Email Verified", default=False)
    secondary_email = models.EmailField(verbose_name="Secondary Email", max_length=200, null=True, blank=True)
    phone_regex = RegexValidator(
        regex=r"^\+?1?\d{9,15}$",
        message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.",
    )
    phone = models.CharField(verbose_name="Phone", validators=[phone_regex], max_length=15, null=True, blank=True)

    # Personal Information
    avatar = models.TextField(verbose_name="Avatar", null=True, blank=True)
    gender = models.CharField(verbose_name="Gender", max_length=200, null=True, blank=True, choices=GENDER_OPTIONS)
    birthday = models.DateField(verbose_name="Birthday", null=True, blank=True)
    address = models.TextField(verbose_name="Address", null=True, blank=True)
    country = models.CharField(verbose_name="Country", max_length=90, null=True, blank=True)
    language = models.ForeignKey(LanguagePreference, verbose_name="Language", null=True, on_delete=models.SET_NULL)
    ein = models.CharField(verbose_name="Employer Identification Number", max_length=100, null=True, blank=True)
    related_user_leader = models.ForeignKey(
        "self", verbose_name="Related User Leader", null=True, on_delete=models.SET_NULL
    )
    cpf = models.CharField(verbose_name="CPF", max_length=15, null=True, blank=True)
    admission_date = models.DateField(verbose_name="Admission Date", null=True, blank=True)
    ethnicity = models.CharField(verbose_name="Ethnicity", max_length=100, null=True, blank=True)
    marital_status = models.CharField(verbose_name="Marital Status", max_length=100, null=True, blank=True)
    education = models.CharField(verbose_name="Education Level", max_length=100, null=True, blank=True)
    hierarchical_level = models.CharField(verbose_name="Hierarchical Level", max_length=100, null=True, blank=True)
    contract_type = models.CharField(verbose_name="Contract Type", max_length=100, null=True, blank=True)
    is_user_integration = models.BooleanField(
        verbose_name="Is User Integration", null=False, blank=False, default=False
    )

    status = models.BooleanField(verbose_name="Status", default=True)

    time_zone = models.CharField(verbose_name="Time Zone", max_length=200, default="america/sao_paulo")

    created_date = models.DateTimeField(verbose_name="Created Date", auto_now_add=True, null=True, blank=True)
    updated_date = models.DateTimeField(verbose_name="Updated Date", auto_now=True, null=True, blank=True)

    def save(self, *args, **kwargs):
        if self.phone:
            self.phone = self.phone.replace("+", "")
        if self.gender:
            self.gender = self.gender.strip().upper()
        if self.cpf:
            self.cpf = self.cpf.strip()
        if self.ethnicity:
            self.ethnicity = self.ethnicity.strip().upper()
        if self.marital_status:
            self.marital_status = self.marital_status.strip().upper()
        if self.education:
            self.education = self.education.strip().upper()
        if self.hierarchical_level:
            self.hierarchical_level = self.hierarchical_level.strip().upper()
        if self.contract_type:
            self.contract_type = self.contract_type.strip().upper()
        super().save(*args, **kwargs)

    class Meta:
        app_label = "common"
        verbose_name_plural = "Users"
        db_table = "user"

    def __str__(self):
        short_id = str(self.id).split("-")[0]

        return f"{short_id} - {self.name}"

    def update(self, **kwargs):
        for name, values in kwargs.items():
            try:
                setattr(self, name, values)
            except KeyError as exception:
                raise ValidationError(f"Field {name} does not exist") from exception

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._created: bool = False

    @property
    def created(self) -> bool:
        return self._created

    @created.setter
    def created(self, created: bool):
        self._created = created
