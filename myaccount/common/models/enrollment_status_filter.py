import uuid

from django.db import models


class EnrollmentStatus<PERSON>ilter(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    status_code = models.CharField(max_length=50, unique=True)
    status_name = models.CharField(max_length=100)

    def __str__(self):
        return self.status_name

    class Meta:
        db_table = "enrollment_status_filters"
        verbose_name = "Enrollment Status Filter"
        verbose_name_plural = "Enrollment Status Filters"
        ordering = ["status_name"]
