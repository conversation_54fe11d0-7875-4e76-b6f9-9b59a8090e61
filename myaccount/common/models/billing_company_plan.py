import uuid

from django.db import models

from common.models import Application


class BillingCompanyPlan(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    application = models.ForeignKey(Application, verbose_name="Application", on_delete=models.PROTECT)
    company = models.ForeignKey("company", verbose_name="Company", on_delete=models.PROTECT)

    current_plan = models.IntegerField(verbose_name="Current monthly plan", null=False, blank=False, default=50)

    created_date = models.DateTimeField(verbose_name="Created Date", auto_now_add=True)
    updated_date = models.DateTimeField(verbose_name="Updated Date", auto_now=True)

    class Meta:
        app_label = "common"
        verbose_name_plural = "Company application plan"
        db_table = "company_billing_plan"
        unique_together = ("application", "company")
