import uuid

from django.db import models

from common.models.base_model import BaseModel


class UserIntegrationMessageHash(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    hash = models.TextField(verbose_name="hash")
    user_email = models.TextField(verbose_name="user email")
    workspace_id = models.UUIDField(verbose_name="workspace_id")

    class Meta:
        app_label = "common"
        verbose_name_plural = "User Integration Message Hashes"
        db_table = "user_integration_message_hash"
