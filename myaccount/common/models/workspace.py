import uuid

from django.core.validators import MaxV<PERSON>ueValidator, MinValueValidator
from django.db import models

from common.models import Company
from common.models.base_model import BaseModel
from constants import BANNER_MODE_CHOICES


class Workspace(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(verbose_name="Name", max_length=200, null=False, blank=False)
    duns_number = models.CharField(verbose_name="DUNS Number", max_length=200, null=True, blank=True)
    doc_number = models.CharField(verbose_name="DOCS Number", max_length=200, null=True, blank=True)
    description = models.TextField(verbose_name="Description", null=True, blank=True)
    status = models.BooleanField(verbose_name="Status", default=True)
    hash_id = models.CharField(max_length=125, unique=True, null=True, blank=True)

    address = models.TextField(verbose_name="Address", null=True, blank=True)
    city = models.CharField(verbose_name="City", max_length=200, null=True, blank=True)
    state = models.Char<PERSON>ield(verbose_name="State", max_length=200, null=True, blank=True)
    post_code = models.CharField(verbose_name="Post Code", max_length=200, null=True, blank=True)
    country = models.CharField(verbose_name="Country", max_length=200, null=True, blank=True)

    icon_url = models.TextField(verbose_name="Icon URL", null=True, blank=True)
    icon_svg_url = models.TextField(verbose_name="Logo SVG URL", null=True, blank=True)
    logo_url = models.TextField(verbose_name="Logo URL", null=True, blank=True)
    theme_id = models.TextField(verbose_name="Theme Id", null=True, blank=True)
    theme_dark = models.BooleanField(verbose_name="Theme Dark Active", default=False)

    use_own_smtp = models.BooleanField(verbose_name="Use own smtp", default=False)
    enable_email_notifications = models.BooleanField(verbose_name="Enable email notifications", default=True)
    smtp_host = models.CharField(verbose_name="Smtp Host", max_length=400, null=True, blank=True)
    smtp_port = models.IntegerField(verbose_name="Smtp Port", null=True, blank=True)
    smtp_secure = models.BooleanField(verbose_name="Smtp Secure", null=True, blank=True, default=False)
    smtp_auth_user = models.CharField(verbose_name="Smtp Auth User", max_length=400, null=True, blank=True)
    smtp_auth_pass = models.CharField(verbose_name="Smtp Auth User", max_length=1000, null=True, blank=True)
    smtp_sender_email = models.CharField(verbose_name="Smtp Sender Email", max_length=400, null=True, blank=True)
    smtp_reject_unauthorized = models.BooleanField(
        verbose_name="Smtp Reject Unauthorized", null=True, blank=True, default=True
    )

    company = models.ForeignKey(Company, verbose_name="Workspace's company", on_delete=models.PROTECT)

    created_date = models.DateTimeField(verbose_name="Created Date", auto_now_add=True, null=True, blank=True)
    updated_date = models.DateTimeField(verbose_name="Updated Date", auto_now=True, null=True, blank=True)
    logout_url = models.TextField(verbose_name="Logout URL", null=True, blank=True)
    custom_color = models.CharField(verbose_name="Custom Color", max_length=16, null=True, blank=True)
    custom_login_url = models.TextField(verbose_name="Custom Login URL", null=True, blank=True)
    notify_slack = models.BooleanField(verbose_name="Notify Slack", default=False)
    notify_teams = models.BooleanField(verbose_name="Notify Teams", default=False)
    alura_integration_active = models.BooleanField(verbose_name="Alura Integration Active", default=False)

    #  Konquest Configuration
    allow_list_public_categories = models.BooleanField(verbose_name="Allow List Public Categories", default=True)

    allow_list_public_channel = models.BooleanField(verbose_name="Allow List Public Channel", default=False)
    allow_create_public_channel = models.BooleanField(verbose_name="Allow Create Public Channel", default=False)
    allow_list_paid_channel = models.BooleanField(verbose_name="Allow List Paid Channel", default=False)
    allow_create_paid_channel = models.BooleanField(verbose_name="Allow Create Paid Channel", default=False)
    need_approve_channel = models.BooleanField(verbose_name="Need Approve Channel", default=True)

    allow_list_public_mission = models.BooleanField(verbose_name="Allow List Public Mission", default=False)
    allow_create_public_mission = models.BooleanField(verbose_name="Allow Create Public Mission", default=False)
    allow_list_paid_mission = models.BooleanField(verbose_name="Allow List Paid Mission", default=False)
    allow_create_paid_mission = models.BooleanField(verbose_name="Allow Create Paid Mission", default=False)
    need_approve_mission = models.BooleanField(verbose_name="Need Approve Mission", default=True)

    enrollment_goal_duration_days = models.IntegerField(verbose_name="Enrollment Goal Duration", default=30)

    min_performance_certificate = models.FloatField(
        verbose_name="Minimum Performance Certificate",
        default=0,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
    )

    default_federated_identity_provider_alias = models.CharField(
        verbose_name="Default Federated Identity Provider Alias", max_length=100, null=True
    )
    block_reenrollment = models.BooleanField(verbose_name="Block re-enrollment", null=False, default=False)

    banner_mode = models.CharField(
        verbose_name="Banner Mode",
        max_length=20,
        choices=BANNER_MODE_CHOICES,
        default='RECOMMENDATION',
        null=False,
        blank=False
    )

    class Meta:
        app_label = "common"
        verbose_name_plural = "workspaces"
        db_table = "workspace"

    def __str__(self):
        short_id = str(self.id).split("-")[0]

        return f"{short_id} - {self.name}"
