import uuid

from django.db import models


class LanguagePreference(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(verbose_name="Name", max_length=200, null=False, blank=False)
    status = models.BooleanField(verbose_name="Status", default=True)

    created_date = models.DateTimeField(verbose_name="Created Date", auto_now_add=True, null=True, blank=True)
    updated_date = models.DateTimeField(verbose_name="Updated Date", auto_now=True, null=True, blank=True)

    class Meta:
        app_label = "common"
        verbose_name_plural = "Language Preferences"
        db_table = "language_preference"

    def __str__(self):
        return self.name
