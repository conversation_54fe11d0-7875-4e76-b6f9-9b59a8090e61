import uuid

from django.db import models


class Company(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(verbose_name="Name", max_length=200, null=False, blank=False)

    description = models.TextField(verbose_name="Description", null=True, blank=True)
    status = models.BooleanField(verbose_name="Status", default=True)

    address = models.TextField(verbose_name="Address", null=True, blank=True)
    city = models.CharField(verbose_name="City", max_length=200, null=True, blank=True)
    state = models.CharField(verbose_name="State", max_length=200, null=True, blank=True)
    post_code = models.CharField(verbose_name="Post Code", max_length=200, null=True, blank=True)
    country = models.CharField(verbose_name="Country", max_length=200, null=True, blank=True)

    icon_url = models.TextField(verbose_name="Icon URL", null=True, blank=True)

    created_date = models.DateTimeField(verbose_name="Created Date", auto_now_add=True, editable=False)
    updated_date = models.DateTimeField(verbose_name="Updated Date", auto_now=True)

    class Meta:
        app_label = "common"
        verbose_name_plural = "companies"
        db_table = "company"

    def __str__(self):
        short_id = str(self.id).split("-")[0]

        return f"{short_id} - {self.name}"
