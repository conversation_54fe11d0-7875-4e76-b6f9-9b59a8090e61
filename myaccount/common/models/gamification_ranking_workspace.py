import uuid

from django.db import models
from notification.services.notification_service import NotificationService

from common.models.service import Service
from common.models.workspace import Workspace

RANKING_CHOICES = [
    ("general", "general ranking"),
    ("multiplier", "ranking by multiplier"),
    ("director", "ranking by director"),
    ("manager", "ranking by manager"),
    ("leader", "ranking by leader"),
    ("activity_area", "ranking by activity area"),
]


class GamificationRankingWorkspace(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    service = models.ForeignKey(Service, verbose_name="Service", on_delete=models.PROTECT)
    workspace = models.ForeignKey(Workspace, verbose_name="Company", on_delete=models.PROTECT)
    ranking = models.CharField(verbose_name="Ranking", choices=RANKING_CHOICES, max_length=30)
    status = models.BooleanField(verbose_name="Status", default=True)
    created_date = models.DateTimeField(verbose_name="Created Date", auto_now_add=True, null=True, blank=True)
    updated_date = models.DateTimeField(verbose_name="Updated Date", auto_now=True, null=True, blank=True)

    @property
    def can_enable(self):
        if self.ranking == "director" and not (self.director_count >= self.users_count * 0.7):
            return False
        elif self.ranking == "manager" and not (self.manager_count >= self.users_count * 0.7):
            return False
        elif self.ranking == "leader" and not (self.leader_count >= self.users_count * 0.7):
            return False
        elif self.ranking == "activity_area" and not (self.activity_area_count >= self.users_count * 0.7):
            return False
        return True

    def disable_ranking(self, notify=False):
        self.status = False
        self.save()
        if notify:
            notification_service = NotificationService()
            notification_service.notify_gamification_ranking_workspace_disabled(self)

    class Meta:
        app_label = "common"
        verbose_name_plural = "Gamification Ranking Workspace"
        db_table = "gamification_ranking_workspace"
        unique_together = ("service", "workspace", "ranking")

    def __str__(self):
        return f"{self.workspace.name} {self.service} {self.ranking}"
