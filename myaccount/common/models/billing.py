import uuid

from django.db import models

from common.models import Application


class Billing(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    company = models.ForeignKey("company", verbose_name="Company", on_delete=models.PROTECT)
    application = models.ForeignKey(Application, verbose_name="Application", on_delete=models.PROTECT)

    monthly_plan = models.IntegerField(verbose_name="Current monthly plan", null=False, blank=False, default=50)

    used = models.IntegerField(verbose_name="Used credits", null=False, blank=False, default=0)
    balance = models.IntegerField(verbose_name="Final balance", null=False, blank=False, default=0)

    start_date = models.DateTimeField(verbose_name="Start Date Billing")
    end_date = models.DateTimeField(verbose_name="End Date Billing")

    created_date = models.DateTimeField(verbose_name="Billing created date", auto_now_add=True)

    class Meta:
        app_label = "common"
        verbose_name_plural = "Billing"
        db_table = "billing"
