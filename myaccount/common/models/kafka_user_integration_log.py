import uuid

from django.db import models


class KafkaUserIntegrationLog(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    title = models.TextField(verbose_name="Title", null=True, blank=True)
    description = models.TextField(verbose_name="Description", null=True, blank=True)
    message_read = models.TextField(verbose_name="Message Read", null=True, blank=True)
    workspace_id = models.UUIDField(verbose_name="Workspace Id", null=True, blank=True)
    batch_name = models.CharField(verbose_name="Batch Name", max_length=500, null=True, blank=True)

    is_error = models.BooleanField(verbose_name="Is Error", default=False)

    created_date = models.DateTimeField(verbose_name="Created Date", auto_now_add=True)

    class Meta:
        app_label = "common"
        verbose_name_plural = "Kafka User Integration Log"
        db_table = "kafka_user_integration_log"

    def __str__(self):
        return self.title
