# -*- coding: utf-8 -*-

import uuid

from django.db import models

from common.models.application import Application


class Role(models.Model):
    """
    player	Kizup
    content	<PERSON><PERSON><PERSON>
    admin	Kizup
    account admin	My Account
    company admin	My Account
    keeps admin	My Account
    user	Konquest
    content	Konquest
    curator	Konquest
    admin	Konquest
    super admin	Konquest
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(verbose_name="Name", max_length=200, null=True, blank=True)
    key = models.CharField(verbose_name="Key", max_length=200, null=False, blank=False)
    description = models.TextField(verbose_name="Description", null=True, blank=True)
    application = models.ForeignKey(Application, on_delete=models.CASCADE)

    status = models.BooleanField(verbose_name="Status", default=True)

    created_date = models.DateTimeField(verbose_name="Created Date", auto_now_add=True, null=True, blank=True)
    updated_date = models.DateTimeField(verbose_name="Updated Date", auto_now=True, null=True, blank=True)

    class Meta:
        app_label = "common"
        verbose_name_plural = "roles"
        db_table = "role"

    def __str__(self):
        return self.name
