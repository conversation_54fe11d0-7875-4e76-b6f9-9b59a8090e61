import uuid

from django.db import models

from common.models.workspace import Workspace


class Job(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(verbose_name="Job", max_length=200, null=True, blank=True)
    workspace = models.ForeignKey(Workspace, verbose_name="Workspace", null=False, on_delete=models.CASCADE)
    created_date = models.DateTimeField(verbose_name="Created Date", auto_now_add=True, null=True, blank=True)
    updated_date = models.DateTimeField(verbose_name="Updated Date", auto_now=True, null=True, blank=True)

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if self.name:
            self.name = self.name.upper()
        super(Job, self).save(*args, **kwargs)

    class Meta:
        unique_together = ("name", "workspace")
        app_label = "common"
        verbose_name_plural = "Jobs"
        db_table = "job"


class JobFunction(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(verbose_name="Job Function", max_length=200, null=True, blank=True)
    workspace = models.ForeignKey(Workspace, verbose_name="Workspace", null=False, on_delete=models.CASCADE)
    created_date = models.DateTimeField(verbose_name="Created Date", auto_now_add=True, null=True, blank=True)
    updated_date = models.DateTimeField(verbose_name="Updated Date", auto_now=True, null=True, blank=True)

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if self.name:
            self.name = self.name.upper().strip()
        super().save(*args, **kwargs)

    class Meta:
        unique_together = ("name", "workspace")
        app_label = "common"
        verbose_name_plural = "Job Functions"
        db_table = "job_function"
