import uuid

from django.db import models

from common.models.role import Role
from common.models.user import User
from common.models.workspace import Workspace


class UserRoleWorkspace(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    role = models.ForeignKey(Role, on_delete=models.CASCADE)
    workspace = models.ForeignKey(Workspace, on_delete=models.CASCADE)

    status = models.BooleanField(verbose_name="Status", default=True)
    self_sign_up = models.BooleanField(verbose_name="Self Sign Up", default=False)

    created_date = models.DateTimeField(verbose_name="Created Date", auto_now_add=True, null=True, blank=True)
    updated_date = models.DateTimeField(verbose_name="Updated Date", auto_now=True, null=True, blank=True)

    class Meta:
        app_label = "common"
        verbose_name_plural = "Users profile workspace"
        unique_together = ("user", "role", "workspace")
        db_table = "user_role_workspace"
