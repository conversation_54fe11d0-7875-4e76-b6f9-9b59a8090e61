from django.db import models
from uuid import uuid4

class WorkspaceCustomMenuItem(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    name = models.CharField(max_length=100)
    icon = models.CharField(max_length=100)
    url = models.CharField(max_length=500)
    workspace = models.ForeignKey(
        'Workspace', 
        on_delete=models.CASCADE,
        related_name='custom_menu_items',
        db_column='workspace_id'
    )
    created_date = models.DateTimeField(auto_now_add=True)
    updated_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'workspace_custom_menu_item'
    def __str__(self):
        return self.name