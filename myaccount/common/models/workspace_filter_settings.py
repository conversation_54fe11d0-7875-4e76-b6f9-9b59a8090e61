import uuid

from django.db import models


class WorkspaceFilterSetting(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE)
    enrollment_status_filter = models.ForeignKey("EnrollmentStatusFilter", on_delete=models.CASCADE)
    is_enabled = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.workspace.name} - {self.filter.status_name} ({'Enabled' if self.is_enabled else 'Disabled'})"

    class Meta:
        db_table = "workspace_filter_settings"
        verbose_name = "Workspace Filter Setting"
        verbose_name_plural = "Workspace Filter Settings"
        unique_together = ("workspace", "enrollment_status_filter")
