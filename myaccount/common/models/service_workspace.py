import uuid

from django.db import models

from common.models.service import Service
from common.models.workspace import Workspace


class ServiceWorkspace(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    service = models.ForeignKey(Service, verbose_name="Service", on_delete=models.CASCADE)
    workspace = models.ForeignKey(Workspace, verbose_name="Company", on_delete=models.CASCADE)

    status = models.BooleanField(verbose_name="Status", default=True)
    custom_url = models.URLField(verbose_name="Custom URL", max_length=500, null=True, blank=True)

    created_date = models.DateTimeField(verbose_name="Created Date", auto_now_add=True, null=True, blank=True)
    updated_date = models.DateTimeField(verbose_name="Updated Date", auto_now=True, null=True, blank=True)

    class Meta:
        app_label = "common"
        verbose_name_plural = "Workspace services"
        db_table = "service_workspace"
        unique_together = ("service", "workspace")

    def __str__(self):
        return f"{self.workspace.name} {self.service}"
