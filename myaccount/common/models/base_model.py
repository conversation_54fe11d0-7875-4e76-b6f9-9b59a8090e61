from django.core.exceptions import ValidationError
from django.db import models


class BaseModel(models.Model):
    created_date = models.DateTimeField(verbose_name="Created Date", auto_now_add=True, editable=False, null=True)
    updated_date = models.DateTimeField(verbose_name="Updated Date", auto_now=True)

    def update(self, **kwargs):
        for name, values in kwargs.items():
            try:
                setattr(self, name, values)
            except KeyError as exception:
                raise ValidationError(f"Field {name} does not exist") from exception

    class Meta:
        abstract = True
