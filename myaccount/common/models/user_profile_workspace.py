import uuid

from django.db import models

from common.models.job import Job, JobFunction
from common.models.user import User
from common.models.workspace import Workspace


class UserProfileWorkspace(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
    )
    workspace = models.ForeignKey(Workspace, on_delete=models.CASCADE)
    job_position = models.ForeignKey(Job, verbose_name="Job", null=True, on_delete=models.SET_NULL)
    job_function = models.ForeignKey(JobFunction, verbose_name="Job Function", null=True, on_delete=models.SET_NULL)

    director = models.Char<PERSON>ield(verbose_name="Director", max_length=200, null=True, blank=True)
    manager = models.CharField(verbose_name="Manager", max_length=200, null=True, blank=True)
    area_of_activity = models.CharField(verbose_name="Area of activity.", max_length=300, null=True, blank=True)

    created_date = models.DateTimeField(verbose_name="Created Date", auto_now_add=True, null=True, blank=True)
    updated_date = models.DateTimeField(verbose_name="Updated Date", auto_now=True, null=True, blank=True)

    @property
    def job(self):
        return self.job_position.name if self.job_position and self.job_position.name else ""

    @job.setter
    def job(self, job_name):
        if job_name:
            try:
                job_uuid = uuid.UUID(job_name)
                job = Job.objects.get(id=job_uuid)
            except ValueError:
                job, _ = Job.objects.get_or_create(name=job_name.strip().upper(), workspace=self.workspace)
            self.job_position = job

    class Meta:
        app_label = "common"
        verbose_name_plural = "Users Profiles Workspaces"
        unique_together = ("user", "workspace")
        db_table = "user_profile_workspace"
