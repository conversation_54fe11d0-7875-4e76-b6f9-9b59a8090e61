import django_filters
from django_filters import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, UUIDFilter

from common.models import User, UserRoleWorkspace


class UserFilter(django_filters.FilterSet):
    application_id = UUIDFilter(field_name="application_id", method="by_role_application")
    role_id = BaseInFilter(field_name="role_id", method="by_role")

    def by_role_application(self, queryset, name, value):
        roles = UserRoleWorkspace.objects.filter(
            user_id__in=queryset.values_list("id", flat=True),
            role__application__id=value,
        )
        if hasattr(self.request, "workspace_id"):
            roles = roles.filter(workspace_id=self.request.workspace_id)
        return queryset.filter(id__in=roles.values_list("user_id"))

    def by_role(self, queryset, name, value):
        roles = UserRoleWorkspace.objects.filter(user_id__in=queryset.values_list("id", flat=True), role_id__in=value)
        if hasattr(self.request, "workspace_id"):
            roles = roles.filter(workspace_id=self.request.workspace_id)
        return queryset.filter(id__in=roles.values_list("user_id"))

    class Meta:
        model = User
        fields = "__all__"
