from django_filters import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Char<PERSON>ilter
from django_filters.rest_framework import FilterSet

from common.models import UserRoleWorkspace


class UserRoleFilter(FilterSet):
    user__name = CharFilter(lookup_expr="icontains")
    user__email = CharFilter(lookup_expr="icontains")
    user__status = CharFilter(lookup_expr="icontains")
    workspace__id = CharFilter()
    role__id = CharFilter()
    role_id = BaseInFilter(field_name="role_id", method="filter_role")

    class Meta:
        model = UserRoleWorkspace
        fields = ["user__name", "user__email", "user__status", "workspace__id", "role__id"]

    def filter_role(self, queryset, name, value):
        return queryset.filter(role__id__in=value)
