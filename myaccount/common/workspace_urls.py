from django.urls import path

from common.views.gamification_workspace_viewset import GamificationWorkspaceViewSet
from common.views.images_viewset import WorkspaceIconSVGViewSet, WorkspaceIconViewSet, WorkspaceLogoViewSet
from common.views.service_workspace_viewset import ServiceWorkspaceViewSet
from common.views.user_profile_viewset import UserProfileViewSet
from common.views.workspace_filter_viewset import WorkspaceFilterSettingViewSet
from common.views.workspace_user_viewset import (
    WorkspaceSignUpViewSet,
    WorkspaceUserImportViewSet,
    WorkspaceUserPublicViewSet,
    WorkspaceUserSendInviteViewSet,
    WorkspaceUserViewSet,
)
from common.views.workspace_viewset import WorkspaceHashLoginUrlViewset, WorkspaceThemeViewSet, WorkspaceViewSet

SAVE_ONLY = {"post": "create"}
READ_ONLY = {"get": "list"}
UPDATE_ONLY = {"put": "update"}
LIST = {"get": "list", "post": "create"}
LIST_OR_UPDATE = {"get": "list", "put": "update"}
DETAIL = {"get": "retrieve", "put": "update", "patch": "partial_update", "delete": "destroy"}
CREATE_OR_DELETE = {"post": "create", "delete": "destroy"}


urlpatterns = [
    path("", WorkspaceViewSet.as_view(LIST), name="workspaces-list"),
    path(
        "/hash-login-url/<str:hash_id>",
        WorkspaceHashLoginUrlViewset.as_view({"get": "retrieve"}),
        name="workspace-hash-login-url",
    ),
    path("/<uuid:pk>", WorkspaceViewSet.as_view(DETAIL), name="workspace-detail"),
    path("/<uuid:pk>/theme", WorkspaceThemeViewSet.as_view(READ_ONLY), name="workspace-theme"),
    path("/<uuid:workspace_id>/users", WorkspaceUserViewSet.as_view(LIST), name="workspace-users-list"),
    path("/<uuid:workspace_id>/users/profile", UserProfileViewSet.as_view(LIST), name="workspace-users-profiles"),
    path(
        "/<uuid:workspace_id>/users/profile/<uuid:pk>",
        UserProfileViewSet.as_view(DETAIL),
        name="workspace-users-profile",
    ),
    path(
        "/<uuid:workspace_id>/users/public",
        WorkspaceUserPublicViewSet.as_view(READ_ONLY),
        name="workspace-users-public-list",
    ),
    path("/<uuid:workspace_id>/users/<uuid:pk>", WorkspaceUserViewSet.as_view(DETAIL), name="workspace-users-list"),
    path(
        "/<uuid:workspace_id>/users/invite",
        WorkspaceUserSendInviteViewSet.as_view(SAVE_ONLY),
        name="workspace-users-invite",
    ),
    path(
        "/<uuid:workspace_id>/users/sign-up", WorkspaceSignUpViewSet.as_view(SAVE_ONLY), name="workspace-users-sign-up"
    ),
    path("/<uuid:workspace_id>/services", ServiceWorkspaceViewSet.as_view(READ_ONLY), name="services-workspaces-list"),
    path(
        "/<uuid:workspace_id>/services/<uuid:service_id>",
        ServiceWorkspaceViewSet.as_view(CREATE_OR_DELETE),
        name="service-workspace-detail",
    ),
    path("/<uuid:workspace_id>/users/import", WorkspaceUserImportViewSet.as_view(SAVE_ONLY), name="user-import"),
    path("/<uuid:workspace_id>/logo", WorkspaceLogoViewSet.as_view(SAVE_ONLY), name="logo-create"),
    path("/<uuid:workspace_id>/icon-svg", WorkspaceIconSVGViewSet.as_view(SAVE_ONLY), name="logo-svg-create"),
    path("/<uuid:workspace_id>/icon", WorkspaceIconViewSet.as_view(SAVE_ONLY), name="icon-create"),
    path(
        "/gamification/ranking",
        GamificationWorkspaceViewSet.as_view(READ_ONLY),
        name="gamification-workspace-ranking-list",
    ),
    path(
        "/gamification/ranking/<uuid:ranking_id>",
        GamificationWorkspaceViewSet.as_view(UPDATE_ONLY),
        name="gamification-workspace-ranking-detail",
    ),
    path("/filter-settings", WorkspaceFilterSettingViewSet.as_view(READ_ONLY), name="filter-settings-list"),
    path(
        "/filter-settings/<uuid:pk>",
        WorkspaceFilterSettingViewSet.as_view({"patch": "partial_update"}),
        name="filter-settings-update",
    ),
]
