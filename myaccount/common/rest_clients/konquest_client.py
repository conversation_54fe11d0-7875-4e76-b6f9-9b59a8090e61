import requests
from django.conf import settings


class KonquestClient:
    def __init__(self):
        self._url = settings.KONQUEST_API_URL

    def enable_alura_integration(self, workspace_id: str) -> requests.Response:
        return requests.post(
            f"{self._url}workspaces/{workspace_id}/alura-integration/enable",
            headers={"x-client": workspace_id, "Authorization": f"{settings.KEEPS_SECRET_TOKEN_INTEGRATION}"},
        )

    def disable_alura_integration(self, workspace_id: str) -> requests.Response:
        return requests.post(
            f"{self._url}workspaces/{workspace_id}/alura-integration/disable",
            headers={"x-client": workspace_id, "Authorization": f"{settings.KEEPS_SECRET_TOKEN_INTEGRATION}"},
        )
