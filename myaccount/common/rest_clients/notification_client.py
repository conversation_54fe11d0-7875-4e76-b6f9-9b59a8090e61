import base64
from dataclasses import dataclass

import requests
from django.conf import settings

UTF_8 = "utf-8"


@dataclass
class MessagePayload:
    body: str
    receiver_mail: str
    subject: str
    workspace_id: str
    language: str


class NotificationClient:
    def __init__(self):
        self._url = settings.NOTIFICATION_API_URL
        self._url_create_email = self._url + "/emails/messages"

    def send_email(self, message: MessagePayload):
        body = message.body
        body_base64 = self.string_to_base64(body)
        requests.post(
            f"{self._url_create_email}",
            json={
                "compiledMessageBase64": body_base64,
                "receiverMail": message.receiver_mail,
                "subject": message.subject,
                "workspaceId": str(message.workspace_id),
                "language": message.language,
            },
        )

    @staticmethod
    def string_to_base64(body: str):
        body_bytes = body.encode(UTF_8)
        body_base64_bytes = base64.b64encode(body_bytes)
        body_base64 = body_base64_bytes.decode(UTF_8)
        return body_base64
