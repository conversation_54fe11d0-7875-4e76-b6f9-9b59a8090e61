from django.conf import settings
from kombu import Connection, Exchange, Producer, Queue


class PlatformIntegrationService:
    @staticmethod
    # pylint: disable=R1710
    def publish(integration, action, payload):
        """
        :param integration: List of integrations
        :param action: List of actions for each integration
        :param payload: data changed
        """

        if not settings.SYNC_PLATFORM_ENABLED:
            print("Integrations Service Disabled.")
            return

        allowed_actions = ["CREATE_OR_UPDATE", "DELETE_OR_DISABLE"]

        if action not in allowed_actions:
            print("Integrations Service Action Not Allowed.")
            return

        conn = Connection(settings.CELERY_BROKER_URL)
        producer = Producer(conn.channel())
        integration_queues = PlatformIntegrationService.set_integration_queues()

        data = {"action": action, "payload": payload}

        queue = integration_queues.get(str(integration))
        if not queue:
            raise RuntimeError(f"not found queue for the integration: {integration}")

        producer.publish(
            body=data,
            exchange=queue.exchange,
            routing_key=queue.routing_key,
            declare=[queue],
            retry=True,
            retry_policy={
                "interval_start": settings.CELERY_INTERVAL_START,
                "interval_step": settings.CELERY_INTERVAL_STEP,
                "interval_max": settings.CELERY_INTERVAL_MAX,
                "max_retries": settings.CELERY_MAX_RETRIES,
            },
        )
        return True

    @staticmethod
    def set_integration_queues():
        exchange_notification = Exchange(settings.NOTIFICATION_EXCHANGE)
        integration_queues = {
            "NOTIFICATION": Queue(
                settings.NOTIFICATION_MESSAGE_QUEUE,
                exchange_notification,
                routing_key=settings.BELL_NOTIFICATION_ROUTING_KEY,
            ),
        }
        return integration_queues
