from celery import shared_task
from config import settings
from utils.task_transaction import task_transaction

from tasks.platform_service import PlatformIntegrationService


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True)
def integration_task_publish(integration, action, payload):
    """
    :param integration: List of integrations
    :param action: List of actions for each integration
    :param payload: data changed
    """
    with task_transaction(integration_task_publish.__name__):
        integration_service = PlatformIntegrationService()
        integration_service.publish(integration, action, payload)
