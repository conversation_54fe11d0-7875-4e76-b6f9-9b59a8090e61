import uuid
from unittest import mock

from django.test import TestCase
from model_mommy import mommy

from common.models import Role, User, UserRoleWorkspace, Workspace
from config import settings
from tasks import notifications

INTEGRATION = "tasks.platform_integration_task.integration_task_publish.delay"
NOTIFY_USER = "tasks.notifications.notify_user.delay"


@mock.patch("common.rest_clients.notification_client.NotificationClient.send_email", return_value={})
@mock.patch("authentication.utils.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
@mock.patch("tasks.whatsapp.WhatsappClient.send_message", return_value={})
class NotificationTaskTestCase(TestCase):
    fixtures = ["application", "services", "language_preference", "roles"]

    def setUp(self):
        self.user = mommy.make(
            User,
            id=uuid.uuid4(),
            name="User Keeps",
            email="<EMAIL>",
            phone="5548999999999",
            email_verified=True,
        )
        self.workspace = mommy.make(Workspace, id=uuid.uuid4(), name="Keeps", logo_url="logo.com.br")

        self.data = {
            "company_id": str(self.workspace.id),
            "company_logo": "logo.com.br",
            "company": "Keeps",
            "user_email": "<EMAIL>",
            "user_login": "<EMAIL>",
            "user_name": "User Keeps",
            "user_phone": "5548999999999",
        }

    @mock.patch(NOTIFY_USER)
    @mock.patch(INTEGRATION)
    def test_notification_new_konquest_user(
        self, mock_return, mock_user_task, mock_send: mock.MagicMock, mock_integration, mock_notify
    ):
        """ """
        self.data["app_web_link"] = "http://konquest-stage.keepsdev.com/"
        konquest_user_role = Role.objects.get(key="user", application_id=settings.KONQUEST_ID)

        mommy.make(UserRoleWorkspace, user=self.user, workspace=self.workspace, role=konquest_user_role)

        response = notifications.notify_user(self.workspace.id, self.user.id, konquest_user_role.id, "1234")
        self.assertEqual(response[0]["company_logo"], self.data["company_logo"])
        self.assertEqual(response[0]["company"], self.data["company"])
        self.assertEqual(response[0]["user_email"], self.data["user_email"])
        self.assertEqual(response[0]["user_login"], self.data["user_login"])
        self.assertEqual(response[0]["user_name"], self.data["user_name"])
        self.assertEqual(response[0]["user_phone"], self.data["user_phone"])
        self.assertEqual(response[0]["app_web_link"], self.data["app_web_link"])
        self.assertEqual(response[1], "konquest_onboarding.html")
        self.assertEqual(response[2], "konquest_onboarding.txt")
        self.data["user_password"] = response[0]["user_password"]

        mock_send.assert_called()

    @mock.patch(NOTIFY_USER)
    @mock.patch(INTEGRATION)
    def test_not_notify_user_with_invalid_email(
        self, mock_return, mock_user_task, mock_send: mock.MagicMock, mock_integration, mock_notify
    ):
        """ """
        konquest_user_role = Role.objects.get(key="user", application_id=settings.KONQUEST_ID)
        user_with_invalid_email = mommy.make(User, email_verified=False, email="<EMAIL>")
        mommy.make(UserRoleWorkspace, user=user_with_invalid_email, workspace=self.workspace, role=konquest_user_role)

        notifications.notify_user(self.workspace.id, user_with_invalid_email.id, konquest_user_role.id)

        mock_send.assert_not_called()

    @mock.patch("utils.keycloak.user_manager.KeyCloakUserManager.set_user_password", return_value={})
    def test_resend_invite(self, mock_set_user_password, mock_user_task, get_token_info, mock_send):
        """ """
        notifications.resend_user_invite(self.workspace.id, self.user.id)

        mock_send.assert_called()
        mock_set_user_password.assert_called()

    @mock.patch(NOTIFY_USER)
    @mock.patch(INTEGRATION)
    def test_notification_new_smartzap_user(
        self, mock_return, mock_user_task, mock_resend, mock_integration, mock_notify
    ):
        """ """
        self.data["app_web_link"] = "http://smartzap-stage.keepsdev.com/"
        smartzap_admin_role = Role.objects.get(key="admin", application_id=settings.SMARTZAP_ID)

        mommy.make(UserRoleWorkspace, user=self.user, workspace=self.workspace, role=smartzap_admin_role)

        response = notifications.notify_user(self.workspace.id, self.user.id, smartzap_admin_role.id, "1234")

        self.assertEqual(response[0]["company_logo"], self.data["company_logo"])
        self.assertEqual(response[0]["company"], self.data["company"])
        self.assertEqual(response[0]["user_email"], self.data["user_email"])
        self.assertEqual(response[0]["user_login"], self.data["user_login"])
        self.assertEqual(response[0]["user_name"], self.data["user_name"])
        self.assertEqual(response[0]["user_phone"], self.data["user_phone"])
        self.assertEqual(response[0]["app_web_link"], self.data["app_web_link"])
        self.assertEqual(response[1], "smartzap_onboarding.html")
        self.assertEqual(response[2], None)
        self.data["user_password"] = response[0]["user_password"]


    @mock.patch(NOTIFY_USER)
    @mock.patch(INTEGRATION)
    def test_notification_user_already_exist_add_role_konquest(
        self, mock_return, mock_user_task, mock_resend, mock_integration, mock_notify
    ):
        self.data["app_web_link"] = "http://konquest-stage.keepsdev.com/"
        smartzap_admin_role = Role.objects.get(key="admin", application_id=settings.SMARTZAP_ID)
        konquest_user_role = Role.objects.get(key="user", application_id=settings.KONQUEST_ID)

        mommy.make(UserRoleWorkspace, user=self.user, workspace=self.workspace, role=smartzap_admin_role)
        mommy.make(UserRoleWorkspace, user=self.user, workspace=self.workspace, role=konquest_user_role)

        response = notifications.notify_user(self.workspace.id, self.user.id, konquest_user_role.id, new=False)

        self.assertEqual(response[0]["company_logo"], self.data["company_logo"])
        self.assertEqual(response[0]["company"], self.data["company"])
        self.assertEqual(response[0]["user_email"], self.data["user_email"])
        self.assertEqual(response[0]["user_login"], self.data["user_login"])
        self.assertEqual(response[0]["user_name"], self.data["user_name"])
        self.assertEqual(response[0]["user_phone"], self.data["user_phone"])
        self.assertEqual(response[0]["app_web_link"], self.data["app_web_link"])
        self.assertEqual(response[1], "konquest_invite.html")
        self.assertEqual(response[2], "konquest_invite.txt")

    @mock.patch(NOTIFY_USER)
    @mock.patch(INTEGRATION)
    def test_notification_user_already_exist_add_role_smartzap_user(
        self, mock_return, mock_user_task, mock_resend, mock_integration, mock_notify
    ):
        """ """
        self.data["app_web_link"] = "http://smartzap-stage.keepsdev.com/"
        konquest_user_role = Role.objects.get(key="user", application_id=settings.KONQUEST_ID)
        smartzap_admin_role = Role.objects.get(key="admin", application_id=settings.SMARTZAP_ID)

        mommy.make(UserRoleWorkspace, user=self.user, workspace=self.workspace, role=konquest_user_role)
        mommy.make(UserRoleWorkspace, user=self.user, workspace=self.workspace, role=smartzap_admin_role)

        response = notifications.notify_user(self.workspace.id, self.user.id, smartzap_admin_role.id, new=False)

        self.assertEqual(response[0]["company_logo"], self.data["company_logo"])
        self.assertEqual(response[0]["company"], self.data["company"])
        self.assertEqual(response[0]["user_email"], self.data["user_email"])
        self.assertEqual(response[0]["user_login"], self.data["user_login"])
        self.assertEqual(response[0]["user_name"], self.data["user_name"])
        self.assertEqual(response[0]["user_phone"], self.data["user_phone"])
        self.assertEqual(response[0]["app_web_link"], self.data["app_web_link"])
        self.assertEqual(response[1], "smartzap_invite.html")
        self.assertEqual(response[2], None)

