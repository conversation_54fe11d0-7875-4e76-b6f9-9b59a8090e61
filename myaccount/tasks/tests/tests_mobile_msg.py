import gettext
import os
from os import listdir
from string import Template

from jinja2 import Environment, FileSystemLoader

template_locale_dir = os.path.join(os.path.abspath(os.path.dirname(__file__)), "../../locales")
template_mobile_dir = os.path.join(os.path.abspath(os.path.dirname(__file__)), "../../assets/tpl_mobile")


class NotificationTaskTestCase:
    def __init__(self):
        pass

    def test_message_assembly(self):
        files = list(listdir(template_mobile_dir))
        languages = ["en", "es", "pt-br"]

        for f in files:
            for language in languages:
                print(f"\n\n#### {f} | {language} #### \n")

                locale = gettext.translation("notification", localedir=template_locale_dir, languages=[language])
                locale.install()
                _ = locale.gettext
                env = Environment(loader=FileSystemLoader(template_locale_dir), extensions=["jinja2.ext.i18n"])
                env.install_gettext_translations(locale)

                translate_key = {
                    "i18n_header_konquest": _("i18n_header_konquest"),
                    "i18n_access_header": _("i18n_access_header"),
                    "i18n_username": _("i18n_username"),
                    "i18n_password": _("i18n_password"),
                    "i18n_password_already_exist": _("i18n_password_already_exist"),
                    "i18n_download_header": _("i18n_download_header"),
                    "i18n_download_apple_label": _("i18n_download_apple_label"),
                    "i18n_download_android_label": _("i18n_download_android_label"),
                    "i18n_web_admin_label": _("i18n_web_admin_label"),
                    "i18n_footer_support": _("i18n_footer_support"),
                    "i18n_change_password_label": _("i18n_change_password_label"),
                    "i18n_header_resend_invite": _("i18n_header_resend_invite"),
                    "i18n_check_application": _("i18n_check_application"),
                    "i18n_header_smartzap": _("i18n_header_smartzap"),
                }

                with open(template_mobile_dir + "/" + f, encoding="utf-8") as file_data:
                    Template(file_data.read()).substitute(
                        company="KEEPS",
                        app_apple_link="http://apple.com",
                        app_android_link="http://android.com",
                        user_login="<EMAIL>",
                        app_url="http://account.keepsdev.com",
                        user_password="pass_1234",
                        **translate_key,
                    )
