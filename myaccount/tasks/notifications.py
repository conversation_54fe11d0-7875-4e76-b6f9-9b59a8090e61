import gettext
import os
import random
import string

from celery import shared_task
from common.models import Role, User, UserRoleWorkspace, Workspace
from common.rest_clients.notification_client import MessagePayload
from config import settings
from config.celery import app
from di import Container
from task_names import NOTIFY_USER_BY_ROLE, RESEND_USER_INVITE
from utils.task_transaction import task_transaction

from tasks.whatsapp import WhatsappClient

CONTAINER = Container()


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True)
def notify_user(workspace: str, user: str, role: str, password: string = None, new: bool = True):
    """
    Send user access using celery tasks
    """
    with task_transaction(notify_user.__name__):
        role_instance = Role.objects.get(id=role)
        workspace_instance = Workspace.objects.get(id=workspace)
        user_instance = User.objects.get(id=user)

        language = user_instance.language.name if user_instance.language else "pt-br"
        data = {
            "company_id": str(workspace_instance.id),
            "company_logo": workspace_instance.logo_url,
            "company": workspace_instance.name,
            "user_email": user_instance.email,
            "user_login": user_instance.email,
            "user_name": user_instance.name,
            "user_phone": user_instance.phone,
        }

        message_mappers = {
            str(settings.KONQUEST_ID): konquest_msg_send,
            str(settings.SMARTZAP_ID): smartzap_send_msg,
        }

        # if is new user, send onboarding email
        message_type = "invite"

        if new and password:
            data["user_password"] = password
            message_type = "onboarding"

        tpl_mail, tpl_mobile = message_mappers[str(role_instance.application_id)](
            data=data,
            language=language,
            msg_type=message_type,
            role_key=role_instance.key,
            workspace_id=workspace_instance.id,
        )

        return data, tpl_mail, tpl_mobile


def smartzap_send_msg(data, language, msg_type, role_key, workspace_id):
    if role_key == "user":
        return None, None

    subject = "smartzap_email_subject"

    email_template = f"smartzap_{msg_type}.html"

    data["app_web_link"] = settings.SMARTZAP_WEB_URL
    # email
    build_and_send_msg(data, language, subject, email_template, workspace_id)

    if not data["user_phone"]:
        return email_template, None

    # whatsapp
    whatsapp_client = WhatsappClient(CONTAINER)
    if msg_type == "invite":
        tpl_mobile = "MYACCOUNT_SMARTZAP_INVITE"
        tpl_mobile_data = {"1": data["company"], "2": data["user_login"], "3": data["app_web_link"]}
    else:
        tpl_mobile = "MYACCOUNT_SMARTZAP_ONBOARDING"
        tpl_mobile_data = {
            "1": data["company"],
            "2": data["user_login"],
            "3": data["user_password"],
            "4": data["app_web_link"],
        }

    whatsapp_client.send_message(data["user_phone"], template_key=tpl_mobile, language=language, data=tpl_mobile_data)

    return email_template, None


def konquest_msg_send(data, language, msg_type, role_key, workspace_id):
    subject = "konquest_email_subject"
    email_template = f"konquest_{msg_type}.html"
    mobile_template = f"konquest_{msg_type}.txt"

    data["app_web_link"] = settings.KONQUEST_WEB_URL

    build_and_send_msg(data, language, subject, email_template, workspace_id)

    if not data["user_phone"]:
        return email_template, None

    # whatsapp
    whatsapp_client = WhatsappClient(CONTAINER)
    if msg_type == "invite":
        tpl_mobile = "MYACCOUNT_KONQUEST_INVITE"
        tpl_mobile_data = {"1": data["company"], "2": data["user_login"], "3": data["app_web_link"]}
    else:
        tpl_mobile = "MYACCOUNT_KONQUEST_ONBOARDING"
        tpl_mobile_data = {
            "1": data["company"],
            "2": data["user_login"],
            "3": data["user_password"],
            "4": data["app_web_link"],
        }
    whatsapp_client.send_message(data["user_phone"], template_key=tpl_mobile, language=language, data=tpl_mobile_data)

    return email_template, mobile_template


@shared_task(name=RESEND_USER_INVITE, queue=settings.CELERY_QUEUE, ignore_result=True)
def resend_user_invite(workspace_id: str, user_id: str):
    """
    Re-send user access using celery tasks
    """
    my_acc_roles = Role.objects.filter(application_id=settings.MYACCOUNT_ID).values_list("id", flat=True)
    UserRoleWorkspace.objects.filter(user_id=user_id).exclude(role_id__in=my_acc_roles)
    workspace_instance = Workspace.objects.get(id=workspace_id)
    user = User.objects.get(id=user_id)
    subject = "konquest_email_subject"

    language = user.language.name if user.language else "pt-br"

    data = {
        "company_logo": workspace_instance.logo_url,
        "company": workspace_instance.name,
        "company_id": workspace_instance.id,
        "user_email": user.email,
        "user_login": user.email,
        "user_name": user.name,
        "user_password": password_generator(),
    }

    CONTAINER.keycloak_user_client.set_user_password(str(user.id), data["user_password"], True)
    data["app_web_link"] = settings.MYACCOUNT_WEB_URL
    tpl_mail = "resend_invite.html"

    if user.phone:
        tpl_mobile = "MYACCOUNT_RESEND_INVITE"
        tpl_mobile_data = {
            "1": workspace_instance.name,
            "2": user.email,
            "3": data["user_password"],
            "4": settings.MYACCOUNT_WEB_URL,
        }
        whatsapp_client = WhatsappClient(CONTAINER)
        whatsapp_client.send_message(user.phone, template_key=tpl_mobile, language=language, data=tpl_mobile_data)

    build_and_send_msg(data, language, subject, tpl_mail, workspace_id)

    return data, tpl_mail, tpl_mobile


def build_and_send_msg(data, language, subject, email_template, workspace_id):
    """
    Compile message and send
    """
    template_dir = os.path.join(settings.BASE_DIR, "locales")

    _locale = gettext.translation("notification", localedir=template_dir, languages=[language])
    _locale.install()
    _locale.info()
    subject = _locale.gettext(subject)

    message = CONTAINER.aws_email_client.render_email(email_template, _locale, **data)

    if not message:
        return
    CONTAINER.notification_client.send_email(
        MessagePayload(
            body=message,
            receiver_mail=data["user_email"],
            subject=subject,
            workspace_id=workspace_id,
            language=language if language != "pt-br" else "pt-BR",
        )
    )


@app.task(name=NOTIFY_USER_BY_ROLE, queue=settings.CELERY_QUEUE, ignore_result=True)
def notify_user_by_role(user_role_workspace_id: str, password: string = None, new: bool = True):
    user_role_workspace = UserRoleWorkspace.objects.get(id=user_role_workspace_id)
    applications_with_notification = [str(settings.KONQUEST_ID), str(settings.SMARTZAP_ID)]
    if str(user_role_workspace.role_id) == settings.SMARTZAP_DEFAULT_ROLE:
        return
    if str(user_role_workspace.role.application.id) in applications_with_notification:
        print("notifiynnD")
        notify_user(
            user_role_workspace.workspace_id, user_role_workspace.user_id, user_role_workspace.role_id, password, new
        )


def password_generator():
    return "".join(random.choice(string.digits) for x in range(6))
