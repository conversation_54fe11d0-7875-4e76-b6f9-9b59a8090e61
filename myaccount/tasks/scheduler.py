from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import Cron<PERSON>rigger
from config import settings

from tasks.billing import (
    generate_workspaces_konquest_billing,
    generate_workspaces_smartzap_billing,
)

INTERVAL = "interval"


def start_background_schedulers():
    jobs = [
        {
            "func": generate_workspaces_konquest_billing,
            "trigger": CronTrigger.from_crontab(settings.GENERATE_BILLING_WORKSPACES),
        },
        {
            "func": generate_workspaces_smartzap_billing,
            "trigger": CronTrigger.from_crontab(settings.GENERATE_BILLING_WORKSPACES),
        },
    ]

    scheduler = BackgroundScheduler(timezone="America/Sao_Paulo")

    for job in jobs:
        scheduler.add_job(**job)

    scheduler.start()
