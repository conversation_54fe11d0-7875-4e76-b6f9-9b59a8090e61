from config import settings
from di import Container
from django_injector import inject


class TemplateNotFound(Exception):
    def __init__(self, template_key, lang):
        self.message = "Any template was found for the given params: " "template_key: {}, lang: {}".format(
            template_key, lang
        )


class WhatsappClient:
    @inject
    def __init__(self, container: Container):
        self.broker = settings.WHATSAPP_BROKER
        self.base_dir = settings.BASE_DIR
        self.container = container
        self.templates = {
            "pt-br": {
                "MYACCOUNT_KONQUEST_ONBOARDING": "HX6217f7d1872244e533072ba90508239d",
                "MYACCOUNT_KONQUEST_INVITE": "HXb0f7ddc3ac2557db10e6c4642751e8d2",
                "MYACCOUNT_SMARTZAP_ONBOARDING": "HXd8d61482db3d6370f9d3ba373d25bbcd",
                "MYACCOUNT_SMARTZAP_INVITE": "HX096eeff175797b7109744baa09857c35",
                "MYACCOUNT_RESEND_INVITE": "HX6217f7d1872244e533072ba90508239d",
            },
            "pt-pt": {
                "MYACCOUNT_KONQUEST_ONBOARDING": "HX6217f7d1872244e533072ba90508239d",
                "MYACCOUNT_KONQUEST_INVITE": "HXb0f7ddc3ac2557db10e6c4642751e8d2",
                "MYACCOUNT_SMARTZAP_ONBOARDING": "HXd8d61482db3d6370f9d3ba373d25bbcd",
                "MYACCOUNT_SMARTZAP_INVITE": "HX096eeff175797b7109744baa09857c35",
                "MYACCOUNT_RESEND_INVITE": "HX6217f7d1872244e533072ba90508239d",
            },
            "en": {
                "MYACCOUNT_KONQUEST_ONBOARDING": "HX5c7acc8ca2a681c3eb4e7792755e2bc8",
                "MYACCOUNT_KONQUEST_INVITE": "HX4263d1a6a3a622ccc6dd9ee98624079d",
                "MYACCOUNT_SMARTZAP_ONBOARDING": "HXd8d61482db3d6370f9d3ba373d25bbcd",
                "MYACCOUNT_SMARTZAP_INVITE": "HXfc563227111fb41ad625eac88b54024d",
                "MYACCOUNT_RESEND_INVITE": "HX5c7acc8ca2a681c3eb4e7792755e2bc8",
            },
            "es": {
                "MYACCOUNT_KONQUEST_ONBOARDING": "HXa257d5925ee51df624142d4c199d9b28",
                "MYACCOUNT_KONQUEST_INVITE": "HXe2ba8732912f466523811378043b9660",
                "MYACCOUNT_SMARTZAP_ONBOARDING": "HXe7c6b00e093f13274efea5845452dd60",
                "MYACCOUNT_SMARTZAP_INVITE": "HX41dd54eff4dc0cb19b28d6d5bb1873a9",
                "MYACCOUNT_RESEND_INVITE": "HXbe59161c0642f7417f3575665c9a59f2",
            },
        }

    def get_template_sid(self, template_key: str, lang: str) -> str:
        if not lang:
            return self.templates["pt-br"][template_key]
        return self.templates[lang.lower()][template_key]

    def send_message(self, user_phone, template_key, language, data):
        try:
            template_sid = self.get_template_sid(template_key, language)
        except KeyError:
            raise TemplateNotFound(template_key, language)

        self.container.whatsapp_twilio_client.send_message(user_phone, template_sid, data)
