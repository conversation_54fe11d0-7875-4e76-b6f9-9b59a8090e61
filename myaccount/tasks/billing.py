from common.services.billing_konquest_service import BillingKonquestService
from common.services.billing_smartzap_service import BillingSmartzapService
from utils.task_transaction import task_transaction


def generate_workspaces_konquest_billing():
    with task_transaction("generate_workspaces_konquest_billing"):
        BillingKonquestService().generate()


def generate_workspaces_smartzap_billing():
    with task_transaction("generate_workspaces_smartzap_billing"):
        BillingSmartzapService().generate()
