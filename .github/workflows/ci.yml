name: Pipeline (CI)

on:
  workflow_dispatch:
  pull_request:
    branches:
      - main
      - develop
    types: [opened, synchronize, reopened]

jobs:
  tests:
    name: Run tests
    uses: ./.github/workflows/test_and_lint.yml
    secrets:
      KEEPS_PYTHON_CORE_GIT_TOKEN: ${{ secrets.KEEPS_PYTHON_CORE_GIT_TOKEN }}

  create-coverage-badges:
    name: Create coverage badges
    runs-on: ubuntu-latest
    needs: [ tests ]
    steps:
      - uses: actions/checkout@v4
      - name: Download coverage report
        uses: actions/download-artifact@v4
        with:
          name: reports
          path: .reports
