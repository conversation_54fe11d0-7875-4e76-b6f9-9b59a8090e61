name: Unit tests and lint

on:
  workflow_call:
    secrets:
      KEEPS_PYTHON_CORE_GIT_TOKEN:
        required: true
    inputs:
      run-build:
        default: true
        type: boolean
        required: false

env:
  CORE_GIT_TOKEN: ${{ secrets.KEEPS_PYTHON_CORE_GIT_TOKEN }}

jobs:
  unit-tests:

    runs-on: ubuntu-latest

    permissions:
      contents: write

    services:
      postgres:
        image: postgres:latest
        env:
          POSTGRES_PORT: 5432
          POSTGRES_DB: myaccount_tst
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - run: mkdir -p .reports

      - uses: actions/checkout@v4
        with:
          ref: ${{ github.head_ref }}

      - uses: actions/setup-python@v4
        with:
          python-version: "3.9"
          cache: "pip"

      - name: Running Code Conventions
        run:
          make ruff-check

      - name: Install dependencies
        run: |
          python -m pip install pip==24.0
          pip install -r myaccount/requirements.txt

      - name: Running Tests
        run: |
          export SUSPEND_SIGNALS=True
          export ENVIRONMENT_TEST=True
          make test-cov

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: reports
          path: .reports/
          include-hidden-files: true

