name: 'Create coverage badge'
description: 'Creates and updates the code coverage badge for the specified app'
inputs:
  coverage-file:
    description: 'Path to the coverage report summary json file (with .json extension)'
    required: true
  gist-id:
    description: 'Id of the gist used to host the coverage report contents'
    required: true
  gist-file:
    description: 'Name of the gist file with the (with .json extension)'
    required: true
  gist-token:
    description: 'Github token with permission to create gists'
    required: true
runs:
  using: "composite"
  steps:
    - name: Check if summary file exists
      shell: bash
      run: |
        FILE=${{ inputs.coverage-file }}
        if [ -f "$FILE" ];
        then
          echo "FILE_EXISTS=1" >> $GITHUB_ENV
        else
          echo "File ${{ inputs.coverage-file }} not found, skipping subsequent steps"
          echo "FILE_EXISTS=0" >> $GITHUB_ENV
        fi

    # We use this action only to extract the values from the summary in order to create the badge
    - name: Extract coverage from summary
      if: env.FILE_EXISTS == 1
      id: coverage-comment
      uses: MishaKav/pytest-coverage-comment@main
      with:
        hide-comment: true
        pytest-xml-coverage-path: ${{ inputs.coverage-file }}

    - name: Extract coverage
      id: coverage
      uses: sergeysova/jq-action@v2
      env:
        cov: ${{ steps.coverage-comment.outputs.coverage }}
      with:
        cmd: jq .cover <<<$cov | tr -d '"-/-' 

    - name: Check the output coverage badge values
      if: env.FILE_EXISTS == 1
      shell: bash
      run: | 
        echo "Coverage percentage: ${{ steps.coverage.outputs.value }}"
        echo "Coverage percentage for ${{ inputs.coverage-file }} -> ${{ steps.coverage-comment.outputs.coverage }}"
        echo "Coverage color for ${{ inputs.coverage-file }} -> ${{ steps.coverage-comment.outputs.color }}"

    - name: Create badges
      if: env.FILE_EXISTS == 1
      uses: schneegans/dynamic-badges-action@v1.6.0
      with:
        auth: ${{ inputs.gist-token }}
        gistID: ${{ inputs.gist-id }}
        filename: ${{ inputs.gist-file }}
        label: Coverage
        message: ${{ steps.coverage-comment.outputs.coverage }}
        color: ${{ steps.coverage-comment.outputs.color }}