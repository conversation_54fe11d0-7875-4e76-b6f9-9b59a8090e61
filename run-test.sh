#!/bin/bash

### Local ###
export DATABASE_NAME=myaccount_dev_db
export DATABASE_USER=postgres
export DATABASE_PASSWORD=postgres
export DATABASE_HOST=localhost
export DATABASE_PORT=15432

# Declare empty DISCORD_WEBHOOK to avoid reporting errors in dev
export DISCORD_WEBHOOK=

export DEBUG=True
export ENVIRONMENT=development
export ENVIRONMENT_TEST=True
export SUSPEND_SIGNALS=True
export PYTHONPATH="${PYTHONPATH}:${PWD}/myaccount/"

docker-compose -f dc_dev.yml up db -d

cd myaccount
pytest $@
cd ..

docker-compose -f dc_dev.yml down
