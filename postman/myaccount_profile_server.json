{"info": {"_postman_id": "d55d2a31-54e1-4852-868f-a6f182f326b3", "name": "My Account", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "Get User Token (from API)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "pm.test(\"Login token should have access_token key\", function () {", "    pm.expect(pm.response.text()).to.include(\"access_token\");", "});", "pm.test(\"Response time is less than 200ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});", "", "var access_token = JSON.parse(responseBody)['access_token'];", "var access_token_decoded = access_token.split('.')[1].replace('-', '+').replace('_', '/');", "pm.environment.set(\"ACCESS_TOKEN\", access_token);", "pm.environment.set(\"USER_ADMIN_UUID\", JSON.parse(atob(access_token_decoded))['sub']);", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "username", "value": "{{USER_LOGIN}}", "type": "text"}, {"key": "password", "value": "{{USER_LOGIN_PASS}}", "type": "text"}]}, "url": {"raw": "{{API_URL}}/auth", "host": ["{{API_URL}}"], "path": ["auth"]}, "description": "User authentication on myaccount server."}, "response": [{"name": "token", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "username", "value": "{{USER_LOGIN}}", "type": "text"}, {"key": "password", "value": "{{USER_LOGIN_PASS}}", "type": "text"}]}, "url": {"raw": "{{API_URL}}/auth", "host": ["{{API_URL}}"], "path": ["auth"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "2790", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 15 Mar 2018 17:48:52 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\"expires_in\": 300, \"access_token\": \"eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJQVjBNNkU5NDIyX2g1bm9XV3NvUWotaUk1R19vOTRBdUgwSjVIaXF5R28wIn0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dzSf9FHNpv-cu84JrKX7f2he19lKSZJY1cIMbzmtTY6u8aJltGolwPdET9K7zwzN_zm0s5PYkRQ2a018PGr4nC5eWPHOY4GeileSpl0isKogu64itWjyPBIfHdPsG-bG7lVNeidS3hgBPZtDzS5-dHS3_COVK2YSqAEP9qSWGFyGWYnOdCbYVb_mx8nmnDNmJ-OSZavXN_L1reLZ9atNOnm_lzJ1elOP6FDc7kPXUBsZV9Rn9aKgUrWW2HxZEf1QWvAI7_H-y3cht0ktQDerLsyS9pRMlO9oLVC5cIzn5xLAyxUxKJzrn6y1qpngUARRRs9AobZrHxgZpHYhrYz-Fg\", \"refresh_expires_in\": 1800, \"refresh_token\": \"eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJQVjBNNkU5NDIyX2g1bm9XV3NvUWotaUk1R19vOTRBdUgwSjVIaXF5R28wIn0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.U4STPabZR3MBBhzfi7UKsQvPzRzAqPr6NML8yFc5IlET_un_s_qTPTig1tVJab_j7RfB3rnbg36xU8gqf6ZrpUU4-ymB1oLLjmON6Aod04gyM4bJzKWYUrenr7-kQ2CPFi01sOxI9BjAlIc4Z62NCT1Hsi0WBJ0LvfMo22ytLfy2k0Pm8DJuXM_yjLflSgYTXwHgmewLKJPKGc1TRFc4PmhEkhzjeUEMOs9EXmKaMVW1e106Z0gmeIQkInhkBgnnG_kVKtCYKoOZ3tYmslzLbvgXp_SiG2r1zwX2TRcnzXzfczm7cIQrd-fAE7-BNPLhhckTlqivOfcCD5CnxhOoLg\", \"session_state\": \"d7dc8bcd-9bbb-4d1c-a7fd-857f77d25e6f\", \"not-before-policy\": 1520622624, \"token_type\": \"bearer\"}"}, {"name": "401 UNAUTHORIZED", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "username", "value": "{{USER_LOGIN}}", "type": "text"}, {"key": "password", "value": "{{USER_LOGIN_PASS}}", "type": "text"}]}, "url": {"raw": "{{API_URL}}/auth", "host": ["{{API_URL}}"], "path": ["auth"]}}, "status": "UNAUTHORIZED", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "75", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 15 Mar 2018 17:51:31 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\"error\": \"invalid_grant\", \"error_description\": \"Invalid user credentials\"}"}]}]}, {"name": "Company Managment", "item": [{"name": "create company", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var response_data = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"COMPANY_UUID\", response_data['id']);", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var _company = \"company_\" + Math.floor(Math.random() * (1000000 - 1 + 1) + 1)", "postman.setEnvironmentVariable(\"COMPANY_NAME\", _company.toUpperCase());"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"{{COMPANY_NAME}}\"\n}"}, "url": {"raw": "{{API_URL}}/companies", "host": ["{{API_URL}}"], "path": ["companies"]}}, "response": [{"name": "200", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"{{COMPANY_NAME}}\"\n}"}, "url": {"raw": "{{API_URL}}/companies", "host": ["{{API_URL}}"], "path": ["companies"]}}, "status": "CREATED", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "235", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 20:05:54 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\n    \"created_date\": \"2018-05-24T17:05:54.092720+00:00\",\n    \"description\": null,\n    \"status\": true,\n    \"uuid\": \"d4a09578-f0cb-4a9b-8de0-af349686b1ed\",\n    \"name\": \"COMPANY_122679\",\n    \"duns_number\": null,\n    \"doc_number\": null\n}\n"}, {"name": "400", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"test\": \"{{COMPANY_NAME}}\"\n}"}, "url": {"raw": "{{API_URL}}/companies", "host": ["{{API_URL}}"], "path": ["companies"]}}, "status": "BAD REQUEST", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "64", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 20:06:19 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\"message\": \"'test' is an invalid keyword argument for Company\"}"}]}, {"name": "companies", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["tests[\"Get profile user\"] = responseCode.code === 200;", ""]}}], "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/companies", "host": ["{{API_URL}}"], "path": ["companies"]}}, "response": [{"name": "200 (nopaginate)", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/companies?nopaginate", "host": ["{{API_URL}}"], "path": ["companies"], "query": [{"key": "nopaginate", "value": null}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "743", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 20:11:44 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "[\n  {\n    \"created_date\": \"2018-05-24T17:05:54.092720+00:00\", \n    \"description\": null, \n    \"doc_number\": null, \n    \"duns_number\": null, \n    \"name\": \"COMPANY_122679\", \n    \"status\": true, \n    \"uuid\": \"d4a09578-f0cb-4a9b-8de0-af349686b1ed\"\n  }, \n  {\n    \"created_date\": \"2018-05-24T17:06:54.278600+00:00\", \n    \"description\": null, \n    \"doc_number\": null, \n    \"duns_number\": null, \n    \"name\": \"COMPANY_122679\", \n    \"status\": true, \n    \"uuid\": \"1e21a4bc-3fc9-4536-8d33-d51ae168a2a8\"\n  }, \n  {\n    \"created_date\": \"2018-05-24T17:07:24.052681+00:00\", \n    \"description\": null, \n    \"doc_number\": null, \n    \"duns_number\": null, \n    \"name\": \"COMPANY_122679\", \n    \"status\": true, \n    \"uuid\": \"0e4baf99-84b7-4310-acbf-d41c6fe539fd\"\n  }\n]\n"}, {"name": "200", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/companies", "host": ["{{API_URL}}"], "path": ["companies"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "999", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 20:11:29 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\n    \"per_page\": 20,\n    \"page\": 1,\n    \"items\": [\n        {\n            \"created_date\": \"2018-05-24T17:05:54.092720+00:00\",\n            \"description\": null,\n            \"status\": true,\n            \"uuid\": \"d4a09578-f0cb-4a9b-8de0-af349686b1ed\",\n            \"name\": \"COMPANY_122679\",\n            \"duns_number\": null,\n            \"doc_number\": null\n        },\n        {\n            \"created_date\": \"2018-05-24T17:06:54.278600+00:00\",\n            \"description\": null,\n            \"status\": true,\n            \"uuid\": \"1e21a4bc-3fc9-4536-8d33-d51ae168a2a8\",\n            \"name\": \"COMPANY_122679\",\n            \"duns_number\": null,\n            \"doc_number\": null\n        },\n        {\n            \"created_date\": \"2018-05-24T17:07:24.052681+00:00\",\n            \"description\": null,\n            \"status\": true,\n            \"uuid\": \"0e4baf99-84b7-4310-acbf-d41c6fe539fd\",\n            \"name\": \"COMPANY_122679\",\n            \"duns_number\": null,\n            \"doc_number\": null\n        }\n    ],\n    \"total\": 3\n}\n"}]}, {"name": "companies by uuid", "event": [{"listen": "test", "script": {"exec": ["tests[\"Get profile user\"] = responseCode.code === 200;"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/companies/{{COMPANY_UUID}}", "host": ["{{API_URL}}"], "path": ["companies", "{{COMPANY_UUID}}"]}}, "response": [{"name": "404", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/companies/1", "host": ["{{API_URL}}"], "path": ["companies", "1"]}}, "status": "NOT FOUND", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "3", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 20:13:44 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "\"\"\n"}, {"name": "200", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/companies/{{COMPANY_UUID}}", "host": ["{{API_URL}}"], "path": ["companies", "{{COMPANY_UUID}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "235", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 20:12:14 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\n    \"created_date\": \"2018-05-24T17:07:24.052681+00:00\",\n    \"description\": null,\n    \"status\": true,\n    \"uuid\": \"0e4baf99-84b7-4310-acbf-d41c6fe539fd\",\n    \"name\": \"COMPANY_122679\",\n    \"duns_number\": null,\n    \"doc_number\": null\n}\n"}]}, {"name": "update company", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["tests[\"Update Company\"] = responseCode.code === 200;", ""]}}, {"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["var _company = \"company_\" + Math.floor(Math.random() * (1000000 - 1 + 1) + 1)", "postman.setEnvironmentVariable(\"COMPANY_NAME\", _company.toUpperCase());"]}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"{{COMPANY_NAME}}\"\n}"}, "url": {"raw": "{{API_URL}}/companies/{{COMPANY_UUID}}", "host": ["{{API_URL}}"], "path": ["companies", "{{COMPANY_UUID}}"]}}, "response": [{"name": "200", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"{{COMPANY_NAME}}\"\n}"}, "url": {"raw": "{{API_URL}}/companies/{{COMPANY_UUID}}", "host": ["{{API_URL}}"], "path": ["companies", "{{COMPANY_UUID}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "235", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 20:14:07 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\n    \"uuid\": \"0e4baf99-84b7-4310-acbf-d41c6fe539fd\",\n    \"description\": null,\n    \"created_date\": \"2018-05-24T17:07:24.052681+00:00\",\n    \"doc_number\": null,\n    \"duns_number\": null,\n    \"status\": true,\n    \"name\": \"COMPANY_653102\"\n}\n"}]}]}, {"name": "User Profile Management", "item": [{"name": "user profile info", "event": [{"listen": "test", "script": {"exec": ["tests[\"Get profile user\"] = responseCode.code === 200;"], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{API_URL}}/users/info", "host": ["{{API_URL}}"], "path": ["users", "info"]}}, "response": [{"name": "401 UNAUTHORIZED", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/users", "host": ["{{API_URL}}"], "path": ["users"]}}, "status": "UNAUTHORIZED", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "37", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 15 Mar 2018 16:29:27 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\"message\": \"Signature has expired.\"}"}, {"name": "200", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/users", "host": ["{{API_URL}}"], "path": ["users"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "604", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 19:39:36 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\n    \"page\": 1,\n    \"total\": 1,\n    \"items\": [\n        {\n            \"job\": null,\n            \"gender\": null,\n            \"avatar\": null,\n            \"address\": null,\n            \"preference_language\": null,\n            \"created_date\": \"2018-05-24T12:39:11.025449+00:00\",\n            \"updated_date\": null,\n            \"email\": \"<EMAIL>\",\n            \"secondary_email\": null,\n            \"nickname\": null,\n            \"uuid\": \"a21757cb-3469-4af0-b717-44e5bb095c0f\",\n            \"phone\": null,\n            \"birthday\": null,\n            \"name\": \"Test Keeps\"\n        }\n    ],\n    \"per_page\": 20\n}\n"}, {"name": "200 (nopaginate)", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/users?nopaginate", "host": ["{{API_URL}}"], "path": ["users"], "query": [{"key": "nopaginate", "value": null}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "421", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 19:40:04 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "[\n  {\n    \"address\": null, \n    \"avatar\": null, \n    \"birthday\": null, \n    \"created_date\": \"2018-05-24T12:39:11.025449+00:00\", \n    \"email\": \"<EMAIL>\", \n    \"gender\": null, \n    \"job\": null, \n    \"name\": \"Test Keeps\", \n    \"nickname\": null, \n    \"phone\": null, \n    \"preference_language\": null, \n    \"secondary_email\": null, \n    \"updated_date\": null, \n    \"uuid\": \"a21757cb-3469-4af0-b717-44e5bb095c0f\"\n  }\n]\n"}]}, {"name": "public user profile", "event": [{"listen": "test", "script": {"exec": ["tests[\"Get profile user\"] = responseCode.code === 200;", "var jsonData = JSON.parse(responseBody);", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}, {"key": "x-client", "value": "{{COMPANY_UUID}}", "type": "text"}], "url": {"raw": "{{API_URL}}/users/public/<EMAIL>", "host": ["{{API_URL}}"], "path": ["users", "public", "<EMAIL>"]}}, "response": [{"name": "200", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/users/public/<EMAIL>", "host": ["{{API_URL}}"], "path": ["users", "public", "<EMAIL>"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": "Options that are desired for the connection"}, {"key": "Content-Length", "value": "97", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 19:26:59 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "P3P", "value": "CP=\"ALL DSP COR PSAa PSDa OUR NOR ONL UNI COM NAV\"", "name": "P3P", "description": "This header is supposed to set P3P policy, in the form of P3P:CP=\"your_compact_policy\". However, P3P did not take off, most browsers have never fully implemented it, a lot of websites set this header with fake policy text, that was enough to fool browsers the existence of P3P policy and grant permissions for third party cookies."}, {"key": "Server", "value": "nginx/1.10.3 (<PERSON><PERSON><PERSON><PERSON>)", "name": "Server", "description": "A name for the server"}, {"key": "Via", "value": "1.1 firewall.agriness.com:3128 (squid)", "name": "Via", "description": "Informs the client of proxies through which the response was sent."}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "MISS from firewall.agriness.com", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Custom header"}, {"key": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "value": "MISS from firewall.agriness.com:3128", "name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "description": "Custom header"}], "cookie": [], "body": "{\n  \"avatar\": null, \n  \"name\": \"Test Keeps\", \n  \"uuid\": \"a21757cb-3469-4af0-b717-44e5bb095c0f\"\n}\n"}, {"name": "404", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/users/public/<EMAIL>", "host": ["{{API_URL}}"], "path": ["users", "public", "<EMAIL>"]}}, "status": "NOT FOUND", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "3", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 19:38:51 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "\"\"\n"}]}, {"name": "user profile", "event": [{"listen": "test", "script": {"exec": ["tests[\"Get profile user\"] = responseCode.code === 200;", "", "var jsonData = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"USER_UUID\", jsonData['results'][0]['id']);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}, {"key": "x-client", "value": "{{COMPANY_UUID}}", "type": "text"}], "url": {"raw": "{{API_URL}}/users?page_1&per_page=2", "host": ["{{API_URL}}"], "path": ["users"], "query": [{"key": "page_1", "value": null}, {"key": "per_page", "value": "2"}]}}, "response": [{"name": "401 UNAUTHORIZED", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/users", "host": ["{{API_URL}}"], "path": ["users"]}}, "status": "UNAUTHORIZED", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "37", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 15 Mar 2018 16:29:27 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\"message\": \"Signature has expired.\"}"}, {"name": "200", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/users", "host": ["{{API_URL}}"], "path": ["users"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "604", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 19:39:36 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\n    \"page\": 1,\n    \"total\": 1,\n    \"items\": [\n        {\n            \"job\": null,\n            \"gender\": null,\n            \"avatar\": null,\n            \"address\": null,\n            \"preference_language\": null,\n            \"created_date\": \"2018-05-24T12:39:11.025449+00:00\",\n            \"updated_date\": null,\n            \"email\": \"<EMAIL>\",\n            \"secondary_email\": null,\n            \"nickname\": null,\n            \"uuid\": \"a21757cb-3469-4af0-b717-44e5bb095c0f\",\n            \"phone\": null,\n            \"birthday\": null,\n            \"name\": \"Test Keeps\"\n        }\n    ],\n    \"per_page\": 20\n}\n"}, {"name": "200 (nopaginate)", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/users?nopaginate", "host": ["{{API_URL}}"], "path": ["users"], "query": [{"key": "nopaginate", "value": null}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "421", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 19:40:04 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "[\n  {\n    \"address\": null, \n    \"avatar\": null, \n    \"birthday\": null, \n    \"created_date\": \"2018-05-24T12:39:11.025449+00:00\", \n    \"email\": \"<EMAIL>\", \n    \"gender\": null, \n    \"job\": null, \n    \"name\": \"Test Keeps\", \n    \"nickname\": null, \n    \"phone\": null, \n    \"preference_language\": null, \n    \"secondary_email\": null, \n    \"updated_date\": null, \n    \"uuid\": \"a21757cb-3469-4af0-b717-44e5bb095c0f\"\n  }\n]\n"}]}, {"name": "user profile by uuid", "event": [{"listen": "test", "script": {"exec": ["tests[\"Get profile user\"] = responseCode.code === 200;"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}, {"key": "x-client", "value": "{{COMPANY_UUID}}", "type": "text"}], "url": {"raw": "{{API_URL}}/users/{{USER_UUID}}", "host": ["{{API_URL}}"], "path": ["users", "{{USER_UUID}}"]}}, "response": [{"name": "404", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/users/1", "host": ["{{API_URL}}"], "path": ["users", "1"]}}, "status": "NOT FOUND", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "3", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 19:42:13 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "\"\"\n"}, {"name": "200", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/users/{{USER_UUID}}", "host": ["{{API_URL}}"], "path": ["users", "{{USER_UUID}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "400", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 19:41:12 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\n    \"job\": null,\n    \"gender\": null,\n    \"avatar\": null,\n    \"address\": null,\n    \"preference_language\": null,\n    \"created_date\": \"2018-05-24T12:39:11.025449+00:00\",\n    \"updated_date\": null,\n    \"email\": \"<EMAIL>\",\n    \"secondary_email\": null,\n    \"nickname\": null,\n    \"uuid\": \"a21757cb-3469-4af0-b717-44e5bb095c0f\",\n    \"phone\": null,\n    \"birthday\": null,\n    \"name\": \"Test Keeps\"\n}\n"}]}]}, {"name": "Company User Managment", "item": [{"name": "companies users", "event": [{"listen": "test", "script": {"exec": ["tests[\"Get company users\"] = responseCode.code === 200;", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}, {"key": "x-client", "value": "{{COMPANY_UUID}}", "type": "text"}], "url": {"raw": "{{API_URL}}/companies/{{COMPANY_UUID}}/users", "host": ["{{API_URL}}"], "path": ["companies", "{{COMPANY_UUID}}", "users"]}}, "response": [{"name": "200", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/companies/{{COMPANY_UUID}}/users", "host": ["{{API_URL}}"], "path": ["companies", "{{COMPANY_UUID}}", "users"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "381", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 20:44:03 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\n    \"per_page\": 10,\n    \"items\": [\n        {\n            \"company_uuid\": \"b38d85c9-ffaa-455f-b72f-475ec60bee27\",\n            \"user\": {\n                \"nickname\": null,\n                \"email\": \"<EMAIL>\",\n                \"uuid\": \"a21757cb-3469-4af0-b717-44e5bb095c0f\",\n                \"name\": \"Test Keeps\"\n            }\n        }\n    ],\n    \"total\": 1,\n    \"page\": 1\n}\n"}, {"name": "400", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/companies/kk/users", "host": ["{{API_URL}}"], "path": ["companies", "kk", "users"]}}, "status": "BAD REQUEST", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "29", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 20:50:14 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\"message\": \"not permission\"}"}]}, {"name": "new company user", "event": [{"listen": "test", "script": {"exec": ["tests[\"Get company users\"] = responseCode.code === 201;", "var jsonData = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"USER_UUID2\", jsonData['id']);", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var _user = \"user_\" + Math.floor(Math.random() * (1000000 - 1 + 1) + 1)", "postman.setEnvironmentVariable(\"USER_PROFILE_EMAIL\", (_user + \"@keepsdev.com\"));", "postman.setEnvironmentVariable(\"USER_PROFILE_NAME\", _user.toUpperCase());", "", "var _user_2 = \"user_\" + Math.floor(Math.random() * (1000000 - 1 + 1) + 1)", "postman.setEnvironmentVariable(\"USER_PROFILE_EMAIL_2\", (_user_2 + \"@keepsdev.com\"));", "postman.setEnvironmentVariable(\"USER_PROFILE_NAME_2\", _user_2.toUpperCase());"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}, {"key": "x-client", "value": "fd7fbfc3-db94-4eb7-9c11-13ef5257c3e0", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"users\": [\n    {\n      \"name\": \"{{USER_PROFILE_NAME}}\",\n      \"email\": \"<EMAIL>\",\n      \"gender\": \"MALE\"\n    }\n  ],\n  \"permissions\": [\n    \"297a88de-c34b-4661-be8a-7090fa9a89e5\"\n  ]\n}"}, "url": {"raw": "{{API_URL}}/companies/{{COMPANY_UUID}}/users", "host": ["{{API_URL}}"], "path": ["companies", "{{COMPANY_UUID}}", "users"]}}, "response": [{"name": "201", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"user_info\": {\n      \"email\": \"{{USER_PROFILE_EMAIL}}\",\n      \"name\": \"{{USER_PROFILE_NAME}}\"\n    },\n    \"permission\": {\n    \"client_id\": \"10cf1fa0-925f-4b16-9572-796d32bca692\",\n      \"roles\": {\"name\": \"player\", \"id\": \"64581e25-1f91-4321-b414-d05b7c6802f7\"}\n  }\n}"}, "url": {"raw": "{{API_URL}}/companies/{{COMPANY_UUID}}/users", "host": ["{{API_URL}}"], "path": ["companies", "{{COMPANY_UUID}}", "users"]}}, "status": "CREATED", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "408", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 20:50:46 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\n    \"updated_date\": null,\n    \"nickname\": null,\n    \"address\": null,\n    \"created_date\": \"2018-05-24T17:50:45.765456+00:00\",\n    \"secondary_email\": null,\n    \"phone\": null,\n    \"job\": null,\n    \"avatar\": null,\n    \"email\": \"<EMAIL>\",\n    \"preference_language\": null,\n    \"gender\": null,\n    \"birthday\": null,\n    \"name\": \"USER_855378\",\n    \"uuid\": \"e50d281e-3f87-4a39-a306-1f812c895287\"\n}\n"}]}, {"name": "user companies", "event": [{"listen": "test", "script": {"exec": ["tests[\"Get company users\"] = responseCode.code === 200;", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}, {"key": "x-client", "value": "{{COMPANY_UUID}}", "type": "text"}], "url": {"raw": "{{API_URL}}/users/{{USER_UUID}}/companies", "host": ["{{API_URL}}"], "path": ["users", "{{USER_UUID}}", "companies"]}}, "response": [{"name": "400", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/companies/kk/users", "host": ["{{API_URL}}"], "path": ["companies", "kk", "users"]}}, "status": "BAD REQUEST", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "29", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 20:50:14 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\"message\": \"not permission\"}"}, {"name": "200", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/companies/{{COMPANY_UUID}}/users", "host": ["{{API_URL}}"], "path": ["companies", "{{COMPANY_UUID}}", "users"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "381", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 20:44:03 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\n    \"per_page\": 10,\n    \"items\": [\n        {\n            \"company_uuid\": \"b38d85c9-ffaa-455f-b72f-475ec60bee27\",\n            \"user\": {\n                \"nickname\": null,\n                \"email\": \"<EMAIL>\",\n                \"uuid\": \"a21757cb-3469-4af0-b717-44e5bb095c0f\",\n                \"name\": \"Test Keeps\"\n            }\n        }\n    ],\n    \"total\": 1,\n    \"page\": 1\n}\n"}]}, {"name": "new import user for company", "event": [{"listen": "test", "script": {"exec": ["tests[\"Get company users\"] = responseCode.code === 201;", "var jsonData = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"USER_UUID2\", jsonData['id']);", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var _user = \"user_\" + Math.floor(Math.random() * (1000000 - 1 + 1) + 1)", "postman.setEnvironmentVariable(\"USER_PROFILE_EMAIL\", (_user + \"@keepsdev.com\"));", "postman.setEnvironmentVariable(\"USER_PROFILE_NAME\", _user.toUpperCase());", "", "var _user_2 = \"user_\" + Math.floor(Math.random() * (1000000 - 1 + 1) + 1)", "postman.setEnvironmentVariable(\"USER_PROFILE_EMAIL_2\", (_user_2 + \"@keepsdev.com\"));", "postman.setEnvironmentVariable(\"USER_PROFILE_NAME_2\", _user_2.toUpperCase());"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}, {"key": "x-client", "value": "{{COMPANY_UUID}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"users\": [{\n    \t\"email\": \"{{USER_PROFILE_EMAIL}}\",\n    \t\"name\": \"{{USER_PROFILE_NAME}}\",\n    \t\"language_id\": \"ea636f50-fdc4-49b0-b2de-9e5905de456b\"\n    }],\n    \"permissions\": [\n    \t\"3b16b975-0297-4edf-950b-e3700b0d0d01\"\n\t]\n}"}, "url": {"raw": "{{API_URL}}/companies/4d4bda79-ac67-4d34-a180-8c49c2e00cb8/users", "host": ["{{API_URL}}"], "path": ["companies", "4d4bda79-ac67-4d34-a180-8c49c2e00cb8", "users"]}}, "response": [{"name": "201", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"user_info\": {\n      \"email\": \"{{USER_PROFILE_EMAIL}}\",\n      \"name\": \"{{USER_PROFILE_NAME}}\"\n    },\n    \"permission\": {\n    \"client_id\": \"10cf1fa0-925f-4b16-9572-796d32bca692\",\n      \"roles\": {\"name\": \"player\", \"id\": \"64581e25-1f91-4321-b414-d05b7c6802f7\"}\n  }\n}"}, "url": {"raw": "{{API_URL}}/companies/{{COMPANY_UUID}}/users", "host": ["{{API_URL}}"], "path": ["companies", "{{COMPANY_UUID}}", "users"]}}, "status": "CREATED", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "408", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 20:50:46 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\n    \"updated_date\": null,\n    \"nickname\": null,\n    \"address\": null,\n    \"created_date\": \"2018-05-24T17:50:45.765456+00:00\",\n    \"secondary_email\": null,\n    \"phone\": null,\n    \"job\": null,\n    \"avatar\": null,\n    \"email\": \"<EMAIL>\",\n    \"preference_language\": null,\n    \"gender\": null,\n    \"birthday\": null,\n    \"name\": \"USER_855378\",\n    \"uuid\": \"e50d281e-3f87-4a39-a306-1f812c895287\"\n}\n"}]}]}, {"name": "Application", "item": [{"name": "list applications", "event": [{"listen": "test", "script": {"exec": ["tests[\"Get applications\"] = responseCode.code === 200;", "", "var jsonData = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"APP_UUID\", jsonData[1]['id']);", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var _company = \"APP_\" + Math.floor(Math.random() * (1000000 - 1 + 1) + 1)", "postman.setEnvironmentVariable(\"APP_NAME\", _company.toUpperCase());"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/applications", "host": ["{{API_URL}}"], "path": ["applications"]}}, "response": [{"name": "200", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/companies/{{COMPANY_UUID}}/services", "host": ["{{API_URL}}"], "path": ["companies", "{{COMPANY_UUID}}", "services"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": "Options that are desired for the connection"}, {"key": "Content-Length", "value": "281", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Wed, 13 Jun 2018 13:33:25 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "P3P", "value": "CP=\"ALL DSP COR PSAa PSDa OUR NOR ONL UNI COM NAV\"", "name": "P3P", "description": "This header is supposed to set P3P policy, in the form of P3P:CP=\"your_compact_policy\". However, P3P did not take off, most browsers have never fully implemented it, a lot of websites set this header with fake policy text, that was enough to fool browsers the existence of P3P policy and grant permissions for third party cookies."}, {"key": "Server", "value": "nginx/1.10.3 (<PERSON><PERSON><PERSON><PERSON>)", "name": "Server", "description": "A name for the server"}, {"key": "Via", "value": "1.1 firewall.agriness.com:3128 (squid)", "name": "Via", "description": "Informs the client of proxies through which the response was sent."}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "MISS from firewall.agriness.com", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Custom header"}, {"key": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "value": "MISS from firewall.agriness.com:3128", "name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "description": "Custom header"}], "cookie": [], "body": "{\"company\": {\"uuid\": \"e61988b5-5df4-4f5c-8051-d758ba017268\", \"name\": \"COMPANY_124623\"}, \"created_date\": \"2018-06-13T13:33:16.508386+00:00\", \"service\": {\"description\": \"Mobile Game Learn\", \"name\": \"Mobile Game\", \"status\": true, \"id\": 1}, \"application_name\": \"APP_386457\", \"id\": 16}\n"}, {"name": "400", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/companies/kk/users", "host": ["{{API_URL}}"], "path": ["companies", "kk", "users"]}}, "status": "BAD REQUEST", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "29", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 20:50:14 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\"message\": \"not permission\"}"}]}, {"name": "list roles by applications", "event": [{"listen": "test", "script": {"exec": ["tests[\"Get application profiles\"] = responseCode.code === 200;", "", "", "var jsonData = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"ROLE_UUID\", jsonData[0]['id']);", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var _company = \"APP_\" + Math.floor(Math.random() * (1000000 - 1 + 1) + 1)", "postman.setEnvironmentVariable(\"APP_NAME\", _company.toUpperCase());"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/applications/{{APP_UUID}}/roles", "host": ["{{API_URL}}"], "path": ["applications", "{{APP_UUID}}", "roles"]}}, "response": [{"name": "400", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/companies/kk/users", "host": ["{{API_URL}}"], "path": ["companies", "kk", "users"]}}, "status": "BAD REQUEST", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "29", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 20:50:14 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\"message\": \"not permission\"}"}, {"name": "200", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/companies/{{COMPANY_UUID}}/services", "host": ["{{API_URL}}"], "path": ["companies", "{{COMPANY_UUID}}", "services"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": "Options that are desired for the connection"}, {"key": "Content-Length", "value": "281", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Wed, 13 Jun 2018 13:33:25 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "P3P", "value": "CP=\"ALL DSP COR PSAa PSDa OUR NOR ONL UNI COM NAV\"", "name": "P3P", "description": "This header is supposed to set P3P policy, in the form of P3P:CP=\"your_compact_policy\". However, P3P did not take off, most browsers have never fully implemented it, a lot of websites set this header with fake policy text, that was enough to fool browsers the existence of P3P policy and grant permissions for third party cookies."}, {"key": "Server", "value": "nginx/1.10.3 (<PERSON><PERSON><PERSON><PERSON>)", "name": "Server", "description": "A name for the server"}, {"key": "Via", "value": "1.1 firewall.agriness.com:3128 (squid)", "name": "Via", "description": "Informs the client of proxies through which the response was sent."}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "MISS from firewall.agriness.com", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Custom header"}, {"key": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "value": "MISS from firewall.agriness.com:3128", "name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "description": "Custom header"}], "cookie": [], "body": "{\"company\": {\"uuid\": \"e61988b5-5df4-4f5c-8051-d758ba017268\", \"name\": \"COMPANY_124623\"}, \"created_date\": \"2018-06-13T13:33:16.508386+00:00\", \"service\": {\"description\": \"Mobile Game Learn\", \"name\": \"Mobile Game\", \"status\": true, \"id\": 1}, \"application_name\": \"APP_386457\", \"id\": 16}\n"}]}, {"name": "create user profile", "event": [{"listen": "test", "script": {"exec": ["tests[\"Create user profile\"] = responseCode.code === 201;", "var jsonData = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"USER_UUID\", jsonData['id']);", "postman.setEnvironmentVariable(\"USER_EMAIL\", jsonData['email']);"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var _user = \"user_\" + Math.floor(Math.random() * (1000000 - 1 + 1) + 1)", "postman.setEnvironmentVariable(\"USER_PROFILE_EMAIL\", (_user + \"@keepsdev.com\"));", "postman.setEnvironmentVariable(\"USER_PROFILE_NAME\", _user.toUpperCase());"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}, {"key": "x-client", "value": "{{COMPANY_UUID}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{USER_PROFILE_EMAIL}}\",\n    \"name\": \"{{USER_PROFILE_NAME}}\",\n    \"birthday\": \"1983-05-09\",\n    \"address\": \"Rua Teste, Florianopolis\",\n    \"nickname\": \"Testes\",\n    \"phone\": \"48 ********\",\n    \"secondary_email\": \"<EMAIL>\",\n    \"gender\": \"FEMALE\",\n    \"avatar\": \"base:64://xxaaa\",\n    \"language\": 1,\n    \"job\": \"CEO\"\n}\n"}, "url": {"raw": "{{API_URL}}/users", "host": ["{{API_URL}}"], "path": ["users"]}, "description": "Create a new user profile in MyAccount and IAM server (Keycloak)\n\n* Requried Params\n  * email\n  * name\n\n* Format Params\n  * birthday: us pattern (yyyy-mm-dd)\n  * avatar: converted image in base64 string\n  * language: ID language (API: user languages available)"}, "response": [{"name": "create user profile", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{USER_PROFILE_EMAIL}}\",\n    \"name\": \"{{USER_PROFILE_NAME}}\",\n    \"birthday\": \"1983-05-09\",\n    \"address\": \"Rua Teste, Florianopolis\",\n    \"nickname\": \"Testes\",\n    \"phone\": \"48 ********\",\n    \"secondary_email\": \"<EMAIL>\",\n    \"gender\": \"FEMALE\",\n    \"avatar\": \"base:64://xxaaa\",\n    \"language\": 1,\n    \"job\": \"CEO\"\n}\n"}, "url": {"raw": "{{API_URL}}/users", "host": ["{{API_URL}}"], "path": ["users"]}}, "status": "CREATED", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "430", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 15 Mar 2018 15:48:58 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\"email\": \"<EMAIL>\", \"nickname\": \"Test<PERSON>\", \"secondary_email\": \"<EMAIL>\", \"birthday\": \"1983-05-09\", \"phone\": \"48 ********\", \"preference_language\": \"pt-BR\", \"uuid\": \"dd933079-d659-487f-ab22-6427999a16a7\", \"job\": \"CEO\", \"updated_date\": null, \"created_date\": \"2018-03-15T12:48:57.483911+00:00\", \"gender\": \"FEMALE\", \"address\": \"Rua Teste, Florianopolis\", \"name\": \"USER_860878\", \"avatar\": \"base:64://xxaaa\"}"}]}, {"name": "add role application", "event": [{"listen": "test", "script": {"exec": ["tests[\"Create application profiles\"] = responseCode.code === 201;", "", "", "var jsonData = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"USER_ROLE_UUID\", jsonData['id']);"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var _company = \"APP_\" + Math.floor(Math.random() * (1000000 - 1 + 1) + 1)", "postman.setEnvironmentVariable(\"APP_NAME\", _company.toUpperCase());"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}, {"key": "x-client", "value": "{{COMPANY_UUID}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"user\": \"{{USER_UUID}}\",\n\t\"company\": \"{{COMPANY_UUID}}\",\n\t\"role\": \"{{ROLE_UUID}}\"\n}"}, "url": {"raw": "{{API_URL}}/users/roles", "host": ["{{API_URL}}"], "path": ["users", "roles"]}}, "response": [{"name": "200", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/companies/{{COMPANY_UUID}}/services", "host": ["{{API_URL}}"], "path": ["companies", "{{COMPANY_UUID}}", "services"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": "Options that are desired for the connection"}, {"key": "Content-Length", "value": "281", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Wed, 13 Jun 2018 13:33:25 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "P3P", "value": "CP=\"ALL DSP COR PSAa PSDa OUR NOR ONL UNI COM NAV\"", "name": "P3P", "description": "This header is supposed to set P3P policy, in the form of P3P:CP=\"your_compact_policy\". However, P3P did not take off, most browsers have never fully implemented it, a lot of websites set this header with fake policy text, that was enough to fool browsers the existence of P3P policy and grant permissions for third party cookies."}, {"key": "Server", "value": "nginx/1.10.3 (<PERSON><PERSON><PERSON><PERSON>)", "name": "Server", "description": "A name for the server"}, {"key": "Via", "value": "1.1 firewall.agriness.com:3128 (squid)", "name": "Via", "description": "Informs the client of proxies through which the response was sent."}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "MISS from firewall.agriness.com", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Custom header"}, {"key": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "value": "MISS from firewall.agriness.com:3128", "name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "description": "Custom header"}], "cookie": [], "body": "{\"company\": {\"uuid\": \"e61988b5-5df4-4f5c-8051-d758ba017268\", \"name\": \"COMPANY_124623\"}, \"created_date\": \"2018-06-13T13:33:16.508386+00:00\", \"service\": {\"description\": \"Mobile Game Learn\", \"name\": \"Mobile Game\", \"status\": true, \"id\": 1}, \"application_name\": \"APP_386457\", \"id\": 16}\n"}, {"name": "400", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/companies/kk/users", "host": ["{{API_URL}}"], "path": ["companies", "kk", "users"]}}, "status": "BAD REQUEST", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "29", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 20:50:14 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\"message\": \"not permission\"}"}]}]}, {"name": "Application Service", "item": [{"name": "all services", "event": [{"listen": "test", "script": {"exec": ["tests[\"Get company users\"] = responseCode.code === 200;", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var _company = \"APP_\" + Math.floor(Math.random() * (1000000 - 1 + 1) + 1)", "postman.setEnvironmentVariable(\"APP_NAME\", _company.toUpperCase());"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/services", "host": ["{{API_URL}}"], "path": ["services"]}}, "response": [{"name": "200", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/companies/{{COMPANY_UUID}}/services", "host": ["{{API_URL}}"], "path": ["companies", "{{COMPANY_UUID}}", "services"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": "Options that are desired for the connection"}, {"key": "Content-Length", "value": "281", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Wed, 13 Jun 2018 13:33:25 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "P3P", "value": "CP=\"ALL DSP COR PSAa PSDa OUR NOR ONL UNI COM NAV\"", "name": "P3P", "description": "This header is supposed to set P3P policy, in the form of P3P:CP=\"your_compact_policy\". However, P3P did not take off, most browsers have never fully implemented it, a lot of websites set this header with fake policy text, that was enough to fool browsers the existence of P3P policy and grant permissions for third party cookies."}, {"key": "Server", "value": "nginx/1.10.3 (<PERSON><PERSON><PERSON><PERSON>)", "name": "Server", "description": "A name for the server"}, {"key": "Via", "value": "1.1 firewall.agriness.com:3128 (squid)", "name": "Via", "description": "Informs the client of proxies through which the response was sent."}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "MISS from firewall.agriness.com", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Custom header"}, {"key": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "value": "MISS from firewall.agriness.com:3128", "name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "description": "Custom header"}], "cookie": [], "body": "{\"company\": {\"uuid\": \"e61988b5-5df4-4f5c-8051-d758ba017268\", \"name\": \"COMPANY_124623\"}, \"created_date\": \"2018-06-13T13:33:16.508386+00:00\", \"service\": {\"description\": \"Mobile Game Learn\", \"name\": \"Mobile Game\", \"status\": true, \"id\": 1}, \"application_name\": \"APP_386457\", \"id\": 16}\n"}, {"name": "400", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/companies/kk/users", "host": ["{{API_URL}}"], "path": ["companies", "kk", "users"]}}, "status": "BAD REQUEST", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "29", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 20:50:14 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\"message\": \"not permission\"}"}]}, {"name": "acquire service", "event": [{"listen": "test", "script": {"exec": ["tests[\"Acquire service\"] = responseCode.code === 201;"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var _company = \"APP_\" + Math.floor(Math.random() * (1000000 - 1 + 1) + 1)", "postman.setEnvironmentVariable(\"APP_NAME\", _company.toUpperCase());"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}, {"key": "x-client", "value": "{{COMPANY_UUID}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"services\": [\"9f4369c4-e7c3-4630-84db-373c167a3c40\"], \n    \"application_name\": \"{{APP_NAME}}\"\n}"}, "url": {"raw": "{{API_URL}}/companies/{{COMPANY_UUID}}/services", "host": ["{{API_URL}}"], "path": ["companies", "{{COMPANY_UUID}}", "services"]}}, "response": [{"name": "200", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/companies/{{COMPANY_UUID}}/users", "host": ["{{API_URL}}"], "path": ["companies", "{{COMPANY_UUID}}", "users"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "381", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 20:44:03 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\n    \"per_page\": 10,\n    \"items\": [\n        {\n            \"company_uuid\": \"b38d85c9-ffaa-455f-b72f-475ec60bee27\",\n            \"user\": {\n                \"nickname\": null,\n                \"email\": \"<EMAIL>\",\n                \"uuid\": \"a21757cb-3469-4af0-b717-44e5bb095c0f\",\n                \"name\": \"Test Keeps\"\n            }\n        }\n    ],\n    \"total\": 1,\n    \"page\": 1\n}\n"}, {"name": "400", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/companies/kk/users", "host": ["{{API_URL}}"], "path": ["companies", "kk", "users"]}}, "status": "BAD REQUEST", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "29", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 20:50:14 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\"message\": \"not permission\"}"}]}, {"name": "deactivate game service", "event": [{"listen": "test", "script": {"exec": ["tests[\"Delete service\"] = responseCode.code === 204;", ""], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}, {"key": "x-client", "value": "{{COMPANY_UUID}}", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{API_URL}}/companies/{{COMPANY_UUID}}/services/{{SERVICE_UUID}}", "host": ["{{API_URL}}"], "path": ["companies", "{{COMPANY_UUID}}", "services", "{{SERVICE_UUID}}"]}}, "response": [{"name": "200", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/companies/{{COMPANY_UUID}}/users", "host": ["{{API_URL}}"], "path": ["companies", "{{COMPANY_UUID}}", "users"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "381", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 20:44:03 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\n    \"per_page\": 10,\n    \"items\": [\n        {\n            \"company_uuid\": \"b38d85c9-ffaa-455f-b72f-475ec60bee27\",\n            \"user\": {\n                \"nickname\": null,\n                \"email\": \"<EMAIL>\",\n                \"uuid\": \"a21757cb-3469-4af0-b717-44e5bb095c0f\",\n                \"name\": \"Test Keeps\"\n            }\n        }\n    ],\n    \"total\": 1,\n    \"page\": 1\n}\n"}, {"name": "400", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "url": {"raw": "{{API_URL}}/companies/kk/users", "host": ["{{API_URL}}"], "path": ["companies", "kk", "users"]}}, "status": "BAD REQUEST", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Content-Length", "value": "29", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Thu, 24 May 2018 20:50:14 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.12.2 Python/3.5.2", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\"message\": \"not permission\"}"}]}]}]}