up_env:
	docker-compose up -d

up_env_build:
	docker-compose up -d --build

down_env:
	docker-compose rm --stop --force

logs_env:
	docker-compose logs -f

run_server:
	python -m application

all: clean create-venv setup-dev test-cov

default_target: clean ruff-check test-cov

test-cov:
	mkdir -p .reports
	export SUSPEND_SIGNALS=True
	export PYTHONPATH=""$(shell dirname $(realpath $(firstword $(MAKEFILE_LIST))))/myaccount
	coverage run -m pytest myaccount
	coverage report -m
	coverage xml
	coverage html
	export ENVIRONMENT_TEST=false
	export SUSPEND_SIGNALS=False


ruff-check:
	pip install ruff==0.1.6
	ruff check . --fix
	ruff format .
	mkdir -p .reports
	ruff check . --output-format=pylint --output-file=.reports/output_flake.txt


PROTO_DIR=myaccount/grpc_services/protos
PYTHON_OUT_DIR=myaccount/grpc_services/generated
PROTO_FILES=$(wildcard $(PROTO_DIR)/*.proto)
export PYTHONPATH=""$(shell dirname $(realpath $(firstword $(MAKEFILE_LIST))))/myaccount; \

build_proto:
	@for proto_file in $(PROTO_FILES); do \
		python -m grpc_tools.protoc -I$(PROTO_DIR) --python_out=$(PYTHON_OUT_DIR) --grpc_python_out=$(PYTHON_OUT_DIR) $$proto_file; \
		# Fix imports in generated files to be relative \
		GENERATED_PY_FILE=$$(echo $$proto_file | sed 's/\.proto/_pb2.py/' | sed 's|$(PROTO_DIR)|$(PYTHON_OUT_DIR)|'); \
		sed -i 's/^import \([^ ]*\)_pb2/from . import \1_pb2/' $$GENERATED_PY_FILE; \
		GENERATED_GRPC_PY_FILE=$$(echo $$proto_file | sed 's/\.proto/_pb2_grpc.py/' | sed 's|$(PROTO_DIR)|$(PYTHON_OUT_DIR)|'); \
		sed -i 's/^import \([^ ]*\)_pb2/from . import \1_pb2/' $$GENERATED_GRPC_PY_FILE; \
	done

clean_proto:
	find $(PYTHON_OUT_DIR) -type f -name "*.py" -delete
