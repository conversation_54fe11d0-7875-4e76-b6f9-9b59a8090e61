  # See https://pre-commit.com for more information
  default_language_version:
  # default language version for each language used in the repository
    python: python3.8
  repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.3.0
    hooks:
      # See https://pre-commit.com/hooks.html for more hooks
      - id: check-ast
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: debug-statements
      - id: end-of-file-fixer
      - id: name-tests-test
        args: [ "--django" ]
      - id: trailing-whitespace
  - repo: https://github.com/asottile/seed-isort-config
    rev: v2.2.0
    hooks:
      - id: seed-isort-config
  - repo: https://github.com/pycqa/isort
    rev: 5.10.1
    hooks:
      - id: isort
        args: ["--profile", "black"]
  - repo: https://github.com/psf/black
    rev: 22.6.0
    hooks:
      - id: black
