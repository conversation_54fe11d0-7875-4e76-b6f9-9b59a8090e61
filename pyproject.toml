[tool.black]
line-length = 120
include = '\.pyi?$'
exclude = '''
/(
    \.git
  | \.hg
  | \.mypy_cache
  | migrations
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | \.reports
)/
'''

[tool.ruff]
line-length = 120
lint.select = [
    # Pyflakes
    "F",
    # Pycodestyle
    "E",
    "W",
    # isort
    "I001"
]
lint.ignore = ["F811", "E402", "E722", "E501", "E203", "FIX001", "B009"]
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".github",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
    "migrations",
    "email_locale",
    "__init__.py",
    "tests",
    "alembic",
    "config",
    "locales",
    "manage.py",
    "apps.py"
]