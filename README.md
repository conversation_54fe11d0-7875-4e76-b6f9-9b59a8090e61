# My Account
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)
![badge](https://img.shields.io/endpoint?url=https://gist.githubusercontent.com/tech-keeps/628e300bc423529c4779605f41c040e4/raw/keeps-my_account-server.json)


## Kafka Worker
### Descrição da arquitetura
#### Consumer:
Arquivo inicial da aplicação do worker que será executado pelo supervisor. Será responsável pela injeção das depedências e inicializar o worker (UserIntegrationWorker). A configuração da execução no arquivo supervisord.conf

#### KeepsMessage:
Representação da mensagem do kafka em um objeto

#### UserIntegrationWorker:
Serviço que irá consumir as mensagens do topico e repessar o processamento para o UserIntegrationProcessor

#### UserIntegrationProcessor:
Serviço que irá processar cada mensagem, cadastrando ou atualizando o usuário na workspace utilizando um serviço interno da aplicação. Será validado os dados da mensagem. Mensagens repetidas não serão processadas.

#### UserIntegrationMessageHash:
Entidade para salvar os hashs das mensagens processadas com sucesso


## Configurações básicas para rodar o projeto


#### Primerio é necessário configurar as variáveis de ambiente sendo:

Básicas:
```
ENVIRONMENT
DATABASE_HOST
DATABASE_NAME
DATABASE_PASSWORD
DATABASE_PORT
DATABASE_USER
AWS_BUCKET_NAME
```

Configurações do Keycloak:
```
IAM_ADMIN_PASS_ADMIN
IAM_ADMIN_REALM
IAM_ADMIN_SERVER_URL
IAM_ADMIN_USER_ADMIN
IAM_OPENID_CLIENT_ID
IAM_OPENID_PUBLIC_KEY
IAM_OPENID_REALM
IAM_OPENID_SECRET_KEY
IAM_OPENID_SERVER_URL
IAM_OPENID_TOKEN_URL
KEYCLOAK_CLIENT_ID
KEYCLOAK_CLIENT_PUBLIC_KEY
KEYCLOAK_CLIENT_SECRET_KEY
KEYCLOAK_REALM
```

Configurações do Celery:
```
BROKER_URL
CELERY_BROKER_URL
CELERY_BROKER_URL
CELERY_RESULT_BACKEND
CELERY_RESULT_SERIALIZER
CELERY_TASK_SERIALIZER
CELERY_TIMEZONE
```

Integração Keeps:
```
KIZUP_API_URL
KONQUEST_API_URL
```

Serviços:
```
WHATSAPP_BROKER
MESSAGEBIRD_ACCESS_KEY
MESSAGEBIRD_CHANNEL_ID
MESSAGEBIRD_NAMESPACE
DEBOUNCE_API_URL
DEBOUNCE_API_KEY
SLACK_LOG_CHANNEL_WEBHOOK
```

### Configuração usadas pelo Kafka Worker

#### env WORKSPACE_USER_TOPICS
representação string de um lista de dicionários contendo o nome do topico e a empresa do mesmo, exemplo:
```[{"workspace_id": "e76b5082-f4fe-4f41-be79-1977840e16a8", "topic_key": "users_by_csv_file_keeps_"}]```

#### Configurando o Banco de Dados

Apos definir as variáveis de ambiente, é necessário rodar as migrações:

```
python manage.py migrate
```

Agora basta carregar os dados básicos necessários:
```
./loaddata.sh
```

## Rodando o projeto em desenvolvimento usando Docker
Esse projeto está configurado para rodar utilizando Docker. Você encontrará três aquivos com
as configurações necessárias para rodar o projeto com o Docker Compose.

- dc_dev.yml
- dc_stage.yml
- dc_production.yml

#### dc_dev.yml
Irá rodar, compilando o projeto atual (docker build) e configurando o ambiente com uma aplicação
WSGI (porta 8000) e Celery. Além das aplicações, irá subir um conteiner com Postgres que será exposto
na porta 15432 (para não dar conflito caso já tenha um postgres rodando na porta 5432)

Você deverá primeiramente configurar um arquivo ".env.dev" com todas as variáveis de ambiente
citadas acima. *Solicite ao seu team leader um arquivo já configurado.*

Para rodar o docker compose utilize o seguinte comando.
```
 docker-compose -f dc_dev.yml up -d --build
```

#### dc_stage.yml
Caso precise rodar o container apontando para as configurações em ambiente de stage, você poderá
usar esse arquivo. Esse compose irá executar a imagem com a tag "stage" hospedada no AWS/ECR da Keeps.

Para rodar esse ambiente, será necessário configurar o arquivo ".env" com as variáveis de ambiente da stage.

```
 docker-compose -f dc_stage.yml up -d --build
```

#### dc_production.yml
Arquivo para deploy em produção. Não utilizar em ambientes de desenvolvimento.

## UPDATE:
Os arquivos dc_dev.yml, dc_stage.yml e dc_production.yml não estão mais disponíveis no repositório.
Você poderá rodar utilizando o _Docker Compose_ conforme os passos abaixo:

1. Instale o [docker-compose](https://docs.docker.com/compose/install/) caso ainda não o tenha.
2. Baixe o projeto do repositório: ```<NAME_EMAIL>:Keeps-Learn/keeps-myaccount-server.git```<br>
Para clonar o projeto localmente, você precisa adicionar uma chave SSH ao seu perfil no Github.
Caso ainda não tenha feito isso, siga os passos descritos em [Adding a new SSH key to your GitHub account](https://docs.github.com/en/authentication/connecting-to-github-with-ssh/adding-a-new-ssh-key-to-your-github-account)

3. Acesse a pasta ```keeps-myaccount-server```
4. Crie um arquivo **.env** contendo as variáveis de ambiente.
*Solicite ao seu team leader um arquivo já configurado.*
5. Execute o comando ```docker-compose up --build```

Você deverá obter no console os logs gerados pelos containers.<br>
O container _worker_ poderá exibir um erro semelhante ao abaixo:
```
worker-1    |   File "/app/kafka_worker/user_integration_worker.py", line 44, in start
worker-1    |     self.consumer.subscribe(self._topics, on_assign=self._on_assign_callback)
worker-1    | cimpl.KafkaException: KafkaError{code=_INVALID_ARG,val=-186,str="Failed to set subscription: Local: Invalid argument or configuration"}
```

Desconsidere esse erro inicialmente.<br>
Para acessar a documentação da API, acesse o seguinte endereço no navegador: ```http://localhost:8000```
