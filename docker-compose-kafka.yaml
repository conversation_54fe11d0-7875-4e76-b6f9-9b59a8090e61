version: '3.8'

services:
  web:
    build:
      context: ./myaccount
      args:
        GITHUB_TOKEN: ${GITHUB_TOKEN}
    command: python manage.py runserver 0.0.0.0:8000
    volumes:
      - ./myaccount/:/usr/src/app/
    ports:
      - 8000:8000
    networks:
      - default
    env_file:
      - ./.env
    depends_on:
      - db
  worker:
    build:
      context: ./myaccount
      args:
        GITHUB_TOKEN: ${GITHUB_TOKEN}
    command: /usr/bin/supervisord -c /etc/supervisord.conf
    volumes:
      - ./myaccount/:/usr/src/app/
    env_file:
      - ./.env
    networks:
      - kafka-connectors_net
      - default
    depends_on:
      - db
      - rabbitmq
    environment:
      - PYTHONPATH=/usr/src/app/
  db:
    image: postgres:13.0-alpine
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=myaccount_dev_db
    ports:
      - 15432:5432
  rabbitmq:
    image: rabbitmq:3-management-alpine
    ports:
        - 5672:5672
        - 15672:15672
    networks:
      - default
    environment:
        - RABBITMQ_DEFAULT_USER=admin
        - RABBITMQ_DEFAULT_PASS=admin
volumes:
  postgres_data:

networks:
  kafka-connectors_net:
    external: true
  default:
    driver: bridge
